# API Documentation - E-commerce Backend

## Overview
Backend API cho hệ thống thương mại điện tử được xây dựng bằng Moleculer microservices framework.

## Base URL
```
http://localhost:3000/api
```

## Authentication
Hầu hết các API yêu cầu authentication. Sử dụng Bearer token trong header:
```
Authorization: Bearer <access_token>
```

## Response Format
Tất cả response đều có format JSON:
```json
{
  "success": true,
  "data": {},
  "message": "Success message"
}
```

Khi có lỗi:
```json
{
  "success": false,
  "message": "Error message",
  "code": 400,
  "data": {}
}
```

---

## Users API

### POST /users/register
Đăng ký tài khoản mới

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "<PERSON>uy<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đăng ký thành công. Vui lòng kiểm tra email để kích hoạt tài khoản.",
  "user": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "fullName": "Nguyen Van A"
  }
}
```

### POST /users/login
Đăng nhập

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "_id": "user_id",
  "email": "<EMAIL>",
  "fullName": "Nguyen Van A"
}
```

### POST /users/forgot-password
Quên mật khẩu

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### POST /users/reset-password
Đặt lại mật khẩu

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "newpassword123"
}
```

### GET /users/profile
Lấy thông tin profile (yêu cầu auth)

### PUT /users/profile
Cập nhật profile (yêu cầu auth)

**Request Body:**
```json
{
  "fullName": "New Name",
  "phone": "0123456789",
  "dateOfBirth": "1990-01-01",
  "gender": "male"
}
```

---

## Products API

### GET /products/search
Tìm kiếm và lọc sản phẩm

**Query Parameters:**
- `q`: Từ khóa tìm kiếm
- `category`: ID danh mục
- `minPrice`: Giá tối thiểu
- `maxPrice`: Giá tối đa
- `sortBy`: Sắp xếp theo (name, price, createdAt, totalSold, averageRating)
- `sortOrder`: Thứ tự (asc, desc)
- `page`: Trang (default: 1)
- `limit`: Số lượng/trang (default: 20)
- `inStock`: Chỉ sản phẩm còn hàng
- `isFeatured`: Sản phẩm nổi bật
- `isNew`: Sản phẩm mới

**Response:**
```json
{
  "docs": [
    {
      "_id": "product_id",
      "name": "Product Name",
      "slug": "product-slug",
      "description": "Product description",
      "minPrice": 100000,
      "maxPrice": 200000,
      "totalStock": 50,
      "averageRating": 4.5,
      "totalReviews": 10
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 20,
  "pages": 5
}
```

### GET /products/:id/detail
Lấy chi tiết sản phẩm

**Response:**
```json
{
  "_id": "product_id",
  "name": "Product Name",
  "description": "Product description",
  "variants": [
    {
      "_id": "variant_id",
      "name": "Variant Name",
      "sku": "SKU001",
      "price": 150000,
      "totalStock": 20,
      "attributes": [
        {
          "name": "Color",
          "value": "Red"
        }
      ]
    }
  ]
}
```

### GET /products/bestsellers
Lấy sản phẩm bán chạy

### GET /products/featured
Lấy sản phẩm nổi bật

### GET /products/new
Lấy sản phẩm mới

### GET /products/:id/related
Lấy sản phẩm liên quan

---

## Carts API

### POST /carts/add
Thêm sản phẩm vào giỏ hàng (yêu cầu auth)

**Request Body:**
```json
{
  "variantId": "variant_id",
  "quantity": 2
}
```

### PUT /carts/:id/quantity
Cập nhật số lượng sản phẩm trong giỏ hàng (yêu cầu auth)

**Request Body:**
```json
{
  "quantity": 3
}
```

### DELETE /carts/:id
Xóa sản phẩm khỏi giỏ hàng (yêu cầu auth)

### GET /carts/my-cart
Lấy giỏ hàng của user (yêu cầu auth)

**Response:**
```json
{
  "items": [
    {
      "_id": "cart_item_id",
      "quantity": 2,
      "itemTotal": 200000,
      "variantId": {
        "name": "Variant Name",
        "price": 100000,
        "productId": {
          "name": "Product Name"
        }
      }
    }
  ],
  "summary": {
    "totalItems": 1,
    "totalQuantity": 2,
    "subtotal": 200000,
    "total": 200000
  }
}
```

### DELETE /carts/clear
Xóa toàn bộ giỏ hàng (yêu cầu auth)

### GET /carts/count
Đếm số lượng sản phẩm trong giỏ hàng (yêu cầu auth)

---

## Orders API

### POST /orders/create-from-cart
Tạo đơn hàng từ giỏ hàng (yêu cầu auth)

**Request Body:**
```json
{
  "shippingAddressId": "address_id",
  "paymentMethod": "cod",
  "shippingMethod": "standard",
  "customerNote": "Giao hàng giờ hành chính",
  "couponCode": "DISCOUNT10"
}
```

### GET /orders/my-orders
Lấy lịch sử đơn hàng (yêu cầu auth)

**Query Parameters:**
- `page`: Trang
- `limit`: Số lượng/trang
- `status`: Lọc theo trạng thái

### GET /orders/:id/detail
Lấy chi tiết đơn hàng (yêu cầu auth)

### PUT /orders/:id/cancel
Hủy đơn hàng (yêu cầu auth)

**Request Body:**
```json
{
  "reason": "Lý do hủy đơn hàng"
}
```

### GET /orders/:id/tracking
Tracking đơn hàng (yêu cầu auth)

**Response:**
```json
{
  "orderId": "order_id",
  "orderNumber": "ORD20241201001",
  "status": "shipped",
  "trackingNumber": "TRK123456",
  "timeline": [
    {
      "status": "pending",
      "title": "Đơn hàng được tạo",
      "timestamp": "2024-01-01T00:00:00Z",
      "completed": true
    }
  ]
}
```

---

## Addresses API

### GET /addresses/my-addresses
Lấy danh sách địa chỉ (yêu cầu auth)

### POST /addresses
Tạo địa chỉ mới (yêu cầu auth)

**Request Body:**
```json
{
  "fullName": "Nguyen Van A",
  "phone": "0123456789",
  "address": "123 Nguyen Trai",
  "ward": "Phuong 1",
  "province": "Ho Chi Minh",
  "type": "home",
  "isDefault": true
}
```

### PUT /addresses/:id
Cập nhật địa chỉ (yêu cầu auth)

### DELETE /addresses/:id
Xóa địa chỉ (yêu cầu auth)

### PUT /addresses/:id/set-default
Đặt địa chỉ mặc định (yêu cầu auth)

### GET /addresses/default
Lấy địa chỉ mặc định (yêu cầu auth)

---

## Payments API

### POST /payments/create
Tạo thanh toán cho đơn hàng (yêu cầu auth)

**Request Body:**
```json
{
  "orderId": "order_id",
  "paymentMethod": "vnpay",
  "returnUrl": "http://frontend.com/payment/return"
}
```

**Response:**
```json
{
  "transactionId": "transaction_id",
  "paymentMethod": "vnpay",
  "amount": 200000,
  "status": "pending",
  "paymentUrl": "https://sandbox.vnpayment.vn/...",
  "expireDate": "2024-01-01T01:00:00Z"
}
```

### POST /payments/:transactionId/confirm
Xác nhận thanh toán (Admin only)

### POST /payments/:transactionId/cancel
Hủy thanh toán (yêu cầu auth)

### GET /payments/my-transactions
Lịch sử giao dịch (yêu cầu auth)

---

## Error Codes

- `400`: Bad Request - Dữ liệu không hợp lệ
- `401`: Unauthorized - Chưa đăng nhập
- `403`: Forbidden - Không có quyền truy cập
- `404`: Not Found - Không tìm thấy tài nguyên
- `500`: Internal Server Error - Lỗi server

## Rate Limiting
API có giới hạn 1000 requests/hour cho mỗi IP address.

## Pagination
Các API trả về danh sách đều hỗ trợ pagination với format:
```json
{
  "docs": [],
  "total": 100,
  "page": 1,
  "limit": 20,
  "pages": 5
}
```
