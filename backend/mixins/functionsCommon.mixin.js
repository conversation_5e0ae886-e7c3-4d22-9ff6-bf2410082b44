const {camelCase, snakeCase} = require('lodash');
const slugify = require('slugify')

module.exports = {
  methods: {
    groupBy(listData, key) {
      return listData.reduce(function(grouped, element) {
        (grouped[element[key]] = grouped[element[key]] || []).push(element);
        return grouped;
      }, {});
    },
    extractQueryTime(params) {
      const {time, fromDate, toDate} = params;
      let createdAtQuery = {};

      switch (time) {
        case 'month':
          const {firstDay, lastDay} = this.getMonthRange();
          createdAtQuery = {$gte: firstDay, $lte: lastDay};
          break;
        case 'week':
          const {firstDay: weekFirstDay, lastDay: weekLastDay} = this.getWeekRange();
          createdAtQuery = {$gte: weekFirstDay, $lte: weekLastDay};
          break;
        case 'custom':
          createdAtQuery = {
            $gte: fromDate ? new Date(fromDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            $lte: toDate ? new Date(toDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999),
          };
          break;
        default:
          const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
          const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999);
          createdAtQuery = {$gte: startOfMonth, $lte: endOfMonth};
          break;
      }

      return {createdAt: createdAtQuery};
    },
    extractParamsList(params) {
      const {page, limit, sort} = params;
      return {
        page: +page || 1,
        pageSize: +limit || 12,
        sort: sort || 'createdAt',
      };
    },
    getMonthRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      return {firstDay, lastDay};
    },
    getWeekRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
      const lastDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay() + 7);
      return {firstDay, lastDay};
    },
    convertSnakeCaseToCamelCase(dataInput) {
      if (typeof dataInput === 'object') {
        if (Array.isArray(dataInput)) {
          let objOutput = [];
          dataInput.forEach(item => {
            objOutput = [...objOutput, this.convertSnakeCaseToCamelCase(item)];
          });
          return objOutput;
        } else {
          return this.convertObjectToCamelCase(dataInput);
        }
      }
      return dataInput;
    },
    convertObjectToCamelCase(objInput) {
      if (!objInput) return objInput;
      const objOutput = {};
      Object.entries(objInput).forEach(([key, value]) => {
        if (key === 'extra') {
          objOutput[key] = value;
        } else {
          if (typeof value === 'object') {
            if (Array.isArray(value)) {
              // array
              objOutput[camelCase(key)] = this.convertSnakeCaseToCamelCase(value);
            } else {
              // object
              objOutput[camelCase(key)] = this.convertObjectToCamelCase(value);
            }
          } else {
            if (key === '_id') {
              objOutput._id = value;
              objOutput.key = value;
            } else {
              objOutput[camelCase(key)] = value;
            }
          }
        }
      });
      return objOutput;
    },
    generateSlug (name) {
      return slugify(name, {
        replacement: '-',  // replace spaces with replacement character, defaults to -
        remove: undefined, // remove characters that match regex, defaults to undefined
        lower: true,      // convert to lower case, defaults to false
        strict: false,     // strip special characters except replacement, defaults to false
        locale: 'vi',      // language code of the locale to use
        trim: true         // trim leading and trailing replacement chars, defaults to true
      })
    },
    createDataTree (data, parentId = null) {
      const tree = []
      data.forEach(item => {
        if (String(item.parentId) === String(parentId)) {
          const children = this.createDataTree(data, item._id)
          const dataObj = {
            ...item.toObject(),
            children: children.length > 0 ? children : []
          }
          tree.push(dataObj)
        }
      })

      return tree
    },
  },
};
