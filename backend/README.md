# E-commerce Backend API

Backend API cho hệ thống thương mại điện tử được xây dựng bằng Moleculer microservices framework với MongoDB.

## 🚀 Tính năng chính

### ✅ Đã triển khai hoàn chỉnh:

#### 🛒 **Quản lý giỏ hàng**
- Thêm/xóa/cập nhật sản phẩm trong giỏ hàng
- Tính toán tổng tiền tự động
- Validation stock trước khi thêm vào giỏ
- Sync với inventory khi stock thay đổi

#### 📦 **Quản lý đơn hàng**
- Tạo đơn hàng từ giỏ hàng
- Quản lý trạng thái đơn hàng (pending → confirmed → processing → shipped → delivered)
- Hủy đơn hàng với release inventory
- Tracking đơn hàng với timeline
- Tự động generate order number

#### 📍 **Quản lý địa chỉ**
- CRUD địa chỉ giao hàng
- Đặt địa chỉ mặc định
- Validation địa chỉ cho checkout

#### 🛍️ **Quản lý sản phẩm nâng cao**
- Tìm kiếm và lọc sản phẩm (theo giá, danh mục, stock)
- Sắp xếp theo nhiều tiêu chí (giá, tên, ngày tạo, số lượng bán)
- Sản phẩm bestseller, featured, mới
- Sản phẩm liên quan
- Quản lý variants với attributes
- Rating và reviews

#### 💳 **Hệ thống thanh toán**
- Hỗ trợ nhiều phương thức: COD, VNPay, VNPTPay, Bank Transfer
- Webhook xử lý kết quả thanh toán
- Transaction history
- Refund processing
- Payment status tracking

#### 👤 **Authentication & Authorization**
- Đăng ký/đăng nhập với JWT
- Forgot password & reset password
- Email activation
- Refresh token mechanism
- Role-based access control (User/Admin)
- Profile management

#### 📊 **Quản lý kho**
- Inventory tracking theo variant
- Stock reservation khi tạo đơn hàng
- Low stock alerts
- Stock movement logs

## 🏗️ Kiến trúc

### Microservices
- **api.service.js**: API Gateway với authentication/authorization
- **users**: Quản lý người dùng, authentication
- **products**: Quản lý sản phẩm, variants, categories
- **carts**: Quản lý giỏ hàng
- **orders**: Quản lý đơn hàng
- **addresses**: Quản lý địa chỉ giao hàng
- **payments**: Xử lý thanh toán
- **files**: Quản lý file/hình ảnh
- **notifications**: Thông báo

### Database Schema
- **MongoDB** với Mongoose ODM
- **Pagination** với mongoose-paginate-v2
- **Indexing** tối ưu cho performance
- **Soft delete** cho data integrity

## 🛠️ Cài đặt

### Prerequisites
- Node.js >= 16.x
- MongoDB >= 4.4
- Yarn hoặc npm

### Installation
```bash
# Clone repository
git clone <repository-url>
cd backend

# Install dependencies
yarn install

# Copy environment variables
cp .env.example .env

# Start development server
yarn dev
```

### Environment Variables
```env
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ecommerce
SECRET=your-jwt-secret
RESET_PASSWORD_SECRET=your-reset-secret

# VNPay Configuration
VNPAY_TMN=your-vnpay-tmn
VNPAY_HASH=your-vnpay-hash
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Frontend URL
FRONTEND_URL=http://localhost:8080
```

## 📚 API Documentation

Xem chi tiết tại [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

### Base URL
```
http://localhost:3000/api
```

### Authentication
```bash
# Login
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use token in subsequent requests
curl -X GET http://localhost:3000/api/users/profile \
  -H "Authorization: Bearer <access_token>"
```

## 🧪 Testing

```bash
# Run tests
yarn test

# Run tests with coverage
yarn test:coverage

# Run specific test file
yarn test carts.test.js
```

### Test Coverage
- ✅ Carts Service: Add to cart, update quantity, validate cart
- ✅ Orders Service: Create order, cancel order, order calculations
- ✅ Users Service: Authentication, profile management
- ✅ Products Service: Search, filtering, related products

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t ecommerce-backend .

# Run container
docker run -p 3000:3000 -e MONGODB_URI=mongodb://host:27017/ecommerce ecommerce-backend
```

### Production Checklist
- [ ] Set production environment variables
- [ ] Configure MongoDB replica set
- [ ] Set up Redis for caching
- [ ] Configure load balancer
- [ ] Set up monitoring (Prometheus/Grafana)
- [ ] Configure logging (ELK stack)
- [ ] Set up backup strategy

## 📈 Performance

### Optimizations
- **Database Indexing**: Tối ưu queries với indexes
- **Pagination**: Giới hạn kết quả trả về
- **Caching**: Cache user sessions và product data
- **Aggregation**: Sử dụng MongoDB aggregation cho complex queries

### Monitoring
- Request/response times
- Database query performance
- Memory usage
- Error rates

## 🔒 Security

### Implemented
- JWT authentication với refresh tokens
- Password hashing với bcrypt
- Input validation với Moleculer validator
- Rate limiting
- CORS configuration
- SQL injection prevention (NoSQL)

### Best Practices
- Không expose sensitive data trong responses
- Validate tất cả input data
- Use HTTPS trong production
- Regular security updates

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Style
- ESLint configuration
- Prettier formatting
- Conventional commits

## 📝 Changelog

### v1.0.0 (2024-12-01)
- ✅ Complete cart management
- ✅ Order processing workflow
- ✅ Address management
- ✅ Payment integration (VNPay, COD)
- ✅ Advanced product search & filtering
- ✅ User authentication & authorization
- ✅ Inventory management
- ✅ API documentation
- ✅ Unit tests

## 📞 Support

Nếu có vấn đề hoặc câu hỏi, vui lòng tạo issue trên GitHub repository.

## 📄 License

MIT License - xem [LICENSE](LICENSE) file để biết thêm chi tiết.
