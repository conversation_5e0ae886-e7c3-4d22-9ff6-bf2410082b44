exports.SETTING = 'Setting';
exports.NOTIFICATION = 'Notification';

exports.USER = 'User';
exports.REFRESH_TOKEN = 'RefreshToken';

exports.FILE = 'File';
exports.IMAGE = 'Image';
exports.VIDEO = 'Video';
exports.ERROR = 'Error';

// Product management
exports.PRODUCT = 'Product';
exports.CATEGORY = 'Category';
exports.VARIANT = 'Variant';
exports.PRODUCT_VARIANT = 'Variant'; // Alias for backward compatibility
exports.REVIEW = 'Review';
exports.WAREHOUSE = 'Warehouse';
exports.STOCK_LOG = 'StockLog';

// Order management
exports.ORDER = 'Order';
exports.ORDER_ITEM = 'OrderItem';
exports.CART = 'Cart';

// Inventory management
exports.INVENTORY = 'Inventory';

// Address management
exports.ADDRESS = 'Address';

// Shipping
exports.SHIPPING_METHOD = 'ShippingMethod';
exports.SHIPPING_RATE = 'ShippingRate';

// Payment methods
exports.TRANSACTION = 'Transaction';
exports.PAY_LOG = 'PayLog';
exports.PAY_TRANSACTION = 'PayTransaction';
