exports.DOMAIN = {
  OPHIM: 'ophim',
  NGUONC: 'nguonc',
  KKPHIM: 'kkphim',
};

exports.STATUS = {
  ON_GOING: 'ongoing',
  COMPLETED: 'completed',
  TRAILER: 'trailer',
};

exports.MOVIE_TYPES = {
  PHIM_BO: 'Phim bộ',
  PHIM_LE: 'Phim lẻ',
  HOAT_HINH: 'Hoạt hình',
};

exports.OUTPUT_TYPE = {
  MARK_TEST_WRITING: 'mark_test_writing',
  MARK_TEST_IELTS_WRITING: 'mark_test_ielts_writing',
  TEXT: 'text',
  ESSAY_TOPICS: 'essay_topics',
  ADVANTAGES_AND_DISADVANTAGES: 'advantages_and_disadvantages',
  WRITING_TASK: 'writing_task',
  SCRAMBLE_WORDS: 'scramble_words',
  WORDS: 'words',
  MATCHING_WORDS: 'matching_words',
  DIALOGUES: 'dialogues',
  FILL_GAPS: 'fill_gaps',
  MULTI_CHOICE: 'multi_choice',
  OPEN_QUESTION: 'open_question',
  TF_QUESTION: 'tf_question',
  HTML: 'html',
  OPTIONS: 'options',
  HTML_QUESTIONS: 'html_questions',
  AUDIO: 'audio',
  PRON_FEEDBACK: 'pron_feedback',
};

exports.INPUT_TYPE = {
  TEXT: 'text',
  HTML: 'html',
  HTML_TRIM_NBSP: 'html_trim_nbsp',
  VIDEO: 'video',
  OFFLINE_VIDEO: 'offline_video',
  TOPIC: 'topic',
  AUDIO: 'audio',
  AUDIO_STREAM: 'audio_stream',
  IMAGE: 'image',
  FILE: 'file',
  MARK_TEST: 'mark_test',
  MARK_TEST_TASK_1: 'mark_test_task_1',
  MARK_TEST_IMAGE: 'mark_test_image',
  MARK_TEST_TASK_2: 'mark_test_task_2',
  TTS: 'text_to_speech',
  NONE: 'none',
  STUDENT_TASK_1: 'student_task_1',
  STUDENT_TASK_2: 'student_task_2',
  STUDENT_SPEAKING: 'student_speaking',
};