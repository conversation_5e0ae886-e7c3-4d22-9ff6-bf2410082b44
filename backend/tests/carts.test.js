const { ServiceBroker } = require('moleculer');
const CartsService = require('../services/carts/carts.service');

describe('Carts Service', () => {
  let broker;
  let service;

  beforeAll(async () => {
    broker = new ServiceBroker({ logger: false });
    service = broker.createService(CartsService);
    await broker.start();
  });

  afterAll(async () => {
    await broker.stop();
  });

  describe('addToCart', () => {
    it('should add product to cart', async () => {
      const mockUser = { _id: 'user123' };
      const mockVariant = { 
        _id: 'variant123', 
        totalStock: 10, 
        price: 100000 
      };

      // Mock dependencies
      broker.call = jest.fn()
        .mockResolvedValueOnce(mockVariant); // variants.get

      service.adapter.findOne = jest.fn().mockResolvedValue(null);
      service.adapter.insert = jest.fn().mockResolvedValue({
        _id: 'cart123',
        userId: 'user123',
        variantId: 'variant123',
        quantity: 1
      });

      const result = await broker.call('carts.addToCart', {
        variantId: 'variant123',
        quantity: 1
      }, { meta: { user: mockUser } });

      expect(result).toBeDefined();
      expect(service.adapter.insert).toHaveBeenCalledWith({
        userId: 'user123',
        variantId: 'variant123',
        quantity: 1
      });
    });

    it('should update quantity if product already in cart', async () => {
      const mockUser = { _id: 'user123' };
      const mockVariant = { 
        _id: 'variant123', 
        totalStock: 10, 
        price: 100000 
      };
      const existingCartItem = {
        _id: 'cart123',
        userId: 'user123',
        variantId: 'variant123',
        quantity: 2
      };

      broker.call = jest.fn()
        .mockResolvedValueOnce(mockVariant); // variants.get

      service.adapter.findOne = jest.fn().mockResolvedValue(existingCartItem);
      service.adapter.updateById = jest.fn().mockResolvedValue({
        ...existingCartItem,
        quantity: 3
      });

      const result = await broker.call('carts.addToCart', {
        variantId: 'variant123',
        quantity: 1
      }, { meta: { user: mockUser } });

      expect(service.adapter.updateById).toHaveBeenCalledWith('cart123', {
        quantity: 3
      });
    });

    it('should throw error if not enough stock', async () => {
      const mockUser = { _id: 'user123' };
      const mockVariant = { 
        _id: 'variant123', 
        totalStock: 1, 
        price: 100000 
      };

      broker.call = jest.fn()
        .mockResolvedValueOnce(mockVariant); // variants.get

      await expect(
        broker.call('carts.addToCart', {
          variantId: 'variant123',
          quantity: 5
        }, { meta: { user: mockUser } })
      ).rejects.toThrow('Không đủ hàng trong kho');
    });
  });

  describe('getMyCart', () => {
    it('should return cart with calculated totals', async () => {
      const mockUser = { _id: 'user123' };
      const mockCartItems = [
        {
          _id: 'cart1',
          quantity: 2,
          variantId: {
            _id: 'variant1',
            price: 100000,
            productId: { name: 'Product 1' }
          }
        },
        {
          _id: 'cart2',
          quantity: 1,
          variantId: {
            _id: 'variant2',
            price: 200000,
            productId: { name: 'Product 2' }
          }
        }
      ];

      service.adapter.find = jest.fn().mockResolvedValue(mockCartItems);

      const result = await broker.call('carts.getMyCart', {}, { 
        meta: { user: mockUser } 
      });

      expect(result.summary.totalItems).toBe(2);
      expect(result.summary.totalQuantity).toBe(3);
      expect(result.summary.subtotal).toBe(400000);
      expect(result.items[0].itemTotal).toBe(200000);
      expect(result.items[1].itemTotal).toBe(200000);
    });
  });

  describe('validateCart', () => {
    it('should validate cart successfully', async () => {
      const mockCartItems = [
        {
          _id: 'cart1',
          quantity: 2,
          variantId: {
            _id: 'variant1',
            price: 100000,
            totalStock: 10,
            name: 'Variant 1'
          }
        }
      ];

      service.adapter.find = jest.fn().mockResolvedValue(mockCartItems);

      const result = await broker.call('carts.validateCart', {
        userId: 'user123'
      });

      expect(result.valid).toBe(true);
      expect(result.totalAmount).toBe(200000);
    });

    it('should throw error if cart is empty', async () => {
      service.adapter.find = jest.fn().mockResolvedValue([]);

      await expect(
        broker.call('carts.validateCart', { userId: 'user123' })
      ).rejects.toThrow('Giỏ hàng trống');
    });

    it('should throw error if insufficient stock', async () => {
      const mockCartItems = [
        {
          _id: 'cart1',
          quantity: 5,
          variantId: {
            _id: 'variant1',
            price: 100000,
            totalStock: 2,
            name: 'Variant 1'
          }
        }
      ];

      service.adapter.find = jest.fn().mockResolvedValue(mockCartItems);

      await expect(
        broker.call('carts.validateCart', { userId: 'user123' })
      ).rejects.toThrow('Giỏ hàng có lỗi');
    });
  });
});
