const { ServiceBroker } = require('moleculer');
const OrdersService = require('../services/orders/orders.service');

describe('Orders Service', () => {
  let broker;
  let service;

  beforeAll(async () => {
    broker = new ServiceBroker({ logger: false });
    service = broker.createService(OrdersService);
    await broker.start();
  });

  afterAll(async () => {
    await broker.stop();
  });

  describe('createFromCart', () => {
    it('should create order from cart successfully', async () => {
      const mockUser = { _id: 'user123' };
      const mockCartValidation = {
        valid: true,
        items: [
          {
            _id: 'cart1',
            quantity: 2,
            variantId: {
              _id: 'variant1',
              price: 100000,
              sku: 'SKU001',
              name: 'Variant 1',
              productId: {
                _id: 'product1',
                name: 'Product 1'
              }
            }
          }
        ],
        totalAmount: 200000
      };

      const mockOrder = {
        _id: 'order123',
        orderNumber: 'ORD20241201001',
        userId: 'user123',
        total: 200000,
        status: 'pending'
      };

      // Mock dependencies
      broker.call = jest.fn()
        .mockResolvedValueOnce(mockCartValidation) // carts.validateCart
        .mockResolvedValueOnce({ valid: true }) // addresses.validateAddress
        .mockResolvedValueOnce([]) // order-items.insertMany
        .mockResolvedValueOnce(true) // carts.clearCart
        .mockResolvedValueOnce(true); // inventories.reserveStock

      service.calculateOrderTotals = jest.fn().mockResolvedValue({
        subtotal: 200000,
        shippingFee: 0,
        tax: 0,
        discount: 0,
        total: 200000
      });

      service.generateOrderNumber = jest.fn().mockResolvedValue('ORD20241201001');
      service.adapter.insert = jest.fn().mockResolvedValue(mockOrder);
      service.getOrderWithItems = jest.fn().mockResolvedValue({
        ...mockOrder,
        items: mockCartValidation.items
      });

      const result = await broker.call('orders.createFromCart', {
        shippingAddressId: 'address123',
        paymentMethod: 'cod'
      }, { meta: { user: mockUser } });

      expect(result).toBeDefined();
      expect(result.orderNumber).toBe('ORD20241201001');
      expect(service.adapter.insert).toHaveBeenCalled();
    });

    it('should throw error if cart is invalid', async () => {
      const mockUser = { _id: 'user123' };

      broker.call = jest.fn()
        .mockRejectedValueOnce(new Error('Giỏ hàng không hợp lệ'));

      await expect(
        broker.call('orders.createFromCart', {
          shippingAddressId: 'address123',
          paymentMethod: 'cod'
        }, { meta: { user: mockUser } })
      ).rejects.toThrow('Giỏ hàng không hợp lệ');
    });
  });

  describe('cancelOrder', () => {
    it('should cancel order successfully', async () => {
      const mockUser = { _id: 'user123' };
      const mockOrder = {
        _id: 'order123',
        userId: 'user123',
        status: 'pending',
        total: 200000
      };

      service.adapter.findOne = jest.fn().mockResolvedValue(mockOrder);
      service.adapter.updateById = jest.fn().mockResolvedValue({
        ...mockOrder,
        status: 'cancelled',
        cancelledAt: new Date()
      });

      broker.call = jest.fn()
        .mockResolvedValueOnce([{ variantId: 'variant1', quantity: 2 }]) // order-items.find
        .mockResolvedValueOnce(true); // inventories.releaseReservedStock

      const result = await broker.call('orders.cancelOrder', {
        id: 'order123',
        reason: 'Customer request'
      }, { meta: { user: mockUser } });

      expect(result.status).toBe('cancelled');
      expect(service.adapter.updateById).toHaveBeenCalledWith('order123', {
        status: 'cancelled',
        cancelledAt: expect.any(Date),
        adminNote: 'Khách hàng hủy: Customer request'
      });
    });

    it('should throw error if order cannot be cancelled', async () => {
      const mockUser = { _id: 'user123' };
      const mockOrder = {
        _id: 'order123',
        userId: 'user123',
        status: 'delivered',
        total: 200000
      };

      service.adapter.findOne = jest.fn().mockResolvedValue(mockOrder);

      await expect(
        broker.call('orders.cancelOrder', {
          id: 'order123'
        }, { meta: { user: mockUser } })
      ).rejects.toThrow('Không thể hủy đơn hàng ở trạng thái này');
    });
  });

  describe('generateOrderNumber', () => {
    it('should generate unique order number', async () => {
      service.adapter.count = jest.fn().mockResolvedValue(5);

      const orderNumber = await service.generateOrderNumber();

      expect(orderNumber).toMatch(/^ORD\d{8}\d{4}$/);
      expect(orderNumber.length).toBe(15);
    });
  });

  describe('calculateOrderTotals', () => {
    it('should calculate order totals correctly', async () => {
      const mockCartItems = [
        {
          variantId: { price: 100000 },
          quantity: 2
        },
        {
          variantId: { price: 50000 },
          quantity: 1
        }
      ];

      broker.call = jest.fn()
        .mockResolvedValueOnce({ fee: 30000 }) // shipping.calculateFee
        .mockResolvedValueOnce({ discount: 20000 }); // discounts.applyCoupon

      const result = await service.calculateOrderTotals(
        mockCartItems,
        'address123',
        'standard',
        'DISCOUNT10'
      );

      expect(result.subtotal).toBe(250000);
      expect(result.shippingFee).toBe(30000);
      expect(result.discount).toBe(20000);
      expect(result.total).toBe(260000);
    });
  });

  describe('buildOrderTimeline', () => {
    it('should build order timeline correctly', () => {
      const mockOrder = {
        createdAt: new Date('2024-01-01'),
        confirmedAt: new Date('2024-01-02'),
        shippedAt: new Date('2024-01-03'),
        deliveredAt: new Date('2024-01-05'),
        trackingNumber: 'TRK123456'
      };

      const timeline = service.buildOrderTimeline(mockOrder);

      expect(timeline).toHaveLength(4);
      expect(timeline[0].status).toBe('pending');
      expect(timeline[1].status).toBe('confirmed');
      expect(timeline[2].status).toBe('shipped');
      expect(timeline[2].trackingNumber).toBe('TRK123456');
      expect(timeline[3].status).toBe('delivered');
    });
  });
});
