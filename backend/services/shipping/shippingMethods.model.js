const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { SHIPPING_METHOD } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const shippingRateSchema = new Schema({
  // Điều kiện áp dụng
  minWeight: { type: Number, default: 0 }, // gram
  maxWeight: { type: Number, default: Infinity },
  minValue: { type: Number, default: 0 }, // VND
  maxValue: { type: Number, default: Infinity },

  // Khu vực áp dụng
  provinces: [String], // Danh sách tỉnh/thành áp dụng
  isNationwide: { type: Boolean, default: false }, // Áp dụng toàn quốc

  // Phí và thời gian
  baseFee: { type: Number, required: true, min: 0 },
  additionalFeePerKg: { type: Number, default: 0 },
  freeShippingThreshold: { type: Number, default: 0 }, // <PERSON><PERSON><PERSON> phí ship từ giá trị này

  // Thời gian giao hàng (giờ)
  minDeliveryTime: { type: Number, required: true },
  maxDeliveryTime: { type: Number, required: true },

}, { _id: false });

const schema = new Schema({
  name: { type: String, required: true, trim: true },
  code: { type: String, required: true, unique: true, uppercase: true },
  description: String,

  // Nhà cung cấp dịch vụ
  provider: {
    type: String,
    enum: ['internal', 'ghn', 'ghtk', 'vnpost', 'j&t', 'shopee_express', 'be_delivery'],
    required: true
  },

  // Cấu hình API (nếu có)
  apiConfig: {
    baseUrl: String,
    apiKey: String,
    secretKey: String,
    shopId: String,
    extraSettings: Schema.Types.Mixed
  },

  // Bảng giá
  rates: [shippingRateSchema],

  // Trạng thái
  isActive: { type: Boolean, default: true, index: true },
  priority: { type: Number, default: 0 }, // Thứ tự hiển thị

  // Hỗ trợ
  supportsCOD: { type: Boolean, default: false }, // Hỗ trợ COD
  supportsInsurance: { type: Boolean, default: false }, // Hỗ trợ bảo hiểm
  maxCODValue: { type: Number, default: 0 },

  // Ràng buộc
  maxWeight: { type: Number, default: 30000 }, // gram
  maxDimensions: {
    length: { type: Number, default: 100 }, // cm
    width: { type: Number, default: 100 },
    height: { type: Number, default: 100 }
  },

  // Thời gian hoạt động
  workingHours: {
    start: { type: String, default: '08:00' },
    end: { type: String, default: '17:00' }
  },
  workingDays: [{
    type: String,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  }],

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Index cho hiệu năng
schema.index({ isActive: 1, priority: 1 });
schema.index({ provider: 1 });
schema.index({ code: 1 });

// Method để tính phí ship
schema.methods.calculateShippingFee = function (orderValue, weight, province) {
  if ( !this.isActive ) {
    throw new Error('Phương thức vận chuyển không khả dụng');
  }

  // Tìm rate phù hợp
  const applicableRates = this.rates.filter(rate => {
    // Kiểm tra trọng lượng
    if ( weight < rate.minWeight || weight > rate.maxWeight ) return false;

    // Kiểm tra giá trị đơn hàng
    if ( orderValue < rate.minValue || orderValue > rate.maxValue ) return false;

    // Kiểm tra khu vực
    if ( !rate.isNationwide && rate.provinces.length > 0 ) {
      if ( !rate.provinces.includes(province) ) return false;
    }

    return true;
  });

  if ( applicableRates.length === 0 ) {
    throw new Error('Không có phí ship áp dụng cho đơn hàng này');
  }

  // Lấy rate đầu tiên (có thể cải thiện logic này)
  const rate = applicableRates[0];

  // Kiểm tra miễn phí ship
  if ( rate.freeShippingThreshold > 0 && orderValue >= rate.freeShippingThreshold ) {
    return {
      fee: 0,
      originalFee: rate.baseFee,
      isFreeShipping: true,
      estimatedDelivery: {
        min: rate.minDeliveryTime,
        max: rate.maxDeliveryTime
      }
    };
  }

  // Tính phí
  let fee = rate.baseFee;

  // Thêm phí theo trọng lượng
  if ( rate.additionalFeePerKg > 0 && weight > 1000 ) {
    const additionalKg = Math.ceil((weight - 1000) / 1000);
    fee += additionalKg * rate.additionalFeePerKg;
  }

  return {
    fee,
    originalFee: fee,
    isFreeShipping: false,
    estimatedDelivery: {
      min: rate.minDeliveryTime,
      max: rate.maxDeliveryTime
    }
  };
};

// Method để kiểm tra khả dụng
schema.methods.isAvailableFor = function (orderValue, weight, dimensions, province) {
  if ( !this.isActive ) return false;

  // Kiểm tra giới hạn trọng lượng
  if ( weight > this.maxWeight ) return false;

  // Kiểm tra kích thước
  if ( dimensions ) {
    if ( dimensions.length > this.maxDimensions.length ||
      dimensions.width > this.maxDimensions.width ||
      dimensions.height > this.maxDimensions.height ) {
      return false;
    }
  }

  // Kiểm tra có rate áp dụng được không
  try {
    this.calculateShippingFee(orderValue, weight, province);
    return true;
  } catch ( error ) {
    return false;
  }
};

// Static method để lấy các phương thức khả dụng
schema.statics.getAvailableMethods = async function (orderValue, weight, dimensions, province) {
  const methods = await this.find({ isActive: true }).sort({ priority: 1 });

  return methods.filter(method =>
    method.isAvailableFor(orderValue, weight, dimensions, province)
  ).map(method => {
    const shippingInfo = method.calculateShippingFee(orderValue, weight, province);
    return {
      method: method.toObject(),
      ...shippingInfo
    };
  });
};

// Static method để tính phí cho tất cả phương thức
schema.statics.calculateAllFees = async function (orderValue, weight, province, dimensions = null) {
  const methods = await this.getAvailableMethods(orderValue, weight, dimensions, province);

  return methods.map(methodInfo => ({
    id: methodInfo.method._id,
    name: methodInfo.method.name,
    code: methodInfo.method.code,
    provider: methodInfo.method.provider,
    fee: methodInfo.fee,
    originalFee: methodInfo.originalFee,
    isFreeShipping: methodInfo.isFreeShipping,
    estimatedDelivery: methodInfo.estimatedDelivery,
    supportsCOD: methodInfo.method.supportsCOD,
    supportsInsurance: methodInfo.method.supportsInsurance
  }));
};

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(SHIPPING_METHOD, schema, SHIPPING_METHOD);
