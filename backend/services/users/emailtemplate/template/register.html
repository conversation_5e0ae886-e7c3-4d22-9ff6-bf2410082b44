<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h1, h2, h3 {
            color: #007bff;
        }

        p {
            margin-bottom: 15px;
        }

        ul, ol {
            margin-bottom: 15px;
            padding-left: 20px;
        }

        li {
            margin-bottom: 8px;
        }

        strong {
            font-weight: bold;
        }

        a {
            color: #007bff;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .greeting {
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .account-info {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .account-info ul {
            padding-left: 20px;
            margin-bottom: 0;
        }

        .button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
        }

        .button:hover {
            background-color: #218838;
        }

        .signature {
            margin-top: 30px;
            font-style: italic;
            color: #777;
        }
    </style>
</head>
<body>
<div class="container">
    <p class="greeting">Kính gửi <strong>{{userFullname}}</strong>,</p>

    <p>Chúng tôi rất vui mừng chào đón bạn đến với <strong>{{websiteName}}</strong>! Tài khoản của bạn đã được tạo thành
        công. Chào mừng bạn đến với cộng đồng của chúng tôi.</p>

    <h2>Thông Tin Tài Khoản:</h2>
    <div class="account-info">
        <ul>
            <li><strong>Tên Tài Khoản:</strong> {{account}}</li>
            {{#if trialTime}}
            <li><strong>Thời Gian Dùng Thử:</strong> {{trialTime}}</li>
            {{/if}}
        </ul>
    </div>

    <h2>Bắt Đầu Ngay:</h2>
    <p>Để bắt đầu sử dụng tài khoản của bạn, vui lòng nhấp vào liên kết kích hoạt bên dưới:</p>
    <p><a class="button" href="{{activateLink}}">Kích Hoạt Tài Khoản Của Bạn</a></p>

    {{#if hasPassword}}
    <p>Bạn có thể đăng nhập bằng tên tài khoản và mật khẩu đã chọn.</p>
    {{else}}
    <p>Vì bạn chưa đặt mật khẩu, vui lòng truy cập liên kết kích hoạt ở trên. Sau khi kích hoạt, bạn sẽ có thể đặt mật
        khẩu cho tài khoản của mình.</p>
    {{/if}}

    <h2>Khám Phá {{websiteName}}:</h2>
    <p>{{welcomeMessage}}</p>

    <h2>Hỗ Trợ:</h2>
    <p>Nếu bạn có bất kỳ câu hỏi hoặc cần hỗ trợ, xin vui lòng liên hệ với chúng tôi qua email: <a
            href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>

    <p class="signature">Trân trọng,<br>
        Đội ngũ {{websiteName}}</p>
</div>
</body>
</html>