const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./refreshtokens.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');

module.exports = {
  name: 'refreshtokens',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  actions: {},
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
