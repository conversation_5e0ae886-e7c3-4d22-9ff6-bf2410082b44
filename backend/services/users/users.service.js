const moment = require('moment');
const DbMongoose = require('../../mixins/dbMongo.mixin');
const { MoleculerClientError } = require('moleculer').Errors;
const USER = require('./users.model');
const i18next = require('i18next');
const { sendEmail } = require('../../helpers/emailHelper');
const { getConfig } = require('../../config/config');
const config = getConfig(process.env.NODE_ENV);
const { comparePassword, encryptPassword } = require('../../helpers/usersHelper');
const jwt = require('../../helpers/jwt');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const cookie = require('cookie');
const EmailTemplate = require('./emailtemplate/emailtemplate');
const axios = require('axios');
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'users',
  mixins: [DbMongoose(USER), BaseService, FunctionsCommon, AdminService],
  settings: {
    SECRET: process.env.SECRET || 'secret',
    RESET_PASSWORD_SECRET: process.env.RESET_PASSWORD_SECRET || 'reset-password-secret',
    fields: ['_id', 'email', 'fullName', 'isSystemAdmin'],
    populates: {},
    populateOptions: [],
  },

  actions: {
    login: {
      rest: 'POST /login',
      params: {
        email: { type: 'string' },
        password: { type: 'string', min: 6 },
      },
      skipToken: true,
      async handler(ctx) {
        let { email, password } = ctx.params;
        let user = await this.adapter.findOne({ email: email, isDeleted: false });

        if ( !user ) {
          throw new MoleculerClientError(i18next.t('login_fail'));
        }

        const authenticated = comparePassword(password, user.password);
        if ( !authenticated ) {
          throw new MoleculerClientError(i18next.t('login_fail'));
        }

        const accessToken = jwt.issue({ id: user._id, isUser: true }, '72h', this.settings.SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        this.setTokenToCookies(ctx, accessToken, refreshToken);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      },
    },
    loginGoogle: {
      rest: 'POST /google',
      skipToken: true,
      async handler(ctx) {
        const { code } = ctx.params;
        const data = await this.getOauthGoogleToken(code);
        const { id_token, access_token } = data;

        const googleUser = await this.getGoogleUser(id_token, access_token);
        if ( !googleUser.verified_email ) {
          throw new MoleculerClientError(i18next.t('google_email_not_verified'));
        }

        let user = await ctx.call('users.findOne', { email: googleUser.email });

        if ( !user ) {
          user = await this.actions.register({ email: googleUser.email, fullName: googleUser.name });
        }

        if ( user.message ) {
          throw new MoleculerClientError(user.message);
        }

        const accessToken = jwt.issue({ id: user._id, isUser: true }, '24h', this.settings.SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        this.setTokenToCookies(ctx, accessToken, refreshToken);
        return await this.transformDocuments(ctx, {}, user);
      },
    },
    forgotPasswordMail: {
      rest: 'POST /forgotPassword',
      params: {
        email: { type: 'string' },
      },
      skipToken: true,
      async handler(ctx) {
        let { email } = ctx.params;
        const user = await this.adapter.findOne({ email: email, isDeleted: false });
        if ( !user ) {
          throw new MoleculerClientError(i18next.t('error_email_not_found'), 422);
        }

        const htmlFormEmail = EmailTemplate.createForgotPasswordEmail(
          user, this.createResetPasswordLink(user._id, '5m'), '5',
        );
        this.sendEmailWithTemplate(user.email, i18next.t('email_subject_user_forgot_password'), htmlFormEmail);

        return { success: true };
      },
    },
    register: {
      rest: 'POST /register',
      params: {
        password: { type: "string", min: 6 },
        email: { type: 'email' },
        fullName: { type: 'string', min: 1 }
      },
      async handler(ctx) {
        const data = ctx.params;
        await this.validateEntity(data);

        delete data.active;
        delete data.isSystemAdmin;

        const checkMail = await this.adapter.findOne({ email: data.email });
        if ( checkMail ) {
          throw new MoleculerClientError(i18next.t('account_already_exists'));
        }

        // Encrypt password
        data.password = encryptPassword(data.password);

        const user = await this.adapter.insert(data);

        // Send activation email
        const activationToken = this.createActivateAccountLink(user._id, '24h');
        const htmlFormEmail = EmailTemplate.createRegisterEmail(user, activationToken);
        await this.sendEmailWithTemplate(user.email, i18next.t('email_subject_user_register'), htmlFormEmail);

        return {
          success: true,
          message: 'Đăng ký thành công. Vui lòng kiểm tra email để kích hoạt tài khoản.',
          user: await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user)
        };
      },
    },
    create: {
      rest: 'POST /',
      params: {
        password: { type: 'string', min: 6 },
        email: { type: 'email' },
        fullName: { type: 'string', min: 1 },
      },
      async handler(ctx) {
        const data = ctx.params;
        await this.validateEntity(data);

        const checkMail = await this.adapter.findOne({ email: data.email });
        if ( checkMail ) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'));
        }

        const user = await this.adapter.insert(data);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      },
    },
    changePassword: {
      rest: 'POST /changePassword',
      params: {
        oldPassword: { type: 'string', min: 6 },
        newPassword: { type: 'string', min: 6 },
      },
      auth: 'required',
      skipToken: true,
      async handler(ctx) {
        let { oldPassword, newPassword, currentRefreshToken } = ctx.params;
        let { userID } = ctx.meta;
        const user = await this.adapter.findOne({ isDeleted: false, _id: userID });
        if ( !user ) {
          throw new MoleculerClientError(i18next.t('error_old_password_wrong'), 400);
        }

        const changeTime = new Date();

        const authenticated = comparePassword(oldPassword, user.password);
        if ( !authenticated ) {
          throw new MoleculerClientError(i18next.t('error_old_password_wrong'), 400);
        }

        const encryptedPass = encryptPassword(newPassword);

        let userUpdate = await this.adapter.updateById(userID, {
          password: encryptedPass,
          lastChangePassword: changeTime,
        });

        if ( userUpdate ) {
          await this.timeout(1000);
          userUpdate.accessToken = jwt.issue({ id: user?.id, isUser: user?.isUser }, '24h', this.settings.SECRET);
        }

        if ( currentRefreshToken ) {
          await ctx.call('refreshtokens.deleteMany', {
            userId: user?._id,
            refreshToken: { $ne: currentRefreshToken },
          });
        } else {
          await ctx.call('refreshtokens.deleteMany', { userId: user?._id });
        }

        const htmlFormEmail = EmailTemplate.createChangePasswordEmail(user, changeTime);
        this.sendEmailWithTemplate(user.email, i18next.t('error_user_change_message_successful'), htmlFormEmail);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, userUpdate);
      },
    },
    resetPassword: {
      rest: 'POST /resetPassword',
      auth: 'required',
      skipToken: true,
      async handler(ctx) {
        const user = await this.adapter.findOne({ isDeleted: false, _id: ctx.meta.userID });
        if ( !user ) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'));
        }
        const encryptedPass = encryptPassword(ctx.params.password);

        const userUpdate = await this.adapter.updateById(user._id, { password: encryptedPass }, { new: true });

        const htmlFormEmail = EmailTemplate.createChangePasswordEmail(userUpdate, changeTime);
        this.sendEmailWithTemplate(user.email, i18next.t('change_password_successfully'), htmlFormEmail);
        return { success: true, message: i18next.t('reset_passwo8rd_successfully') };
      },
    },
    resolveToken: {
      cache: {
        keys: ['token'],
        ttl: 30 * 60 * 60 * 24, // 30 day
      },
      params: {
        accessToken: 'string',
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(ctx.params.accessToken, this.settings.SECRET);
        if ( decoded?.id ) {
          const user = await this.getById(decoded.id);
          return this.transformDocuments(ctx, {}, user);
        }
      },
    },
    resolveResetPasswordToken: {
      cache: {
        keys: ['token'],
        ttl: 60 * 5, // 5 minutes
      },
      params: {
        resetPasswordToken: 'string',
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(ctx.params.resetPasswordToken, this.settings.RESET_PASSWORD_SECRET);
        if ( decoded?.id ) {
          const user = await this.getById(decoded.id);
          return this.transformDocuments(ctx, {}, user);
        }
      },
    },
    me: {
      rest: 'GET /me',
      auth: 'required',
      skipUser: true,
      skipToken: true,
      async handler(ctx) {
        const user = await this.getById(ctx.meta.user._id);
        if ( !user ) throw new MoleculerClientError(i18next.t('error_user_not_found'), 400);
        const hasPassword = !!user.password;

        const userTransform = await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);

        return {
          ...userTransform,
          hasPassword,
        };
      },
    },
    logout: {
      rest: 'GET /logout',
      skipToken: true,
      async handler(ctx) {
        this.setTokenToCookies(ctx, '', '');
        return { success: true };
      },
    },
    updateInfo: {
      rest: 'PATCH /info',
      auth: 'required',
      async handler(ctx) {
        const id = ctx.meta.user._id;
        const value = ctx.params;

        delete value.password;
        delete value.isSystemAdmin;
        delete value.isDeleted;

        const checkMail = await this.adapter.findOne({ _id: { $ne: id }, email: value.email }, { _id: 1 });
        if ( checkMail ) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'), 422);
        }

        const user = await this.adapter.updateById(id, value, { new: true });
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      },
    },
    remove: {
      rest: 'DELETE /:id',
      auth: 'required',
      params: {
        id: { type: 'string', min: 3 },
      },
      async handler(ctx) {
        const { id } = ctx.params;
        let user = await this.adapter.findById(id);
        if ( user.isSystemAdmin ) {
          throw new MoleculerClientError(i18next.t('error_delete_sysadmin'));
        }

        return await this.adapter.updateById(id, { isDeleted: true }, { new: true });
      },
    },
    update: {
      rest: 'PUT /:id',
      auth: 'required',
      params: {
        id: { type: 'string', min: 3 },
      },
      async handler(ctx) {
        const value = ctx.params;

        const checkMail = await this.adapter.findOne({ _id: { $ne: value.id }, email: value.email });
        if ( checkMail ) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'), 422);
        }

        const user = await this.adapter.updateById(value.id, value, { new: true });

        if ( !user ) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }

        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      },
    },
    generateAccessToken: {
      rest: 'POST /generateAccessToken',
      async handler(ctx) {
        const { refreshToken } = ctx.meta;
        if ( !refreshToken ) {
          throw new MoleculerClientError(i18next.t('error_unauthorized'), 401);
        }
        const decoded = await jwt.verifyToken(refreshToken, this.settings.SECRET);
        if ( decoded?.id ) {
          const user = await this.getById(decoded.id);
          const accessToken = jwt.issue({ id: user?._id, isUser: true }, '72h', this.settings.SECRET);
          this.setTokenToCookies(ctx, accessToken, refreshToken);
        }
      },
    },
    getOneByEmail: {
      rest: 'GET /getOneByEmail',
      async handler(ctx) {
        const { email } = ctx.params;
        const user = await this.adapter.findOne({ email });
        if ( !user ) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      },
    },

    // Forgot password
    forgotPassword: {
      rest: 'POST /forgot-password',
      params: {
        email: { type: 'email' }
      },
      async handler(ctx) {
        const { email } = ctx.params;

        const user = await this.adapter.findOne({ email, isDeleted: false });
        if (!user) {
          // Don't reveal if email exists or not
          return {
            success: true,
            message: 'Nếu email tồn tại, bạn sẽ nhận được link reset mật khẩu.'
          };
        }

        // Create reset password token
        const resetToken = jwt.issue({ id: user._id }, '1h', this.settings.RESET_PASSWORD_SECRET);

        // Send reset password email
        const resetLink = `${config.frontendUrl}/reset-password?token=${resetToken}`;
        const htmlFormEmail = EmailTemplate.createResetPasswordEmail(user, resetLink);
        await this.sendEmailWithTemplate(user.email, 'Reset mật khẩu', htmlFormEmail);

        return {
          success: true,
          message: 'Nếu email tồn tại, bạn sẽ nhận được link reset mật khẩu.'
        };
      }
    },

    // Reset password
    resetPassword: {
      rest: 'POST /reset-password',
      params: {
        token: 'string',
        newPassword: { type: 'string', min: 6 }
      },
      async handler(ctx) {
        const { token, newPassword } = ctx.params;

        try {
          const decoded = await jwt.verifyToken(token, this.settings.RESET_PASSWORD_SECRET);
          const user = await this.getById(decoded.id);

          if (!user) {
            throw new MoleculerClientError('Token không hợp lệ', 400);
          }

          // Update password
          const hashedPassword = encryptPassword(newPassword);
          await this.adapter.updateById(user._id, {
            password: hashedPassword,
            lastChangePassword: new Date()
          });

          return { success: true, message: 'Đặt lại mật khẩu thành công' };
        } catch (error) {
          throw new MoleculerClientError('Token không hợp lệ hoặc đã hết hạn', 400);
        }
      }
    },

    // Get user profile
    getProfile: {
      rest: 'GET /profile',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const user = await this.getById(userId);

        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, user);
      }
    },

    // Update user profile
    updateProfile: {
      rest: 'PUT /profile',
      auth: 'required',
      params: {
        fullName: { type: 'string', min: 1, optional: true },
        phone: { type: 'string', optional: true },
        dateOfBirth: { type: 'date', optional: true },
        gender: { type: 'enum', values: ['male', 'female', 'other'], optional: true }
      },
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const updateData = ctx.params;

        // Remove sensitive fields
        delete updateData.email;
        delete updateData.password;
        delete updateData.isSystemAdmin;

        const updated = await this.adapter.updateById(userId, updateData);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Logout
    logout: {
      rest: 'POST /logout',
      auth: 'required',
      async handler(ctx) {
        const refreshToken = ctx.meta.refreshToken;

        if (refreshToken) {
          await ctx.call('refresh-tokens.revoke', { token: refreshToken });
        }

        // Clear cookies
        ctx.meta.$responseHeaders = {
          'Set-Cookie': [
            'accessToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
            'refreshToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly'
          ]
        };

        return { success: true, message: 'Đăng xuất thành công' };
      }
    },
  },
  methods: {
    async refreshTokenCreator(ctx, user) {
      if ( !user ) {
        throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
      }
      let expRefreshToken;
      const nowTimeStamp = new Date().getTime();
      let expTimeStamp = nowTimeStamp + 30 * 24 * 60 * 60 * 1000;

      const nextExpDay = moment(new Date(expTimeStamp))
        .add(1, 'days')
        .format('YYYY-MM-DD 18:00:00');
      const expDay = moment(new Date(expTimeStamp)).format('YYYY-MM-DD 18:00:00');
      const nextExpDayTimeStamp = new Date(nextExpDay).getTime();
      const expDayTimeStamp = new Date(expDay).getTime();

      let expiresDateTime;
      if ( expTimeStamp > expDayTimeStamp ) {
        expRefreshToken = nextExpDayTimeStamp - nowTimeStamp;
        expiresDateTime = nextExpDay;
      } else {
        expRefreshToken = expDayTimeStamp - nowTimeStamp;
        expiresDateTime = expDay;
      }

      const refreshToken = jwt.issue({ id: user?._id, isUser: true }, expRefreshToken / 1000 + 's',
        this.settings.SECRET,
      );
      await ctx.call('refreshtokens.create', {
        userId: user._id,
        refreshToken: refreshToken,
        expiresDate: expiresDateTime,
      });
      return refreshToken;
    },
    async getGoogleUser(id_token, access_token) {
      const { data } = await axios.get(
        'https://www.googleapis.com/oauth2/v1/userinfo',
        {
          params: {
            access_token,
            alt: 'json',
          },
          headers: {
            Authorization: `Bearer ${id_token}`,
          },
        },
      );
      return data;
    },
    async getOauthGoogleToken(code) {
      try {
        const body = {
          code,
          client_id: config.client_id,
          client_secret: config.client_secret,
          redirect_uri: config.redirect_uri,
          grant_type: 'authorization_code',
          access_type: 'offline',
        };
        const { data } = await axios.post(
          'https://oauth2.googleapis.com/token',
          body,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );
        return data;
      } catch ( e ) {
        console.error(e);
        throw new MoleculerClientError(i18next.t('error_google_login'), 422);
      }
    },
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    async seedDB() {
      const systemAdmin = {
        email: '<EMAIL>',
        password: '123456',
        fullName: 'System Admin',
        isSystemAdmin: true,
      };
      await this.adapter.insert(systemAdmin);
      return this.clearCache();
    },
    createResetPasswordLink(userId, expiresIn) {
      const resetPasswordToken = jwt.issue({ id: userId }, expiresIn, this.settings.RESET_PASSWORD_SECRET);
      return config.domain + '/reset-password?resetPasswordToken=' + resetPasswordToken;
    },
    sendEmailWithTemplate(email, subject, formHtml) {
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`,
        to: email,
        subject: subject,
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if ( err ) {
          console.log(err);
        }
      });
    },
    setTokenToCookies(ctx, accessToken, refreshToken) {
      const cookieOptions = {
        httpOnly: true,
        secure: true,
        path: '/',
        maxAge: 30 * 24 * 60 * 60, // 30 day
      };
      const cookieAccessToken = cookie.serialize('accessToken', accessToken, cookieOptions);
      const cookieRefreshToken = cookie.serialize('refreshToken', refreshToken, cookieOptions);

      ctx.meta.$responseHeaders = {
        'Set-Cookie': [cookieAccessToken, cookieRefreshToken],
      };
    },
  },
  events: {},
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
    const count = await this.adapter.count();
    if ( count === 0 ) {
      return this.seedDB();
    }
  },
};
