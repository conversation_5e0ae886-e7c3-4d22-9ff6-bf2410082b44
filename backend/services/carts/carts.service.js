const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./carts.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');
const { MoleculerClientError } = require('moleculer').Errors;
const i18next = require('i18next');

module.exports = {
  name: 'carts',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      variantId: 'variants',
      userId: 'users'
    },
    populateOptions: ['variantId'],
  },

  hooks: {
    before: {
      create: async function(ctx) {
        // Validate variant exists and has stock
        const variant = await ctx.call('variants.get', { id: ctx.params.variantId });
        if (!variant) {
          throw new MoleculerClientError('<PERSON><PERSON><PERSON> phẩm không tồn tại', 404);
        }
        if (variant.totalStock < ctx.params.quantity) {
          throw new MoleculerClientError('Không đủ hàng trong kho', 400);
        }
      }
    }
  },

  actions: {
    // Thêm sản phẩm vào giỏ hàng
    addToCart: {
      rest: 'POST /add',
      auth: 'required',
      params: {
        variantId: 'string',
        quantity: { type: 'number', min: 1, default: 1 }
      },
      async handler(ctx) {
        const { variantId, quantity } = ctx.params;
        const userId = ctx.meta.user._id;

        // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
        const existingItem = await this.adapter.findOne({
          userId,
          variantId,
          isDeleted: false
        });

        if (existingItem) {
          // Cập nhật số lượng
          const newQuantity = existingItem.quantity + quantity;

          // Validate stock
          const variant = await ctx.call('variants.get', { id: variantId });
          if (variant.totalStock < newQuantity) {
            throw new MoleculerClientError('Không đủ hàng trong kho', 400);
          }

          const updated = await this.adapter.updateById(existingItem._id, {
            quantity: newQuantity
          });

          return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
        } else {
          // Tạo mới
          const cartItem = await this.adapter.insert({
            userId,
            variantId,
            quantity
          });

          return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, cartItem);
        }
      }
    },

    // Cập nhật số lượng sản phẩm trong giỏ hàng
    updateQuantity: {
      rest: 'PUT /:id/quantity',
      auth: 'required',
      params: {
        id: 'string',
        quantity: { type: 'number', min: 1 }
      },
      async handler(ctx) {
        const { id, quantity } = ctx.params;
        const userId = ctx.meta.user._id;

        const cartItem = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!cartItem) {
          throw new MoleculerClientError('Sản phẩm không có trong giỏ hàng', 404);
        }

        // Validate stock
        const variant = await ctx.call('variants.get', { id: cartItem.variantId });
        if (variant.totalStock < quantity) {
          throw new MoleculerClientError('Không đủ hàng trong kho', 400);
        }

        const updated = await this.adapter.updateById(id, { quantity });
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Xóa sản phẩm khỏi giỏ hàng
    removeFromCart: {
      rest: 'DELETE /:id',
      auth: 'required',
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const userId = ctx.meta.user._id;

        const cartItem = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!cartItem) {
          throw new MoleculerClientError('Sản phẩm không có trong giỏ hàng', 404);
        }

        await this.adapter.updateById(id, { isDeleted: true });
        return { success: true, message: 'Đã xóa sản phẩm khỏi giỏ hàng' };
      }
    },

    // Lấy danh sách giỏ hàng của user
    getMyCart: {
      rest: 'GET /my-cart',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        const cartItems = await this.adapter.find({
          query: {
            userId,
            isDeleted: false
          },
          populate: [
            {
              path: 'variantId',
              populate: [
                { path: 'productId', select: 'name slug images' },
                { path: 'images' }
              ]
            }
          ],
          sort: { createdAt: -1 }
        });

        // Tính tổng tiền
        let subtotal = 0;
        const items = cartItems.map(item => {
          const itemTotal = item.variantId.price * item.quantity;
          subtotal += itemTotal;
          return {
            ...item,
            itemTotal
          };
        });

        return {
          items,
          summary: {
            totalItems: items.length,
            totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
            subtotal,
            total: subtotal // Có thể thêm tax, shipping fee sau
          }
        };
      }
    },

    // Xóa toàn bộ giỏ hàng
    clearCart: {
      rest: 'DELETE /clear',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        await this.adapter.updateMany(
          { userId, isDeleted: false },
          { isDeleted: true }
        );

        return { success: true, message: 'Đã xóa toàn bộ giỏ hàng' };
      }
    },

    // Đếm số lượng sản phẩm trong giỏ hàng
    getCartCount: {
      rest: 'GET /count',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        const count = await this.adapter.count({
          userId,
          isDeleted: false
        });

        return { count };
      }
    },

    // Validate giỏ hàng trước khi checkout
    validateCart: {
      params: {
        userId: 'string'
      },
      async handler(ctx) {
        const { userId } = ctx.params;

        const cartItems = await this.adapter.find({
          query: {
            userId,
            isDeleted: false
          },
          populate: ['variantId']
        });

        if (cartItems.length === 0) {
          throw new MoleculerClientError('Giỏ hàng trống', 400);
        }

        const errors = [];
        let totalAmount = 0;

        for (const item of cartItems) {
          const variant = item.variantId;

          // Kiểm tra variant còn tồn tại
          if (!variant) {
            errors.push(`Sản phẩm ${item._id} không còn tồn tại`);
            continue;
          }

          // Kiểm tra stock
          if (variant.totalStock < item.quantity) {
            errors.push(`Sản phẩm ${variant.name} chỉ còn ${variant.totalStock} trong kho`);
          }

          totalAmount += variant.price * item.quantity;
        }

        if (errors.length > 0) {
          throw new MoleculerClientError('Giỏ hàng có lỗi', 400, '', { errors });
        }

        return {
          valid: true,
          items: cartItems,
          totalAmount
        };
      }
    }
  },

  methods: {
    // Tính tổng tiền giỏ hàng
    async calculateCartTotal(userId) {
      const cartItems = await this.adapter.find({
        query: {
          userId,
          isDeleted: false
        },
        populate: ['variantId']
      });

      let subtotal = 0;
      let totalQuantity = 0;

      cartItems.forEach(item => {
        if (item.variantId) {
          subtotal += item.variantId.price * item.quantity;
          totalQuantity += item.quantity;
        }
      });

      return {
        subtotal,
        totalQuantity,
        totalItems: cartItems.length
      };
    },

    // Sync cart với stock hiện tại
    async syncCartWithStock(userId) {
      const cartItems = await this.adapter.find({
        query: {
          userId,
          isDeleted: false
        },
        populate: ['variantId']
      });

      const updates = [];

      for (const item of cartItems) {
        if (!item.variantId) {
          // Xóa item nếu variant không tồn tại
          updates.push(this.adapter.updateById(item._id, { isDeleted: true }));
        } else if (item.variantId.totalStock < item.quantity) {
          // Cập nhật quantity nếu vượt quá stock
          const newQuantity = Math.max(0, item.variantId.totalStock);
          if (newQuantity === 0) {
            updates.push(this.adapter.updateById(item._id, { isDeleted: true }));
          } else {
            updates.push(this.adapter.updateById(item._id, { quantity: newQuantity }));
          }
        }
      }

      if (updates.length > 0) {
        await Promise.all(updates);
      }

      return updates.length;
    }
  },

  events: {
    // Khi variant bị xóa hoặc hết hàng
    'variant.deleted': {
      async handler(ctx) {
        const { variantId } = ctx.params;

        // Xóa tất cả cart items có variant này
        await this.adapter.updateMany(
          { variantId, isDeleted: false },
          { isDeleted: true }
        );
      }
    },

    // Khi stock variant thay đổi
    'variant.stock.updated': {
      async handler(ctx) {
        const { variantId, newStock } = ctx.params;

        if (newStock === 0) {
          // Xóa tất cả cart items nếu hết hàng
          await this.adapter.updateMany(
            { variantId, isDeleted: false },
            { isDeleted: true }
          );
        } else {
          // Cập nhật quantity nếu vượt quá stock mới
          const cartItems = await this.adapter.find({
            query: { variantId, isDeleted: false }
          });

          for (const item of cartItems) {
            if (item.quantity > newStock) {
              await this.adapter.updateById(item._id, { quantity: newStock });
            }
          }
        }
      }
    }
  },

  async started() {
    this.logger.info('Carts service started');
  },

  async stopped() {
    this.logger.info('Carts service stopped');
  }
};
