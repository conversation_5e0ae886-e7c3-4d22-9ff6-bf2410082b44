const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { USER, VARIANT, CART } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },
  variantId: { type: Schema.Types.ObjectId, ref: VARIANT, required: true },
  quantity: { type: Number, required: true, min: 1 },
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(CART, schema, CART);