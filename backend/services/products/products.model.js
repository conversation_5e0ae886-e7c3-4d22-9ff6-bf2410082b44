const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { PRODUCT, FILE, CATEGORY } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true, index: true },
  description: String,
  categoryIds: [{ type: Schema.Types.ObjectId, ref: CATEGORY }],
  attributes: [{ name: String, values: [String] }],
  images: { type: Schema.Types.ObjectId, ref: FILE },

  averageRating: { type: Number, default: 0, min: 0, max: 5 }, // Rating trung bình từ reviews
  totalReviews: { type: Number, default: 0, min: 0 }, // Tổng số reviews
  totalSold: { type: Number, default: 0, min: 0 }, // Tổng số đã bán (cho best selling)

  // Product flags
  isFeatured: { type: Boolean, default: false }, // Sản phẩm nổi bật
  isNew: { type: Boolean, default: false }, // Sản phẩm mới

  isActive: { type: Boolean, default: true, index: true },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(PRODUCT, schema, PRODUCT);

