const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./stockLogs.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'stockLogs',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      variantId: 'variants',
      warehouseId: 'warehouse',
      orderId: 'orders',
      userId: 'users'
    },
    populateOptions: [],
  },

  actions: {
    // L<PERSON>y lịch sử stock theo variant
    getByVariant: {
      params: {
        variantId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        type: { type: 'string', optional: true },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true }
      },
      async handler(ctx) {
        const { variantId, page, limit, type, fromDate, toDate } = ctx.params;

        let query = { variantId };

        if ( type ) {
          query.type = type;
        }

        if ( fromDate || toDate ) {
          query.createdAt = {};
          if ( fromDate ) query.createdAt.$gte = fromDate;
          if ( toDate ) query.createdAt.$lte = toDate;
        }

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: [
            { path: 'userId', select: 'firstName lastName email' },
            { path: 'orderId', select: 'orderNumber status' },
            { path: 'warehouseId', select: 'name address' }
          ]
        };

        return await this.adapter.paginate({ query, options });
      }
    },

    // Lấy lịch sử stock theo kho
    getByWarehouse: {
      params: {
        warehouseId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        type: { type: 'string', optional: true },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true }
      },
      async handler(ctx) {
        const { warehouseId, page, limit, type, fromDate, toDate } = ctx.params;

        let query = { warehouseId };

        if ( type ) {
          query.type = type;
        }

        if ( fromDate || toDate ) {
          query.createdAt = {};
          if ( fromDate ) query.createdAt.$gte = fromDate;
          if ( toDate ) query.createdAt.$lte = toDate;
        }

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: [
            {
              path: 'variantId',
              populate: {
                path: 'productId',
                select: 'name slug'
              }
            },
            { path: 'userId', select: 'firstName lastName' },
            { path: 'orderId', select: 'orderNumber' }
          ]
        };

        return await this.adapter.paginate({ query, options });
      }
    },

    // Lấy lịch sử theo user
    getByUser: {
      params: {
        userId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true }
      },
      async handler(ctx) {
        const { userId, page, limit, fromDate, toDate } = ctx.params;

        let query = { userId };

        if ( fromDate || toDate ) {
          query.createdAt = {};
          if ( fromDate ) query.createdAt.$gte = fromDate;
          if ( toDate ) query.createdAt.$lte = toDate;
        }

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: [
            {
              path: 'variantId',
              populate: {
                path: 'productId',
                select: 'name slug'
              }
            },
            { path: 'warehouseId', select: 'name' },
            { path: 'orderId', select: 'orderNumber' }
          ]
        };

        return await this.adapter.paginate({ query, options });
      }
    },

    // Thống kê stock movements
    getMovementStats: {
      params: {
        warehouseId: { type: 'string', optional: true },
        variantId: { type: 'string', optional: true },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true },
        groupBy: { type: 'string', optional: true, default: 'day' } // day, week, month
      },
      async handler(ctx) {
        const { warehouseId, variantId, fromDate, toDate, groupBy } = ctx.params;

        let matchCondition = {};

        if ( warehouseId ) {
          matchCondition.warehouseId = this.adapter.stringToObjectID(warehouseId);
        }

        if ( variantId ) {
          matchCondition.variantId = this.adapter.stringToObjectID(variantId);
        }

        if ( fromDate || toDate ) {
          matchCondition.createdAt = {};
          if ( fromDate ) matchCondition.createdAt.$gte = fromDate;
          if ( toDate ) matchCondition.createdAt.$lte = toDate;
        }

        let dateFormat;
        switch ( groupBy ) {
          case 'week':
            dateFormat = '%Y-%U';
            break;
          case 'month':
            dateFormat = '%Y-%m';
            break;
          default:
            dateFormat = '%Y-%m-%d';
        }

        const pipeline = [
          { $match: matchCondition },
          {
            $group: {
              _id: {
                date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
                type: '$type',
                direction: '$direction'
              },
              count: { $sum: 1 },
              totalQuantity: { $sum: '$quantity' },
              totalValue: { $sum: '$totalCost' }
            }
          },
          { $sort: { '_id.date': -1 } }
        ];

        const movements = await this.adapter.model.aggregate(pipeline);

        // Group by date
        const groupedData = {};
        movements.forEach(item => {
          const date = item._id.date;
          if ( !groupedData[date] ) {
            groupedData[date] = {
              date,
              increases: { count: 0, quantity: 0, value: 0 },
              decreases: { count: 0, quantity: 0, value: 0 },
              byType: {}
            };
          }

          const key = item._id.direction === 'increase' ? 'increases' : 'decreases';
          groupedData[date][key].count += item.count;
          groupedData[date][key].quantity += item.totalQuantity;
          groupedData[date][key].value += item.totalValue || 0;

          const type = item._id.type;
          if ( !groupedData[date].byType[type] ) {
            groupedData[date].byType[type] = { count: 0, quantity: 0, value: 0 };
          }
          groupedData[date].byType[type].count += item.count;
          groupedData[date].byType[type].quantity += item.totalQuantity;
          groupedData[date].byType[type].value += item.totalValue || 0;
        });

        return Object.values(groupedData);
      }
    },

    // Audit trail - theo dõi các thay đổi bất thường
    getAuditTrail: {
      params: {
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 50 },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true },
        largeQuantityThreshold: { type: 'number', optional: true, default: 100 }
      },
      async handler(ctx) {
        const { page, limit, fromDate, toDate, largeQuantityThreshold } = ctx.params;

        let query = {};

        // Filter theo thời gian
        if ( fromDate || toDate ) {
          query.createdAt = {};
          if ( fromDate ) query.createdAt.$gte = fromDate;
          if ( toDate ) query.createdAt.$lte = toDate;
        }

        // Lọc các giao dịch có khối lượng lớn hoặc bất thường
        query.$or = [
          { quantity: { $gte: largeQuantityThreshold } },
          { type: 'adjustment' },
          { type: 'damage' },
          { type: 'expired' }
        ];

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: [
            {
              path: 'variantId',
              populate: {
                path: 'productId',
                select: 'name slug'
              }
            },
            { path: 'userId', select: 'firstName lastName email' },
            { path: 'warehouseId', select: 'name' }
          ]
        };

        const result = await this.adapter.paginate({ query, options });

        // Thêm risk score cho mỗi log entry
        result.docs = result.docs.map(log => ({
          ...log,
          riskScore: this.calculateRiskScore(log),
          flags: this.getAuditFlags(log)
        }));

        return result;
      }
    },

    // Lấy tổng quan activities
    getActivitySummary: {
      params: {
        days: { type: 'number', optional: true, default: 7 },
        warehouseId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { days, warehouseId } = ctx.params;

        const fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - days);

        let matchCondition = {
          createdAt: { $gte: fromDate }
        };

        if ( warehouseId ) {
          matchCondition.warehouseId = this.adapter.stringToObjectID(warehouseId);
        }

        const summary = await this.adapter.model.aggregate([
          { $match: matchCondition },
          {
            $group: {
              _id: '$type',
              count: { $sum: 1 },
              totalQuantity: { $sum: '$quantity' },
              totalValue: { $sum: '$totalCost' },
              lastActivity: { $max: '$createdAt' }
            }
          },
          { $sort: { count: -1 } }
        ]);

        const totalActivities = await this.adapter.count(matchCondition);
        const uniqueVariants = await this.adapter.model.distinct('variantId', matchCondition);
        const uniqueUsers = await this.adapter.model.distinct('userId', matchCondition);

        return {
          period: `${days} days`,
          totalActivities,
          uniqueVariants: uniqueVariants.length,
          uniqueUsers: uniqueUsers.length,
          byType: summary,
          generatedAt: new Date()
        };
      }
    },

    // Export stock logs
    exportLogs: {
      params: {
        format: { type: 'string', optional: true, default: 'csv' }, // csv, excel
        variantId: { type: 'string', optional: true },
        warehouseId: { type: 'string', optional: true },
        fromDate: { type: 'date', optional: true },
        toDate: { type: 'date', optional: true },
        types: { type: 'array', items: 'string', optional: true }
      },
      async handler(ctx) {
        const { format, variantId, warehouseId, fromDate, toDate, types } = ctx.params;

        let query = {};

        if ( variantId ) query.variantId = variantId;
        if ( warehouseId ) query.warehouseId = warehouseId;
        if ( types && types.length > 0 ) query.type = { $in: types };

        if ( fromDate || toDate ) {
          query.createdAt = {};
          if ( fromDate ) query.createdAt.$gte = fromDate;
          if ( toDate ) query.createdAt.$lte = toDate;
        }

        const logs = await this.adapter.find({
          query,
          limit: 10000, // Giới hạn để tránh quá tải
          sort: { createdAt: -1 },
          populate: [
            {
              path: 'variantId',
              populate: {
                path: 'productId',
                select: 'name slug'
              }
            },
            { path: 'userId', select: 'firstName lastName' },
            { path: 'warehouseId', select: 'name' }
          ]
        });

        // Format data for export
        const exportData = logs.map(log => ({
          Date: log.createdAt,
          Product: log.variantId?.productId?.name || 'N/A',
          SKU: log.variantId?.sku || 'N/A',
          Warehouse: log.warehouseId?.name || 'N/A',
          Type: log.type,
          Direction: log.direction,
          Quantity: log.quantity,
          'Before Total': log.beforeStock.total,
          'After Total': log.afterStock.total,
          'Before Available': log.beforeStock.available,
          'After Available': log.afterStock.available,
          Reason: log.reason,
          'User': log.userId ? `${log.userId.firstName} ${log.userId.lastName}` : 'System',
          'Order ID': log.orderId || '',
          'Unit Cost': log.unitCost || '',
          'Total Cost': log.totalCost || '',
          Notes: log.notes || ''
        }));

        // Trong thực tế sẽ generate file và trả về download link
        return {
          format,
          recordCount: exportData.length,
          data: exportData.slice(0, 100), // Chỉ return 100 records đầu để demo
          downloadUrl: `/api/downloads/stock-logs-${Date.now()}.${format}`,
          generatedAt: new Date()
        };
      }
    }
  },

  methods: {
    // Tính toán risk score cho audit
    calculateRiskScore(log) {
      let score = 0;

      // Large quantity movements
      if ( log.quantity > 1000 ) score += 3;
      else if ( log.quantity > 100 ) score += 2;
      else if ( log.quantity > 50 ) score += 1;

      // Suspicious types
      if ( ['adjustment', 'damage', 'expired'].includes(log.type) ) score += 2;

      // Out of business hours (assuming 9-18)
      const hour = new Date(log.createdAt).getHours();
      if ( hour < 9 || hour > 18 ) score += 1;

      // Weekend activity
      const day = new Date(log.createdAt).getDay();
      if ( day === 0 || day === 6 ) score += 1;

      return Math.min(score, 5); // Max score 5
    },

    // Lấy audit flags
    getAuditFlags(log) {
      const flags = [];

      if ( log.quantity > 1000 ) flags.push('LARGE_QUANTITY');
      if ( ['adjustment', 'damage'].includes(log.type) ) flags.push('MANUAL_ADJUSTMENT');
      if ( !log.userId ) flags.push('SYSTEM_GENERATED');
      if ( log.notes && log.notes.length < 10 ) flags.push('INSUFFICIENT_NOTES');

      const hour = new Date(log.createdAt).getHours();
      if ( hour < 9 || hour > 18 ) flags.push('OFF_HOURS');

      return flags;
    },

    // Validate log data
    validateLogData(data) {
      const errors = [];

      if ( !data.variantId ) {
        errors.push('Variant ID là bắt buộc');
      }

      if ( !data.type ) {
        errors.push('Type là bắt buộc');
      }

      if ( !data.quantity || data.quantity <= 0 ) {
        errors.push('Quantity phải lớn hơn 0');
      }

      if ( !data.reason || data.reason.trim().length === 0 ) {
        errors.push('Reason là bắt buộc');
      }

      if ( errors.length > 0 ) {
        throw new MoleculerError('Dữ liệu log không hợp lệ', 400, '', errors);
      }
    }
  },

  events: {
    // Lắng nghe inventory stock changes để tạo log
    'inventory.stock.changed': {
      async handler(ctx) {
        const logData = ctx.params;

        try {
          // Validate và tạo log entry
          const stockLog = {
            variantId: logData.variantId,
            warehouseId: logData.warehouseId,
            type: logData.type,
            quantity: logData.quantity,
            direction: logData.direction || (logData.quantity > 0 ? 'increase' : 'decrease'),
            beforeStock: {
              total: logData.oldTotal || 0,
              available: logData.oldAvailable || 0,
              reserved: logData.oldReserved || 0,
              committed: logData.oldCommitted || 0
            },
            afterStock: {
              total: logData.newTotal || 0,
              available: logData.newAvailable || 0,
              reserved: logData.newReserved || 0,
              committed: logData.newCommitted || 0
            },
            reason: logData.reason,
            notes: logData.notes,
            unitCost: logData.cost,
            totalCost: logData.cost ? logData.cost * logData.quantity : undefined,
            orderId: logData.orderId,
            userId: logData.userId,
            referenceId: logData.referenceId,
            referenceType: logData.referenceType,
            source: logData.source || 'automatic',
            batchNumber: logData.batchNumber,
            expiryDate: logData.expiryDate
          };

          await this.adapter.insert(stockLog);
          this.logger.info('Stock log created:', { variantId: logData.variantId, type: logData.type });

        } catch ( error ) {
          this.logger.error('Failed to create stock log:', error);
        }
      }
    }
  },

  async started() {
    this.logger.info('StockLogs service started');
  },

  async stopped() {
    this.logger.info('StockLogs service stopped');
  }
};

