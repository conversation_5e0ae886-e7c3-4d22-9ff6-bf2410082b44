const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { STOCK_LOG, PRODUCT_VARIANT, USER, ORDER, WAREHOUSE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  variantId: { type: Schema.Types.ObjectId, ref: PRODUCT_VARIANT, required: true },
  warehouseId: { type: Schema.Types.ObjectId, ref: WAREHOUSE },

  // Change details
  type: {
    type: String,
    required: true,
    enum: ['reserve', 'commit', 'fulfill', 'release', 'receive', 'adjustment', 'damage', 'expired', 'transfer', 'return']
  },
  quantity: { type: Number, required: true },
  direction: { type: String, enum: ['increase', 'decrease'], required: true },

  // Stock levels before and after
  beforeStock: {
    total: { type: Number, default: 0 },
    available: { type: Number, default: 0 },
    reserved: { type: Number, default: 0 },
    committed: { type: Number, default: 0 }
  },
  afterStock: {
    total: { type: Number, default: 0 },
    available: { type: Number, default: 0 },
    reserved: { type: Number, default: 0 },
    committed: { type: Number, default: 0 }
  },

  // Transaction details
  reason: { type: String, required: true },
  notes: String,

  // Cost information
  unitCost: { type: Number, min: 0 },
  totalCost: { type: Number, min: 0 },

  // References
  orderId: { type: Schema.Types.ObjectId, ref: ORDER },
  userId: { type: Schema.Types.ObjectId, ref: USER }, // Người thực hiện
  referenceId: String, // ID tham chiếu khác (PO, Transfer, etc.)
  referenceType: String, // Loại tham chiếu

  // Batch/Lot information
  batchNumber: String,
  expiryDate: Date,

  // System info
  source: { type: String, default: 'manual' }, // manual, automatic, import, api
  ipAddress: String,
  userAgent: String,
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(STOCK_LOG, schema, STOCK_LOG);

