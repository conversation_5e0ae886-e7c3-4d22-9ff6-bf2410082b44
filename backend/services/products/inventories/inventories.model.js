const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { INVENTORY, PRODUCT_VARIANT, WAREHOUSE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  variantId: { type: Schema.Types.ObjectId, ref: PRODUCT_VARIANT, required: true, unique: true },
  warehouseId: { type: Schema.Types.ObjectId, ref: WAREHOUSE },

  // Stock levels
  totalStock: { type: Number, required: true, default: 0, min: 0 },
  availableStock: { type: Number, required: true, default: 0, min: 0 },
  reservedStock: { type: Number, default: 0, min: 0 }, // Đ<PERSON> được đặt nhưng chưa thanh toán
  committedStock: { type: Number, default: 0, min: 0 }, // Đã thanh toán, chờ giao

  // Reorder management
  reorderLevel: { type: Number, default: 10, min: 0 }, // <PERSON><PERSON><PERSON> cảnh b<PERSON><PERSON> hết hàng
  reorderQuantity: { type: Number, default: 50, min: 0 }, // Số lượng nhập khi reorder
  maxStock: { type: Number, min: 0 }, // Mức tồn kho tối đa

  // Cost tracking
  averageCost: { type: Number, default: 0, min: 0 }, // Giá vốn trung bình
  lastPurchasePrice: { type: Number, min: 0 }, // Giá mua gần nhất
  lastPurchaseDate: { type: Date }, // Ngày mua gần nhất

  // Dates
  expiryDate: { type: Date },
  lastStockUpdate: { type: Date, default: Date.now },

  // Status
  isTrackingEnabled: { type: Boolean, default: true }, // Có theo dõi tồn kho không
  stockAlertEnabled: { type: Boolean, default: true }, // Có cảnh báo hết hàng không

  // Location in warehouse
  location: {
    aisle: String, // Lối đi
    shelf: String, // Kệ
    bin: String    // Ngăn
  },

  // Notes
  notes: String,
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Indexes for better performance
schema.index({ variantId: 1, warehouseId: 1 });
schema.index({ availableStock: 1 });
schema.index({ reorderLevel: 1 });
schema.index({ 'location.aisle': 1, 'location.shelf': 1 });

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(INVENTORY, schema, INVENTORY);
