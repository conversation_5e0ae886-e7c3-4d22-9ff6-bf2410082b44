const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./inventories.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'inventories',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      variantId: 'variants',
      warehouseId: 'warehouse'
    },
    populateOptions: [],
  },

  actions: {
    getByVariant: {
      params: {
        variantId: 'string'
      },
      async handler(ctx) {
        const { variantId } = ctx.params;

        const inventory = await this.adapter.findOne(
          { variantId },
          {
            populate: [
              { path: 'variantId', select: 'sku name price' },
              { path: 'warehouseId', select: 'address ward province' }
            ]
          }
        );

        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy thông tin tồn kho', 404);
        }

        return inventory;
      }
    },
    reserveStock: {
      params: {
        variantId: 'string',
        quantity: 'number',
        orderId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { variantId, quantity, orderId } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        if ( inventory.availableStock < quantity ) {
          throw new MoleculerError(`Không đủ hàng trong kho. Có sẵn: ${inventory.availableStock}, Yêu cầu: ${quantity}`, 400);
        }

        const updatedInventory = await this.adapter.updateOne(
          { variantId },
          {
            $inc: {
              availableStock: -quantity,
              reservedStock: quantity
            },
            $set: {
              lastStockUpdate: new Date()
            }
          }
        );

        // Log stock change
        await this.logStockChange({
          variantId,
          type: 'reserve',
          quantity,
          reason: `Reserved for order ${orderId || 'unknown'}`,
          orderId,
          oldAvailable: inventory.availableStock,
          newAvailable: inventory.availableStock - quantity
        });

        this.broker.emit('inventory.reserved', {
          variantId,
          quantity,
          orderId,
          remainingStock: updatedInventory.availableStock
        });

        return updatedInventory;
      }
    },

    // Commit stock khi đơn hàng được thanh toán
    commitStock: {
      params: {
        variantId: 'string',
        quantity: 'number',
        orderId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { variantId, quantity, orderId } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        if ( inventory.reservedStock < quantity ) {
          throw new MoleculerError(`Số lượng reserved không đủ. Reserved: ${inventory.reservedStock}, Yêu cầu: ${quantity}`, 400);
        }

        const updatedInventory = await this.adapter.updateOne(
          { variantId },
          {
            $inc: {
              reservedStock: -quantity,
              committedStock: quantity
            },
            $set: {
              lastStockUpdate: new Date()
            }
          }
        );

        await this.logStockChange({
          variantId,
          type: 'commit',
          quantity,
          reason: `Committed for order ${orderId || 'unknown'}`,
          orderId
        });

        this.broker.emit('inventory.committed', {
          variantId,
          quantity,
          orderId
        });

        return updatedInventory;
      }
    },

    // Fulfill stock khi đơn hàng được giao
    fulfillStock: {
      params: {
        variantId: 'string',
        quantity: 'number',
        orderId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { variantId, quantity, orderId } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        if ( inventory.committedStock < quantity ) {
          throw new MoleculerError(`Số lượng committed không đủ. Committed: ${inventory.committedStock}, Yêu cầu: ${quantity}`, 400);
        }

        const updatedInventory = await this.adapter.updateOne(
          { variantId },
          {
            $inc: {
              totalStock: -quantity,
              committedStock: -quantity
            },
            $set: {
              lastStockUpdate: new Date()
            }
          }
        );

        await this.logStockChange({
          variantId,
          type: 'fulfill',
          quantity,
          reason: `Fulfilled for order ${orderId || 'unknown'}`,
          orderId,
          oldTotal: inventory.totalStock,
          newTotal: inventory.totalStock - quantity
        });

        // Kiểm tra cảnh báo reorder
        await this.checkReorderAlert(variantId, updatedInventory);

        this.broker.emit('inventory.fulfilled', {
          variantId,
          quantity,
          orderId,
          remainingStock: updatedInventory.totalStock
        });

        return updatedInventory;
      }
    },

    // Release stock khi hủy đơn hàng
    releaseStock: {
      params: {
        variantId: 'string',
        quantity: 'number',
        orderId: { type: 'string', optional: true },
        source: { type: 'string', optional: true, default: 'reserved' } // 'reserved' hoặc 'committed'
      },
      async handler(ctx) {
        const { variantId, quantity, orderId, source } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        let updateFields = {
          $set: { lastStockUpdate: new Date() }
        };

        if ( source === 'committed' ) {
          if ( inventory.committedStock < quantity ) {
            throw new MoleculerError(`Số lượng committed không đủ để release`, 400);
          }
          updateFields.$inc = {
            committedStock: -quantity,
            availableStock: quantity
          };
        } else {
          if ( inventory.reservedStock < quantity ) {
            throw new MoleculerError(`Số lượng reserved không đủ để release`, 400);
          }
          updateFields.$inc = {
            reservedStock: -quantity,
            availableStock: quantity
          };
        }

        const updatedInventory = await this.adapter.updateOne({ variantId }, updateFields);

        await this.logStockChange({
          variantId,
          type: 'release',
          quantity,
          reason: `Released from ${source} for cancelled order ${orderId || 'unknown'}`,
          orderId
        });

        this.broker.emit('inventory.released', {
          variantId,
          quantity,
          orderId,
          source,
          newAvailableStock: inventory.availableStock + quantity
        });

        return updatedInventory;
      }
    },

    // Receive stock - nhập kho
    receiveStock: {
      params: {
        variantId: 'string',
        quantity: 'number',
        cost: { type: 'number', optional: true },
        purchaseOrderId: { type: 'string', optional: true },
        supplierId: { type: 'string', optional: true },
        expiryDate: { type: 'date', optional: true },
        notes: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { variantId, quantity, cost, purchaseOrderId, supplierId, expiryDate, notes } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        let updateFields = {
          $inc: {
            totalStock: quantity,
            availableStock: quantity
          },
          $set: {
            lastStockUpdate: new Date()
          }
        };

        // Cập nhật average cost nếu có cost
        if ( cost && cost > 0 ) {
          const newTotalStock = inventory.totalStock + quantity;
          const currentValue = inventory.totalStock * inventory.averageCost;
          const newValue = quantity * cost;
          const newAverageCost = (currentValue + newValue) / newTotalStock;

          updateFields.$set.averageCost = Math.round(newAverageCost * 100) / 100;
          updateFields.$set.lastPurchasePrice = cost;
          updateFields.$set.lastPurchaseDate = new Date();
        }

        // Cập nhật expiry date nếu có
        if ( expiryDate ) {
          updateFields.$set.expiryDate = expiryDate;
        }

        const updatedInventory = await this.adapter.updateOne({ variantId }, updateFields);

        await this.logStockChange({
          variantId,
          type: 'receive',
          quantity,
          cost,
          reason: notes || `Received from supplier ${supplierId || 'unknown'}`,
          purchaseOrderId,
          supplierId,
          oldTotal: inventory.totalStock,
          newTotal: inventory.totalStock + quantity
        });

        this.broker.emit('inventory.received', {
          variantId,
          quantity,
          cost,
          purchaseOrderId,
          supplierId,
          newTotalStock: inventory.totalStock + quantity
        });

        return updatedInventory;
      }
    },

    // Adjust stock - điều chỉnh tồn kho
    adjustStock: {
      params: {
        variantId: 'string',
        newTotalStock: 'number',
        reason: 'string',
        notes: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { variantId, newTotalStock, reason, notes } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        const difference = newTotalStock - inventory.totalStock;
        const newAvailableStock = Math.max(0, inventory.availableStock + difference);

        const updatedInventory = await this.adapter.updateOne(
          { variantId },
          {
            $set: {
              totalStock: newTotalStock,
              availableStock: newAvailableStock,
              lastStockUpdate: new Date()
            }
          }
        );

        await this.logStockChange({
          variantId,
          type: 'adjustment',
          quantity: Math.abs(difference),
          direction: difference > 0 ? 'increase' : 'decrease',
          reason,
          notes,
          oldTotal: inventory.totalStock,
          newTotal: newTotalStock
        });

        // Kiểm tra reorder alert
        await this.checkReorderAlert(variantId, updatedInventory);

        this.broker.emit('inventory.adjusted', {
          variantId,
          oldStock: inventory.totalStock,
          newStock: newTotalStock,
          difference,
          reason
        });

        return updatedInventory;
      }
    },

    // Lấy danh sách cần reorder
    getReorderList: {
      params: {
        warehouseId: { type: 'string', optional: true },
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 }
      },
      async handler(ctx) {
        const { warehouseId, page, limit } = ctx.params;

        let query = {
          $expr: { $lte: ['$availableStock', '$reorderLevel'] },
          isTrackingEnabled: true,
          stockAlertEnabled: true
        };

        if ( warehouseId ) {
          query.warehouseId = warehouseId;
        }

        const options = {
          page,
          limit,
          sort: { availableStock: 1 },
          populate: [
            {
              path: 'variantId',
              populate: {
                path: 'productId',
                select: 'name slug'
              }
            },
            { path: 'warehouseId', select: 'name address' }
          ]
        };

        const result = await this.adapter.paginate({ query, options });

        // Thêm suggested order quantity
        result.docs = result.docs.map(inventory => ({
          ...inventory,
          suggestedOrderQuantity: inventory.reorderQuantity,
          shortfall: inventory.reorderLevel - inventory.availableStock
        }));

        return result;
      }
    },

    // Thống kê inventory nâng cao
    getAdvancedStats: {
      params: {
        warehouseId: { type: 'string', optional: true },
        categoryId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { warehouseId, categoryId } = ctx.params;

        let matchCondition = {};
        if ( warehouseId ) {
          matchCondition.warehouseId = this.adapter.stringToObjectID(warehouseId);
        }

        const pipeline = [
          { $match: matchCondition },
          {
            $lookup: {
              from: 'variants',
              localField: 'variantId',
              foreignField: '_id',
              as: 'variant'
            }
          },
          { $unwind: '$variant' },
          {
            $lookup: {
              from: 'products',
              localField: 'variant.productId',
              foreignField: '_id',
              as: 'product'
            }
          },
          { $unwind: '$product' }
        ];

        if ( categoryId ) {
          pipeline.push({
            $match: {
              'product.categoryIds': this.adapter.stringToObjectID(categoryId)
            }
          });
        }

        pipeline.push({
          $group: {
            _id: null,
            totalValue: {
              $sum: {
                $multiply: ['$totalStock', '$averageCost']
              }
            },
            totalUnits: { $sum: '$totalStock' },
            availableUnits: { $sum: '$availableStock' },
            reservedUnits: { $sum: '$reservedStock' },
            committedUnits: { $sum: '$committedStock' },
            lowStockCount: {
              $sum: {
                $cond: [{ $lte: ['$availableStock', '$reorderLevel'] }, 1, 0]
              }
            },
            outOfStockCount: {
              $sum: {
                $cond: [{ $eq: ['$availableStock', 0] }, 1, 0]
              }
            },
            overStockCount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $ne: ['$maxStock', null] },
                      { $gt: ['$totalStock', '$maxStock'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        });

        const stats = await this.adapter.model.aggregate(pipeline);
        return stats[0] || {
          totalValue: 0,
          totalUnits: 0,
          availableUnits: 0,
          reservedUnits: 0,
          committedUnits: 0,
          lowStockCount: 0,
          outOfStockCount: 0,
          overStockCount: 0
        };
      }
    },

    // Dự báo cần nhập hàng
    getForecastReorder: {
      params: {
        variantId: 'string',
        days: { type: 'number', default: 30 }
      },
      async handler(ctx) {
        const { variantId, days } = ctx.params;

        const inventory = await this.adapter.findOne({ variantId });
        if ( !inventory ) {
          throw new MoleculerError('Không tìm thấy inventory', 404);
        }

        // Lấy lịch sử bán hàng (từ orders) - tạm thời mock
        // Trong thực tế sẽ query từ orders/sales data
        const mockDailySales = 2; // 2 sản phẩm/ngày trung bình

        const currentStock = inventory.availableStock;
        const dailyConsumption = mockDailySales;
        const daysUntilReorder = Math.floor(currentStock / dailyConsumption);
        const forecastDate = new Date();
        forecastDate.setDate(forecastDate.getDate() + daysUntilReorder);

        return {
          variantId,
          currentStock,
          reorderLevel: inventory.reorderLevel,
          dailyConsumption,
          daysUntilReorder,
          forecastReorderDate: forecastDate,
          suggestedOrderQuantity: inventory.reorderQuantity,
          recommendation: daysUntilReorder <= 7 ? 'ORDER_NOW' :
            daysUntilReorder <= 14 ? 'ORDER_SOON' : 'MONITOR'
        };
      }
    }
  },

  methods: {
    // Ghi log thay đổi stock
    async logStockChange(data) {
      const logData = {
        ...data,
        timestamp: new Date(),
        userId: data.userId || 'system'
      };

      // Emit event để service khác xử lý (có thể tạo stockLogs service)
      this.broker.emit('inventory.stock.changed', logData);

      // Log ra console để debug
      this.logger.info('Stock change logged:', {
        variantId: data.variantId,
        type: data.type,
        quantity: data.quantity,
        reason: data.reason
      });
    },

    // Kiểm tra cảnh báo reorder
    async checkReorderAlert(variantId, inventory) {
      if ( !inventory.stockAlertEnabled || !inventory.isTrackingEnabled ) {
        return;
      }

      const currentInventory = inventory || await this.adapter.findOne({ variantId });

      if ( currentInventory.availableStock <= currentInventory.reorderLevel ) {
        this.broker.emit('inventory.reorder.alert', {
          variantId,
          currentStock: currentInventory.availableStock,
          reorderLevel: currentInventory.reorderLevel,
          suggestedQuantity: currentInventory.reorderQuantity,
          urgency: currentInventory.availableStock === 0 ? 'CRITICAL' :
            currentInventory.availableStock <= currentInventory.reorderLevel * 0.5 ? 'HIGH' : 'MEDIUM',
          warehouseId: currentInventory.warehouseId
        });
      }
    },

    // Validate inventory data
    validateInventoryData(data) {
      const errors = [];

      if ( data.totalStock < 0 ) {
        errors.push('Total stock không được âm');
      }

      if ( data.availableStock < 0 ) {
        errors.push('Available stock không được âm');
      }

      if ( data.reservedStock < 0 ) {
        errors.push('Reserved stock không được âm');
      }

      if ( data.reorderLevel < 0 ) {
        errors.push('Reorder level không được âm');
      }

      if ( data.maxStock && data.maxStock < data.reorderLevel ) {
        errors.push('Max stock phải lớn hơn reorder level');
      }

      if ( errors.length > 0 ) {
        throw new MoleculerError('Dữ liệu inventory không hợp lệ', 400, '', errors);
      }
    },

    // Tính toán stock availability
    calculateAvailability(inventory) {
      const total = inventory.totalStock;
      const available = inventory.availableStock;
      const reserved = inventory.reservedStock;
      const committed = inventory.committedStock;

      return {
        total,
        available,
        reserved,
        committed,
        utilization: total > 0 ? ((reserved + committed) / total * 100).toFixed(2) : 0,
        availability: total > 0 ? (available / total * 100).toFixed(2) : 0
      };
    }
  },

  events: {
    // Khi có đơn hàng mới cần reserve stock
    'order.created': {
      async handler(ctx) {
        const order = ctx.params;
        this.logger.info('Order created, processing inventory reservations:', order._id);

        try {
          // Reserve stock cho tất cả items trong order
          for ( const item of order.items || [] ) {
            await this.actions.reserveStock({
              variantId: item.variantId,
              quantity: item.quantity,
              orderId: order._id
            });
          }
        } catch ( error ) {
          this.logger.error('Failed to reserve stock for order:', order._id, error);
          // Emit event để order service biết có lỗi
          this.broker.emit('inventory.reservation.failed', {
            orderId: order._id,
            error: error.message
          });
        }
      }
    },

    // Khi đơn hàng được thanh toán
    'order.payment.confirmed': {
      async handler(ctx) {
        const order = ctx.params;
        this.logger.info('Order payment confirmed, committing stock:', order._id);

        try {
          for ( const item of order.items || [] ) {
            await this.actions.commitStock({
              variantId: item.variantId,
              quantity: item.quantity,
              orderId: order._id
            });
          }
        } catch ( error ) {
          this.logger.error('Failed to commit stock for order:', order._id, error);
        }
      }
    },

    // Khi đơn hàng được giao hoàn tất
    'order.fulfilled': {
      async handler(ctx) {
        const order = ctx.params;
        this.logger.info('Order fulfilled, finalizing stock:', order._id);

        try {
          for ( const item of order.items || [] ) {
            await this.actions.fulfillStock({
              variantId: item.variantId,
              quantity: item.quantity,
              orderId: order._id
            });
          }
        } catch ( error ) {
          this.logger.error('Failed to fulfill stock for order:', order._id, error);
        }
      }
    },

    // Khi đơn hàng bị hủy
    'order.cancelled': {
      async handler(ctx) {
        const { order, stage } = ctx.params; // stage: 'reserved' hoặc 'committed'
        this.logger.info('Order cancelled, releasing stock:', order._id);

        try {
          for ( const item of order.items || [] ) {
            await this.actions.releaseStock({
              variantId: item.variantId,
              quantity: item.quantity,
              orderId: order._id,
              source: stage || 'reserved'
            });
          }
        } catch ( error ) {
          this.logger.error('Failed to release stock for cancelled order:', order._id, error);
        }
      }
    },

    // Khi có variant mới được tạo
    'variant.created': {
      async handler(ctx) {
        const variant = ctx.params;
        this.logger.info('Variant created, creating inventory record:', variant._id);

        try {
          // Tự động tạo inventory record với stock = 0
          await this.actions.create({
            variantId: variant._id,
            totalStock: 0,
            availableStock: 0,
            reorderLevel: 10,
            reorderQuantity: 50
          });
        } catch ( error ) {
          // Ignore nếu đã tồn tại
          if ( !error.message.includes('đã tồn tại') ) {
            this.logger.error('Failed to create inventory for variant:', variant._id, error);
          }
        }
      }
    },

    // Khi variant bị xóa
    'variant.deleted': {
      async handler(ctx) {
        const { variantId } = ctx.params;
        this.logger.info('Variant deleted, removing inventory:', variantId);

        try {
          await this.actions.deleteByVariant({ variantId });
        } catch ( error ) {
          this.logger.error('Failed to delete inventory for variant:', variantId, error);
        }
      }
    }
  },
};
