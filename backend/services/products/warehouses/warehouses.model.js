const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { WAREHOUSE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  name: { type: String, required: true, trim: true }, // Tên kho
  address: { type: String, required: true, trim: true }, // Số nhà, tên đường
  ward: { type: String, required: true, trim: true }, // Phường/Xã
  province: { type: String, required: true, trim: true }, // Tỉnh/Thành phố
  country: { type: String, default: 'Vietnam', trim: true },
  phoneNumber: { type: String, trim: true }, // Số điện thoại liên hệ
  email: { type: String, trim: true }, // Email liên hệ
  manager: { type: String, trim: true }, // Người quản lý kho
  capacity: { type: Number, min: 0 }, // <PERSON><PERSON><PERSON> chứ<PERSON> kho (m2 hoặc m3)
  isActive: { type: Boolean, default: true }, // Kho có hoạt động không
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(WAREHOUSE, schema, WAREHOUSE);
