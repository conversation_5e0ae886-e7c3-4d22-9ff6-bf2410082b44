const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./warehouses.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'warehouses',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    // Lấy kho theo khu vực
    getByRegion: {
      rest: 'GET /get-by-region',
      params: {
        province: 'string'
      },
      async handler(ctx) {
        const { province } = ctx.params;

        return await this.adapter.find({
          query: {
            province: { $regex: province, $options: 'i' },
            isDeleted: false,
            isActive: true
          },
          sort: { name: 1 }
        });
      }
    },

    // Lấy chi tiết kho kèm thống kê
    getDetail: {
      rest: 'GET /:id/detail',
      async handler(ctx) {
        const { id } = ctx.params;

        const warehouse = await this.adapter.findById(id);
        if ( !warehouse ) {
          throw new MoleculerError('Kho không tồn tại', 404);
        }

        // Lấy thống kê inventory trong kho này
        const inventoryStats = await ctx.call('inventories.find', {
          query: { warehouseId: id }
        });

        const totalProducts = inventoryStats.length;
        let totalStock = 0;
        let totalValue = 0;

        for ( const inventory of inventoryStats ) {
          if ( inventory.totalStock ) {
            totalStock += inventory.totalStock;
          }
        }

        return {
          ...warehouse,
          stats: {
            totalProducts,
            totalStock,
            totalValue
          }
        };
      }
    },
  },

  methods: {},
  events: {},
  async started() {
  },
  async stopped() {
  }
};
