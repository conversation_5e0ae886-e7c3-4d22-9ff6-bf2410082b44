const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { REVIEW, USER, PRODUCT, ORDER, FILE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },
  productId: { type: Schema.Types.ObjectId, ref: PRODUCT, required: true },
  orderId: { type: Schema.Types.ObjectId, ref: ORDER }, // Đảm bảo chỉ đánh giá sau khi mua

  // Nội dung đánh giá
  rating: { type: Number, required: true, min: 1, max: 5 },
  title: { type: String, trim: true },
  comment: { type: String, trim: true },

  // Hình ảnh/video đánh giá
  images: [{ type: Schema.Types.ObjectId, ref: FILE }],
  videos: [{ type: Schema.Types.ObjectId, ref: FILE }],
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(REVIEW, schema, REVIEW);
