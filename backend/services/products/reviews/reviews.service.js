const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./reviews.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'reviews',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      userId: 'users',
      productId: 'products',
      orderId: 'orders',
      images: 'files',
      videos: 'files'
    },
    populateOptions: [],
  },
  hooks: {
    before: {
      async create(ctx) {
        const { productId } = ctx.params;
        const { userId } = ctx.meta.user;

        const order = await this.broker.call('orders.findOne', {
          query: {
            userId,
            'items.productId': productId
          }
        });

        if ( !order ) {
          throw new MoleculerError('Bạn phải mua sản phẩm này trước khi đánh giá', 400);
        }

        // Kiểm tra xem đã đánh giá sản phẩm này chưa
        const existingReview = await this.adapter.findOne({
          query: { userId, productId }
        });

        if ( existingReview ) {
          throw new this.broker.Errors.MoleculerError('Bạn đã đánh giá sản phẩm này rồi', 400);
        }
      },
      async update(ctx) {
        const { id, userId, productId } = ctx.params;

        // Kiểm tra xem review có thuộc về người dùng này không
        const review = await this.adapter.findById(id);
        if ( !review || review.userId.toString() !== userId ) {
          throw new this.broker.Errors.MoleculerError('Bạn không có quyền sửa review này', 403);
        }

        // Kiểm tra xem sản phẩm có đúng không
        if ( review.productId.toString() !== productId ) {
          throw new this.broker.Errors.MoleculerError('Sản phẩm không đúng với review này', 400);
        }
      }
    },
    after: {
      async create(ctx) {
        if ( ctx.params.productId ) {
          await this.updateProductRating(ctx.params.productId);
        }
      },
      async update(ctx) {
        if ( ctx.params.productId ) {
          await this.updateProductRating(ctx.params.productId);
        }
      }
    }
  },

  actions: {
    // Lấy thống kê rating của sản phẩm
    getProductRatingStats: {
      params: {
        productId: 'string'
      },
      async handler(ctx) {
        const { productId } = ctx.params;

        const stats = await this.adapter.model.aggregate([
          { $match: { productId: this.adapter.stringToObjectID(productId) } },
          {
            $group: {
              _id: '$rating',
              count: { $sum: 1 }
            }
          },
          { $sort: { _id: -1 } }
        ]);

        const totalReviews = await this.adapter.count({ productId });
        const avgRating = await this.adapter.model.aggregate([
          { $match: { productId: this.adapter.stringToObjectID(productId) } },
          {
            $group: {
              _id: null,
              avgRating: { $avg: '$rating' }
            }
          }
        ]);

        return {
          totalReviews,
          averageRating: avgRating.length > 0 ? Math.round(avgRating[0].avgRating * 10) / 10 : 0,
          ratingDistribution: stats.reduce((acc, curr) => {
            acc[curr._id] = curr.count;
            return acc;
          }, {})
        };
      }
    },

    // Báo cáo review spam/vi phạm
    reportReview: {
      params: {
        reviewId: 'string',
        reporterId: 'string',
        reason: 'string', // spam, inappropriate, fake, etc.
        description: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { reviewId, reporterId, reason, description } = ctx.params;

        // Lưu báo cáo vào bảng reports (có thể tạo service riêng)
        // Tạm thời emit event để admin xử lý
        this.broker.emit('review.reported', {
          reviewId,
          reporterId,
          reason,
          description,
          reportedAt: new Date()
        });

        return { success: true, message: 'Báo cáo đã được gửi' };
      }
    },

    // Lấy reviews của user
    getByUser: {
      params: {
        userId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 }
      },
      async handler(ctx) {
        const { userId, page, limit } = ctx.params;

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: [
            { path: 'productId', select: 'name slug images' },
            { path: 'images' },
            { path: 'videos' }
          ]
        };

        return await this.adapter.paginate({
          query: { userId },
          options
        });
      }
    },

    // Lấy top reviews (rating cao, nhiều tương tác)
    getTopReviews: {
      params: {
        productId: { type: 'string', optional: true },
        limit: { type: 'number', optional: true, default: 10 }
      },
      async handler(ctx) {
        const { productId, limit } = ctx.params;

        let query = { rating: { $gte: 4 } };
        if ( productId ) {
          query.productId = productId;
        }

        return await this.adapter.find({
          query,
          limit,
          sort: { rating: -1, createdAt: -1 },
          populate: [
            { path: 'userId', select: 'firstName lastName avatar' },
            { path: 'productId', select: 'name slug' },
            { path: 'images' }
          ]
        });
      }
    }
  },

  methods: {
    // Cập nhật rating trung bình cho sản phẩm
    async updateProductRating(productId) {
      const stats = await this.adapter.model.aggregate([
        { $match: { productId: this.adapter.stringToObjectID(productId) } },
        {
          $group: {
            _id: null,
            avgRating: { $avg: '$rating' },
            totalReviews: { $sum: 1 }
          }
        }
      ]);

      if ( stats.length > 0 ) {
        const { avgRating, totalReviews } = stats[0];

        // Cập nhật vào product (có thể thêm field rating vào product model)
        await this.broker.call('products.update', {
          id: productId,
          averageRating: Math.round(avgRating * 10) / 10,
          totalReviews
        });
      }
    },

  },

  events: {},
  async started() {
  },
  async stopped() {
  }
};
