const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { VARIANT, PRODUCT, FILE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  productId: { type: mongoose.Schema.Types.ObjectId, ref: PRODUCT, required: true },
  totalStock: { type: Number, required: true, min: 0 },
  sku: { type: String, required: true, unique: true }, // Mã SKU duy nhất cho biến thể
  name: { type: String, required: true },
  price: { type: Number, required: true, min: 0 },
  compareAtPrice: { type: Number, min: 0 }, // Giá gốc (để hiển thị giảm giá)
  cost: { type: Number, min: 0 }, // Giá vốn

  attributes: [{
    name: { type: String, required: true }, // Ví dụ: "<PERSON><PERSON><PERSON> sắ<PERSON>", "<PERSON><PERSON><PERSON> thước"
    value: { type: String, required: true } // Ví dụ: "Đỏ", "XL"
  }],

  // Hình ảnh riêng cho biến thể
  images: [{ type: mongoose.Schema.Types.ObjectId, ref: FILE }],

  // Trọng lượng và kích thước (cho tính phí ship)
  weight: { type: Number, min: 0 }, // gram
  dimensions: {
    length: { type: Number, min: 0 }, // cm
    width: { type: Number, min: 0 },  // cm
    height: { type: Number, min: 0 }  // cm
  },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(VARIANT, schema, VARIANT);
