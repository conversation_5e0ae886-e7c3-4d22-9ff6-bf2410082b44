const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./variants.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'variants',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      productId: 'products',
      images: 'files'
    },
    populateOptions: [],
  },
  hooks: {
    before: {},
    after: {
      async create(ctx) {
      },
      async update(ctx) {
      },
      async remove(ctx) {
        const { id } = ctx.params;
        await ctx.call('inventories.removeByVariant', { variantId: id });
      }
    }
  },

  actions: {
    // <PERSON><PERSON>y danh sách variants theo product
    getByProduct: {
      params: {
        productId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 }
      },
      async handler(ctx) {
        const { productId, page, limit } = ctx.params;

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: ['images']
        };

        return await this.adapter.paginate({
          query: { productId },
          options
        });
      }
    },

    // Lấy chi tiết variant kèm inventory
    getDetail: {
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;

        const variant = await this.adapter.findById(id, {
          populate: [
            { path: 'productId', select: 'name slug' },
            { path: 'images' }
          ]
        });

        if ( !variant ) {
          throw new MoleculerError('Variant không tồn tại', 404);
        }

        // Lấy thông tin inventory
        const inventory = await ctx.call('inventories.getByVariant', { variantId: id });

        return {
          ...variant,
          inventory
        };
      }
    },

    // Tìm variant theo SKU
    getBySku: {
      params: {
        sku: 'string'
      },
      async handler(ctx) {
        const { sku } = ctx.params;

        const variant = await this.adapter.findOne({ sku }, {
          populate: [
            { path: 'productId', select: 'name slug' },
            { path: 'images' }
          ]
        });

        if ( !variant ) {
          throw new MoleculerError('Variant không tồn tại', 404);
        }

        return variant;
      }
    },

    // Lấy variants có giá trong khoảng
    getByPriceRange: {
      params: {
        minPrice: { type: 'number', optional: true },
        maxPrice: { type: 'number', optional: true },
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 }
      },
      async handler(ctx) {
        const { minPrice, maxPrice, page, limit } = ctx.params;

        let query = {};

        if ( minPrice !== undefined || maxPrice !== undefined ) {
          query.price = {};
          if ( minPrice !== undefined ) query.price.$gte = minPrice;
          if ( maxPrice !== undefined ) query.price.$lte = maxPrice;
        }

        const options = {
          page,
          limit,
          sort: { price: 1 },
          populate: [
            { path: 'productId', select: 'name slug' },
            { path: 'images' }
          ]
        };

        return await this.adapter.paginate({ query, options });
      }
    },

    // Cập nhật giá hàng loạt
    bulkUpdatePrice: {
      params: {
        variantIds: { type: 'array', items: 'string' },
        priceChange: 'number', // Số tiền thay đổi (+ hoặc -)
        isPercentage: { type: 'boolean', optional: true, default: false }
      },
      async handler(ctx) {
        const { variantIds, priceChange, isPercentage } = ctx.params;

        const variants = await this.adapter.find({ query: { _id: { $in: variantIds } } });

        const updatePromises = variants.map(variant => {
          let newPrice = variant.price;

          if ( isPercentage ) {
            newPrice = variant.price * (1 + priceChange / 100);
          } else {
            newPrice = variant.price + priceChange;
          }

          // Đảm bảo giá không âm
          newPrice = Math.max(0, newPrice);

          return this.adapter.updateById(variant._id, { $set: { price: newPrice } });
        });

        return await Promise.all(updatePromises);
      }
    }
  },

  methods: {
    // Tạo SKU tự động
    generateSku(productName, attributes) {
      const productCode = productName.substring(0, 3).toUpperCase();
      const attrCodes = attributes.map(attr =>
        attr.value.substring(0, 2).toUpperCase()
      ).join('-');
      const timestamp = Date.now().toString().slice(-4);

      return `${productCode}-${attrCodes}-${timestamp}`;
    },

    // Validate variant data
    validateVariantData(data) {
      const errors = [];

      if ( !data.sku || data.sku.trim().length === 0 ) {
        errors.push('SKU không được để trống');
      }

      if ( !data.name || data.name.trim().length === 0 ) {
        errors.push('Tên variant không được để trống');
      }

      if ( data.price === undefined || data.price < 0 ) {
        errors.push('Giá phải lớn hơn hoặc bằng 0');
      }

      if ( errors.length > 0 ) {
        throw new MoleculerError('Dữ liệu không hợp lệ', 400, '', errors);
      }
    }
  },
  events: {},
  async started() {
  },
  async stopped() {
  }
};
