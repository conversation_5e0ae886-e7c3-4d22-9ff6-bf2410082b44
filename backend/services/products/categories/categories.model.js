const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { CATEGORY, FILE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  name: { type: String, required: true, trim: true },
  slug: { type: String },
  description: { type: String },
  parentId: { type: Schema.Types.ObjectId, ref: CATEGORY, default: null },
  imageId: { type: Schema.Types.ObjectId, ref: FILE },
  isActive: { type: Boolean, default: true },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(CATEGORY, schema, CATEGORY);
