const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./categories.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'categories',
  mixins: [DbMongoose(MODEL), FunctionsCommon, BaseService, AdminService],
  settings: {
    populates: {
      parentId: 'categories.get',
      imageId: 'files.get'
    },
    populateOptions: [],
  },
  hooks: {
    before: {
      create: async function (ctx) {
        ctx.params.slug = this.generateSlug(ctx.params.name);
        const data = await this.adapter.findOne({ slug: ctx.params.slug });
        if ( data ) {
          throw new MoleculerError('<PERSON>h mục với slug này đã tồn tại', 400);
        }
      },
      update: async function (ctx) {
        if ( ctx.params.name ) {
          ctx.params.slug = this.generateSlug(ctx.params.name);
        }
        const data = this.adapter.findOne({ slug: ctx.params.slug });
        if ( data ) {
          throw new MoleculerError('Danh mục với slug này đã tồn tại', 400);
        }
      }
    },
    after: {}
  },

  actions: {
    getAllWithoutPagination: {
      admin: false,
    },
    getTree: {
      rest: 'GET /tree',
      async handler(ctx) {
        const categories = await this.adapter.find({
          query: { isActive: true },
          sort: { sortOrder: 1, name: 1 },
          populate: ['imageId']
        });

        return this.createDataTree(categories);
      }
    },
    getChildren: {
      rest: 'GET /children',
      params: {
        parentId: 'string',
      },
      async handler(ctx) {
        const { parentId } = ctx.params;
        return await this.adapter.find({
          query: { parentId, isActive: true },
          sort: { name: 1 },
          populate: ['imageId']
        });
      }
    },
  },

  methods: {},
  events: {},
  async started() {
  },
  async stopped() {
  }
};
