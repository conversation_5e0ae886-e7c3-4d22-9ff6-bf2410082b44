const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { ADDRESS, USER } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },
  fullName: { type: String, required: true, trim: true },
  phone: { type: String, required: true, trim: true },
  address: { type: String, required: true, trim: true }, // Số nhà, tên đường
  ward: { type: String, required: true, trim: true }, // Phường/Xã
  province: { type: String, required: true, trim: true }, // Tỉnh/Thành phố
  country: { type: String, default: 'Vietnam', trim: true },
  type: { type: String, enum: ['home', 'office', 'other'], default: 'home' },
  isActive: { type: Boolean, default: true, index: true },
  note: String,
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ADDRESS, schema, ADDRESS);
