const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./addresses.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');
const { MoleculerClientError } = require('moleculer').Errors;
const i18next = require('i18next');

module.exports = {
  name: 'addresses',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      userId: 'users'
    },
    populateOptions: [],
  },

  hooks: {
    before: {
      create: async function(ctx) {
        ctx.params.userId = ctx.meta.user._id;
        this.validateAddressData(ctx.params);
      },
      update: async function(ctx) {
        this.validateAddressData(ctx.params);

        // <PERSON><PERSON><PERSON> tra quyền sở hữu
        const address = await this.adapter.findById(ctx.params.id);
        if (!address || address.userId.toString() !== ctx.meta.user._id.toString()) {
          throw new MoleculerClientError('Không có quyền truy cập địa chỉ này', 403);
        }
      }
    }
  },

  actions: {
    // Lấy danh sách địa chỉ của user
    getMyAddresses: {
      rest: 'GET /my-addresses',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        const addresses = await this.adapter.find({
          query: {
            userId,
            isDeleted: false
          },
          sort: { isDefault: -1, createdAt: -1 }
        });

        return addresses;
      }
    },

    // Tạo địa chỉ mới
    createAddress: {
      rest: 'POST /',
      auth: 'required',
      params: {
        fullName: { type: 'string', min: 1 },
        phone: { type: 'string', min: 10 },
        address: { type: 'string', min: 1 },
        ward: { type: 'string', min: 1 },
        province: { type: 'string', min: 1 },
        country: { type: 'string', optional: true, default: 'Vietnam' },
        type: { type: 'enum', values: ['home', 'office', 'other'], optional: true, default: 'home' },
        note: { type: 'string', optional: true },
        isDefault: { type: 'boolean', optional: true, default: false }
      },
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const data = { ...ctx.params, userId };

        // Nếu đây là địa chỉ mặc định, bỏ default của các địa chỉ khác
        if (data.isDefault) {
          await this.adapter.updateMany(
            { userId, isDeleted: false },
            { isDefault: false }
          );
        }

        // Nếu đây là địa chỉ đầu tiên, tự động set làm default
        const existingCount = await this.adapter.count({ userId, isDeleted: false });
        if (existingCount === 0) {
          data.isDefault = true;
        }

        const address = await this.adapter.insert(data);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, address);
      }
    },

    // Cập nhật địa chỉ
    updateAddress: {
      rest: 'PUT /:id',
      auth: 'required',
      params: {
        id: 'string',
        fullName: { type: 'string', min: 1, optional: true },
        phone: { type: 'string', min: 10, optional: true },
        address: { type: 'string', min: 1, optional: true },
        ward: { type: 'string', min: 1, optional: true },
        province: { type: 'string', min: 1, optional: true },
        country: { type: 'string', optional: true },
        type: { type: 'enum', values: ['home', 'office', 'other'], optional: true },
        note: { type: 'string', optional: true },
        isDefault: { type: 'boolean', optional: true }
      },
      async handler(ctx) {
        const { id, ...updateData } = ctx.params;
        const userId = ctx.meta.user._id;

        // Nếu set làm địa chỉ mặc định
        if (updateData.isDefault) {
          await this.adapter.updateMany(
            { userId, isDeleted: false, _id: { $ne: id } },
            { isDefault: false }
          );
        }

        const updated = await this.adapter.updateById(id, updateData);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Xóa địa chỉ
    deleteAddress: {
      rest: 'DELETE /:id',
      auth: 'required',
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const userId = ctx.meta.user._id;

        const address = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!address) {
          throw new MoleculerClientError('Địa chỉ không tồn tại', 404);
        }

        // Không cho phép xóa địa chỉ mặc định nếu còn địa chỉ khác
        if (address.isDefault) {
          const otherAddresses = await this.adapter.count({
            userId,
            _id: { $ne: id },
            isDeleted: false
          });

          if (otherAddresses > 0) {
            throw new MoleculerClientError('Không thể xóa địa chỉ mặc định. Vui lòng chọn địa chỉ mặc định khác trước.', 400);
          }
        }

        await this.adapter.updateById(id, { isDeleted: true });
        return { success: true, message: 'Đã xóa địa chỉ' };
      }
    },

    // Đặt địa chỉ mặc định
    setDefaultAddress: {
      rest: 'PUT /:id/set-default',
      auth: 'required',
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const userId = ctx.meta.user._id;

        const address = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!address) {
          throw new MoleculerClientError('Địa chỉ không tồn tại', 404);
        }

        // Bỏ default của tất cả địa chỉ khác
        await this.adapter.updateMany(
          { userId, isDeleted: false },
          { isDefault: false }
        );

        // Set địa chỉ này làm default
        const updated = await this.adapter.updateById(id, { isDefault: true });
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Lấy địa chỉ mặc định
    getDefaultAddress: {
      rest: 'GET /default',
      auth: 'required',
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        const address = await this.adapter.findOne({
          userId,
          isDefault: true,
          isDeleted: false
        });

        if (!address) {
          throw new MoleculerClientError('Chưa có địa chỉ mặc định', 404);
        }

        return address;
      }
    },

    // Validate địa chỉ (cho checkout)
    validateAddress: {
      params: {
        addressId: 'string',
        userId: 'string'
      },
      async handler(ctx) {
        const { addressId, userId } = ctx.params;

        const address = await this.adapter.findOne({
          _id: addressId,
          userId,
          isDeleted: false
        });

        if (!address) {
          throw new MoleculerClientError('Địa chỉ không hợp lệ', 400);
        }

        // Validate required fields
        const requiredFields = ['fullName', 'phone', 'address', 'ward', 'province'];
        const missingFields = requiredFields.filter(field => !address[field]);

        if (missingFields.length > 0) {
          throw new MoleculerClientError(`Địa chỉ thiếu thông tin: ${missingFields.join(', ')}`, 400);
        }

        return {
          valid: true,
          address
        };
      }
    }
  },

  methods: {
    // Validate dữ liệu địa chỉ
    validateAddressData(data) {
      const errors = [];

      if (data.fullName && data.fullName.trim().length < 1) {
        errors.push('Họ tên không được để trống');
      }

      if (data.phone) {
        const phoneRegex = /^[0-9]{10,11}$/;
        if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
          errors.push('Số điện thoại không hợp lệ');
        }
      }

      if (data.address && data.address.trim().length < 1) {
        errors.push('Địa chỉ không được để trống');
      }

      if (data.ward && data.ward.trim().length < 1) {
        errors.push('Phường/Xã không được để trống');
      }

      if (data.province && data.province.trim().length < 1) {
        errors.push('Tỉnh/Thành phố không được để trống');
      }

      if (errors.length > 0) {
        throw new MoleculerClientError('Dữ liệu địa chỉ không hợp lệ', 400, '', { errors });
      }
    },

    // Format địa chỉ đầy đủ
    formatFullAddress(address) {
      const parts = [
        address.address,
        address.ward,
        address.province,
        address.country
      ].filter(part => part && part.trim());

      return parts.join(', ');
    },

    // Tìm địa chỉ gần nhất (có thể dùng cho shipping calculation)
    async findNearestWarehouse(address) {
      // Logic tìm kho gần nhất dựa trên province
      // Có thể tích hợp với service shipping hoặc warehouses
      return await this.broker.call('warehouses.getByRegion', {
        province: address.province
      });
    }
  },

  events: {
    // Khi user bị xóa
    'user.deleted': {
      async handler(ctx) {
        const { userId } = ctx.params;

        // Soft delete tất cả địa chỉ của user
        await this.adapter.updateMany(
          { userId },
          { isDeleted: true }
        );
      }
    }
  },

  async started() {
    this.logger.info('Addresses service started');
  },

  async stopped() {
    this.logger.info('Addresses service stopped');
  }
};
