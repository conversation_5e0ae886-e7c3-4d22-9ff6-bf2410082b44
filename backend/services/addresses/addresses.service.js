const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./addresses.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'addresses',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },
  hooks: {},
  actions: {},
  methods: {},
  events: {},
};
