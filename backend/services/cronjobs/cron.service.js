const CronJob = require('moleculer-cron');
const { CronTime } = require('cron');

/** @type {ServiceSchema} */
module.exports = {
  name: 'cronjobs',
  mixins: [CronJob],
  dependencies: ['settings'],
  settings: {
    runOnInit: true,
    cronJobs: [
      // {
      //   // Craw Ophim website every day at 0:35 AM
      //   name: 'crawMovieDataOphim',
      //   cronTime: '35 0 * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job craw Ophim started at ${new Date().toISOString()}`);
      //       await this.broker.emit('movie.craw', {domain: 'ophim'});
      //     } catch (error) {
      //       this.logger.info(`Job craw OPhim started with error: ${error.message}`);
      //     }
      //   },
      // },
    ],
  },

  actions: {},
  methods: {},
  events: {},
  created() {
  },

  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
