const { MoleculerClientError } = require('moleculer').Errors;
const i18next = require('i18next');

module.exports = {
  name: 'payments',
  mixins: [],
  settings: {
    supportedMethods: ['cod', 'vnpay', 'vnptpay', 'bank_transfer', 'credit_card']
  },

  actions: {
    // Tạo payment cho đơn hàng
    createPayment: {
      rest: 'POST /create',
      auth: 'required',
      params: {
        orderId: 'string',
        paymentMethod: { type: 'enum', values: ['cod', 'vnpay', 'vnptpay', 'bank_transfer', 'credit_card'] },
        returnUrl: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { orderId, paymentMethod, returnUrl } = ctx.params;
        const userId = ctx.meta.user._id;

        // Lấy thông tin đơn hàng
        const order = await ctx.call('orders.get', { id: orderId });
        if (!order || order.userId.toString() !== userId.toString()) {
          throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
        }

        if (order.paymentStatus === 'paid') {
          throw new MoleculerClientError('Đơn hàng đã được thanh toán', 400);
        }

        // Tạo transaction record
        const transaction = await ctx.call('transactions.create', {
          orderId,
          userId,
          amount: order.total,
          paymentMethod,
          status: 'pending'
        });

        let paymentResult = {
          transactionId: transaction._id,
          paymentMethod,
          amount: order.total,
          status: 'pending'
        };

        // Xử lý theo từng phương thức thanh toán
        switch (paymentMethod) {
          case 'cod':
            // COD không cần xử lý gì thêm
            paymentResult.message = 'Thanh toán khi nhận hàng';
            break;

          case 'vnpay':
            const vnpayResult = await ctx.call('vnpay.createPaymentUrl', {
              order: {
                id: orderId,
                amount: order.total,
                currency: 'VND',
                type: 'other'
              },
              customer: {
                id: userId
              },
              transactionId: transaction._id
            });
            
            paymentResult.paymentUrl = vnpayResult.paymentUrl;
            paymentResult.expireDate = vnpayResult.vnpExpireDate;
            break;

          case 'vnptpay':
            // Tương tự VNPay
            const vnptpayResult = await ctx.call('vnptpay.createPaymentUrl', {
              order: {
                id: orderId,
                amount: order.total,
                currency: 'VND'
              },
              customer: {
                id: userId
              },
              transactionId: transaction._id
            });
            
            paymentResult.paymentUrl = vnptpayResult.paymentUrl;
            break;

          case 'bank_transfer':
            // Thông tin chuyển khoản ngân hàng
            paymentResult.bankInfo = await this.getBankTransferInfo();
            paymentResult.transferCode = `ORDER${orderId}`;
            break;

          case 'credit_card':
            throw new MoleculerClientError('Phương thức thanh toán chưa được hỗ trợ', 400);

          default:
            throw new MoleculerClientError('Phương thức thanh toán không hợp lệ', 400);
        }

        return paymentResult;
      }
    },

    // Xác nhận thanh toán (cho COD, bank transfer)
    confirmPayment: {
      rest: 'POST /:transactionId/confirm',
      admin: true,
      params: {
        transactionId: 'string',
        note: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { transactionId, note } = ctx.params;

        const transaction = await ctx.call('transactions.get', { id: transactionId });
        if (!transaction) {
          throw new MoleculerClientError('Giao dịch không tồn tại', 404);
        }

        if (transaction.status === 'completed') {
          throw new MoleculerClientError('Giao dịch đã được xác nhận', 400);
        }

        // Cập nhật transaction
        await ctx.call('transactions.update', {
          id: transactionId,
          status: 'completed',
          completedAt: new Date(),
          note
        });

        // Cập nhật order payment status
        await ctx.call('orders.updatePaymentStatus', {
          orderId: transaction.orderId,
          paymentStatus: 'paid',
          transactionId
        });

        // Emit event
        this.broker.emit('payment.success', {
          orderId: transaction.orderId,
          transactionId,
          amount: transaction.amount,
          paymentMethod: transaction.paymentMethod
        });

        return { success: true, message: 'Đã xác nhận thanh toán' };
      }
    },

    // Hủy thanh toán
    cancelPayment: {
      rest: 'POST /:transactionId/cancel',
      auth: 'required',
      params: {
        transactionId: 'string',
        reason: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { transactionId, reason } = ctx.params;
        const userId = ctx.meta.user._id;

        const transaction = await ctx.call('transactions.get', { id: transactionId });
        if (!transaction || transaction.userId.toString() !== userId.toString()) {
          throw new MoleculerClientError('Giao dịch không tồn tại', 404);
        }

        if (transaction.status === 'completed') {
          throw new MoleculerClientError('Không thể hủy giao dịch đã hoàn thành', 400);
        }

        // Cập nhật transaction
        await ctx.call('transactions.update', {
          id: transactionId,
          status: 'cancelled',
          cancelledAt: new Date(),
          note: reason
        });

        // Emit event
        this.broker.emit('payment.cancelled', {
          orderId: transaction.orderId,
          transactionId,
          reason
        });

        return { success: true, message: 'Đã hủy thanh toán' };
      }
    },

    // Lấy lịch sử giao dịch của user
    getMyTransactions: {
      rest: 'GET /my-transactions',
      auth: 'required',
      params: {
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 10 },
        status: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const { page, limit, status } = ctx.params;

        const query = { userId };
        if (status) {
          query.status = status;
        }

        return await ctx.call('transactions.list', {
          page,
          limit,
          query: JSON.stringify(query),
          sort: '-createdAt',
          populate: ['orderId']
        });
      }
    },

    // Webhook xử lý kết quả thanh toán
    handleWebhook: {
      rest: 'POST /webhook/:provider',
      params: {
        provider: { type: 'enum', values: ['vnpay', 'vnptpay'] }
      },
      async handler(ctx) {
        const { provider } = ctx.params;

        try {
          let result;
          
          switch (provider) {
            case 'vnpay':
              result = await ctx.call('vnpay.ipnURL', ctx.params);
              break;
            case 'vnptpay':
              result = await ctx.call('vnptpay.ipnURL', ctx.params);
              break;
            default:
              throw new MoleculerClientError('Provider không được hỗ trợ', 400);
          }

          return result;
        } catch (error) {
          this.logger.error('Webhook error:', error);
          throw error;
        }
      }
    }
  },

  methods: {
    // Lấy thông tin chuyển khoản ngân hàng
    getBankTransferInfo() {
      return {
        bankName: 'Ngân hàng TMCP Á Châu (ACB)',
        accountNumber: '*********',
        accountName: 'CONG TY ABC',
        branch: 'Chi nhánh Hà Nội',
        note: 'Vui lòng ghi mã đơn hàng vào nội dung chuyển khoản'
      };
    }
  },

  events: {
    // Xử lý kết quả từ VNPay/VNPTPay
    'transactionUpdateState': {
      async handler(ctx) {
        const { transactionId, state, responseCode, responseMessage } = ctx.params;

        if (!transactionId) return;

        const transaction = await ctx.call('transactions.get', { id: transactionId });
        if (!transaction) return;

        const status = state === 'done' ? 'completed' : 'failed';
        
        // Cập nhật transaction
        await ctx.call('transactions.update', {
          id: transactionId,
          status,
          responseCode,
          responseMessage,
          completedAt: status === 'completed' ? new Date() : null
        });

        if (status === 'completed') {
          // Cập nhật order payment status
          await ctx.call('orders.updatePaymentStatus', {
            orderId: transaction.orderId,
            paymentStatus: 'paid',
            transactionId
          });

          // Emit success event
          this.broker.emit('payment.success', {
            orderId: transaction.orderId,
            transactionId,
            amount: transaction.amount,
            paymentMethod: transaction.paymentMethod
          });
        } else {
          // Emit failed event
          this.broker.emit('payment.failed', {
            orderId: transaction.orderId,
            transactionId,
            reason: responseMessage
          });
        }
      }
    }
  },

  async started() {
    this.logger.info('Payments service started');
  },

  async stopped() {
    this.logger.info('Payments service stopped');
  }
};
