const dayjs = require('dayjs');
const CryptoJS = require('crypto-js');
const VnptPayTransaction = require('./transaction.model');
const VnptLog = require('./log.model');
const axios = require('axios');

module.exports = {
  name: 'vnptpay',
  mixins: [],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: [],
    vnptPayConfig: {
      // Config for VNPT Pay
      vnptMerchant: process.env.VNPT_MERCHANT || 'Clickee',
      vnptMerchantId: process.env.VNPT_MERCHANT_ID || '2427',
      vnptMerchantService: process.env.VNPT_MERCHANT_SERVICE || 'Clickee',
      vnptMerchantServiceId: process.env.VNPT_MERCHANT_SERVICE_ID || '6072',
      vnptBaseUrl: process.env.VNPT_BASE_URL || 'https://sandboxpaydev.vnptmedia.vn/rest/payment/v1.0.6/',
      vnptApiKey: process.env.VNPT_API_KEY || '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      vnptSecretKey: process.env.VNPT_SECRET_KEY || 'afc2d760c47c22979fa98f90d7667db6',

      // Config for VNPT QR
      vnptQrBaseUrl: process.env.VNPT_QR_BASE_URL || 'https://sandboxpaydev.vnptmedia.vn/rest/vietqr/merchant/1.0.0/',
      vnptQrApiKey: process.env.VNPT_QR_API_KEY || '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      vnptQrSecretKey: process.env.VNPT_QR_SECRET_KEY || '85604cb96aaf6653c5e36d42edbcc461',
      vnptQrMerchantClientId: process.env.VNPT_QR_MERCHANT_CLIENT_ID || '31618',
      vnptQrMerchantCode: process.env.VNPT_QR_MERCHANT_CODE || '21574',
      vnptQrMerchantName: process.env.VNPT_QR_MERCHANT_NAME || 'Clickee',
      vnptQrTerminalId: process.env.VNPT_QR_TERMINAL_ID || '12413',
    },
  },

  hooks: {},

  actions: {
    createPaymentUrl: {
      // rest: "POST /createPaymentUrl",
      params: {
        order: 'object',
        transactionId: 'string',
      },
      async handler(ctx) {
        const { order, transactionId } = ctx.params;
        const date = new Date();
        const startDate = this.formatDate(date);
        const { vnptBaseUrl, vnptMerchantServiceId, vnptSecretKey, vnptApiKey } = this.settings.vnptPayConfig;
        const orderCode = this.getOrderCode();

        const vnptPayParams = {
          ACTION: 'PAY',
          VERSION: '1.0.6',
          MERCHANT_SERVICE_ID: vnptMerchantServiceId, // VNPTPAY cấp
          MERCHANT_ORDER_ID: orderCode, // Mã đơn hàng
          AMOUNT: order.amount,
          PAYMENT_ACTION: 'PAY', // PAY: Thanh toán thông thường
          SERVICE_CATEGORY: '90', // 90: Dịch vụ thanh toán thông thường
          CHANNEL_ID: '1', // Mã kênh thanh toán, mặc định = 1
          DEVICE: '1', // Mã thiết bị, mặc định = 1
          LOCALE: order.locale || 'vi-VN',
          CURRENCY_CODE: order.currency || 'VND',
          PAYMENT_METHOD: 'VNPTPAY', // Phương thức thanh toán, mặc định: VNPTPAY
          DESCRIPTION: `Thanh toan don hang ${order.service}`, // Mô tả giao dịch
          CREATE_DATE: startDate, // Thời gian giao dịch
          CLIENT_IP: ctx.meta.client_ip, // IP của khách hàng
        };

        vnptPayParams['SECURE_CODE'] = this.genHash(vnptPayParams, vnptSecretKey); // Chữ ký của bản tin

        const vnptPayUrl = vnptBaseUrl + 'pay';

        const response = await this.processVnptApiRequest(ctx, vnptPayUrl, vnptPayParams, vnptApiKey, order, orderCode, transactionId, 'vnptpay');
        if ( response.error ) return { paymentUrl: '', orderCode: orderCode };

        const { SECURE_CODE: secureCode, ...responseData } = response.data;
        const checksum = this.checkSum(responseData, vnptSecretKey, secureCode);

        if ( response.data.RESPONSE_CODE !== '00' || !checksum ) {
          return { paymentUrl: '', orderCode: orderCode };
        }

        return { paymentUrl: response.data.REDIRECT_URL, orderCode: orderCode };
      }
    },
    returnURL: {
      rest: 'POST /returnUrl',
      async handler(ctx) {
        const params = ctx.params;
        const { vnptSecretKey } = this.settings.vnptPayConfig;
        const secureCode = params.secureCode;
        delete params.secureCode;

        let signed = this.genHash(params, vnptSecretKey);

        if ( secureCode === signed ) {
          return { code: params.vnptpayResponseCode };
        } else {
          return { code: '97' };
        }
      },
    },
    ipnURL: {
      rest: 'POST /ipn',
      async handler(ctx) {
        const params = ctx.params.data ? JSON.parse(ctx.params.data) : {};

        const { vnptSecretKey } = this.settings.vnptPayConfig;
        const secureCode = params.SECURE_CODE;
        delete params.SECURE_CODE;

        try {
          const signed = this.genHash({
            ACTION: params.ACTION,
            RESPONSE_CODE: params.RESPONSE_CODE,
            MERCHANT_SERVICE_ID: params.MERCHANT_SERVICE_ID,
            MERCHANT_ORDER_ID: params.MERCHANT_ORDER_ID,
            AMOUNT: params.AMOUNT,
            CURRENCY_CODE: params.CURRENCY_CODE,
            VNPTPAY_TRANSACTION_ID: params.VNPTPAY_TRANSACTION_ID,
            PAYMENT_METHOD: params.PAYMENT_METHOD,
            PAY_DATE: params.PAY_DATE,
            ADDITIONAL_INFO: params.ADDITIONAL_INFO,
            TOKEN: params.TOKEN,
            ACCOUNT_ID: params.ACCOUNT_ID,
            CARD_EXPIRY_YEAR: params.CARD_EXPIRY_YEAR,
            CARD_EXPIRY_MONTH: params.CARD_EXPIRY_MONTH,
          }, vnptSecretKey);

          const transaction = await VnptPayTransaction.findOne({ orderCode: params.MERCHANT_ORDER_ID });

          if ( !transaction ) return this.handlePaymentResponse(ctx, transaction, params, '01', 'Order not found');
          if ( secureCode !== signed ) return this.handlePaymentResponse(ctx, transaction, params, '97', 'Fail checksum');

          if ( transaction.amount !== Number(params.AMOUNT) ) return this.handlePaymentResponse(ctx, transaction, params, '99', 'Invalid amount');
          if ( transaction.status !== 0 ) return this.handlePaymentResponse(ctx, transaction, params, '02', 'Order already confirmed');

          const status = params.RESPONSE_CODE === '00' ? 1 : 2;
          const message = params.RESPONSE_CODE === '00' ? 'Confirm Success' : 'Confirm Fail';
          await VnptPayTransaction.findByIdAndUpdate(transaction._id, { status });

          return this.handlePaymentResponse(ctx, transaction, params, params.RESPONSE_CODE, message);
        } catch ( error ) {
          return this.handlePaymentResponse(ctx, null, params, '99', 'Unknown error');
        }
      }
    },

    createQRCode: {
      // rest: "POST /create_vietqr",
      async handler(ctx) {
        const { order, transactionId } = ctx.params;
        const date = new Date();
        const endDate = this.formatDate(dayjs(date).add(30, 'minute')); // Hết hạn sau 30 phút
        const orderCode = this.getOrderCode();
        const {
          vnptQrBaseUrl,
          vnptQrSecretKey,
          vnptQrMerchantClientId,
          vnptQrMerchantCode,
          vnptQrMerchantName,
          vnptQrTerminalId,
          vnptQrApiKey
        } = this.settings.vnptPayConfig;

        const vnptQrParams = {
          merchantClientId: vnptQrMerchantClientId, // do VNPTPAY cấp
          merchantName: vnptQrMerchantName, // do VNPTPAY cấp
          countryCode: 'VN', // Mã quốc gia: default VN
          merchantCode: vnptQrMerchantCode, // do VNPTPAY cấp
          terminalId: vnptQrTerminalId, // do VNPTPAY cấp
          qrCodeType: '01', // Loại QR 01: QR cho các điểm Offline, 02: QR thanh toán Hóa đơn
          txnId: '', // Mã giao dịch
          billNumber: orderCode, //Số hóa đơn, biên lai. Bắt buộc đối với QR động ( duy nhất mỗi lần tạo mã QR )
          amount: order.amount, // Số tiền thanh toán
          ccy: '704', // Mã tiền tệ : Giá trị mặc định 704
          expDate: endDate, //Thời gian hết hạn thanh toán
          mcc: '0', // Loại hình doanh nghiệp. Giá trị mặc định để "0"
          tipAndFee: '', // Tiền tip and fee. Giá trị mặc định để empty
          consumerId: '', //Mã khách hàng, SĐT của khách hàng dùng để truy vấn/gạch nợ hóa đơn.Bắt buộc đối với QrType = 02
          purpose: 'Thanh toan QRCode', //Nội dung thanh toán
        };
        vnptQrParams['checksum'] = this.genHash(vnptQrParams, vnptQrSecretKey);

        const vnptPayQrUrl = vnptQrBaseUrl + 'create_vietqr';

        const response = await this.processVnptApiRequest(ctx, vnptPayQrUrl, vnptQrParams, vnptQrApiKey, order, orderCode, transactionId, 'vnptqr');
        if ( response.error ) return { paymentUrl: '', orderCode: orderCode };

        const resultData = {
          responseCode: response.data.responseCode,
          description: response.data.description,
          qrcodeData: response.data.qrcodeData,
          qrcodeId: response.data.qrcodeId,
          totalAmount: response.data.totalAmount,
          originalAmount: response.data.originalAmount,
          fee: response.data.fee,
          createDate: response.data.createDate
        };
        const checksum = this.checkSum(resultData, vnptQrSecretKey, response.data.checksum);

        if ( response.data.responseCode !== '00' || !checksum || Number(response.data.totalAmount) !== Number(order.amount) ) {
          return { qrcodeImage: '', orderCode: orderCode };
        }

        return {
          qrcodeData: response.data.qrcodeData,
          qrcodeId: response.data.qrcodeId,
          totalAmount: response.data.totalAmount,
          originalAmount: response.data.originalAmount,
          fee: response.data.fee,
          qrcodeImage: response.data.qrcodeImage,
          orderCode: orderCode
        };
      }
    },
    ipnQrUrl: {
      rest: 'POST /ipnQr',
      async handler(ctx) {
        const params = ctx.params;
        const {
          vnptQrSecretKey,
          vnpt_Whitelist,
          vnptQrMerchantClientId,
          vnptQrMerchantCode,
          vnptQrTerminalId
        } = this.settings.vnptPayConfig;
        const secureCode = params.checksum;
        delete params.checksum;
        delete params.extraData;

        try {
          const signed = this.genHash({
            responseCode: params.responseCode,
            description: params.description,
            merchantClientId: params.merchantClientId,
            merchantCode: params.merchantCode,
            terminalId: params.terminalId,
            billNumber: params.billNumber,
            txnId: params.txnId,
            msgType: params.msgType,
            customerName: params.customerName,
            accountNo: params.accountNo,
            mobile: params.mobile,
            amount: params.amount,
            ccy: params.ccy,
            qrcodeType: params.qrcodeType,
            qrTxnId: params.qrTxnId,
            paymentMethod: params.paymentMethod,
            payDate: params.payDate,
            additionalInfo: params.additionalInfo
          }, vnptQrSecretKey);
          const transaction = await VnptPayTransaction.findOne({ orderCode: params.billNumber });

          if ( !transaction ) return this.handleQrResponse(ctx, transaction, '01', 'Order not found');
          if ( secureCode !== signed ) return this.handleQrResponse(ctx, transaction, '07', 'Fail checksum');

          if ( transaction.amount !== Number(params.amount) ) return this.handleQrResponse(ctx, transaction, '99', 'Invalid amount');
          if ( transaction.status !== 0 ) return this.handleQrResponse(ctx, transaction, '02', 'Order already confirmed');
          if ( params.merchantClientId !== vnptQrMerchantClientId || params.merchantCode !== vnptQrMerchantCode || params.terminalId !== vnptQrTerminalId ) return this.handleQrResponse(ctx, transaction, '99', 'Invalid merchant');

          const status = params.responseCode === '00' ? 1 : 2;
          const message = params.responseCode === '00' ? 'Confirm Success' : 'Confirm Fail';
          await VnptPayTransaction.findByIdAndUpdate(transaction._id, { status });
          return this.handleQrResponse(ctx, transaction, params.responseCode, message);
        } catch ( error ) {
          return this.handleQrResponse(ctx, null, '99', 'Unknown error');
        }
      }
    },
    checkQrOrder: {
      rest: 'POST /checkOrder',
      async handler(ctx) {
        const params = ctx.params;
        const {
          vnptQrSecretKey,
          vnptQrMerchantClientId,
          vnptQrMerchantCode,
          vnptQrTerminalId
        } = this.settings.vnptPayConfig;
        const secureCode = params.checksum;
        delete params.checksum;

        try {
          const signed = this.genHash({
            merchantClientId: params.merchantClientId,
            merchantCode: params.merchantCode,
            terminalId: params.terminalId,
            billNumber: params.billNumber,
            txnId: params.txnId,
            virtualAccount: params.virtualAccount,
            amount: params.amount,
            createDate: params.createDate,
          }, vnptQrSecretKey);
          const transaction = await VnptPayTransaction.findOne({ orderCode: params.billNumber });

          if ( !transaction ) return this.handleCheckQrOrder(ctx, transaction, params, '01', 'Order not found');
          if ( secureCode !== signed ) return this.handleCheckQrOrder(ctx, transaction, params, '07', 'Fail checksum');
          if ( transaction.amount !== Number(params.amount) ) return this.handleCheckQrOrder(ctx, transaction, params, '99', 'Invalid amount');
          if ( transaction.status !== 0 ) return this.handleCheckQrOrder(ctx, transaction, params, '02', 'Order already confirmed');
          if ( params.merchantClientId !== vnptQrMerchantClientId || params.merchantCode !== vnptQrMerchantCode || params.terminalId !== vnptQrTerminalId ) return this.handleCheckQrOrder(ctx, transaction, params, '99', 'Invalid merchant');

          return this.handleCheckQrOrder(ctx, transaction, params, '00', 'Success');
        } catch ( e ) {
          return this.handleCheckQrOrder(ctx, null, params, '99', 'Unknown error');
        }
      }
    },
  },
  methods: {
    async processVnptApiRequest(ctx, apiUrl, requestParams, apiKey, order, orderCode, transactionId, transactionType) {
      try {
        const response = await axios.post(apiUrl, requestParams, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        });
        const responseCodeField = transactionType === 'vnptqr' ? 'responseCode' : 'RESPONSE_CODE';
        const createLog = await VnptLog.create({
          url: apiUrl,
          ip: ctx.meta.client_ip,
          type: 1,
          responseCode: response.data[responseCodeField],
        });

        const transactionPayload = {
          date: new Date(),
          amount: order.amount,
          orderCode: orderCode,
          transactionId,
          type: transactionType,
          paymentLogId: createLog._id,
        };
        await VnptPayTransaction.create(transactionPayload);

        return { data: response.data, status: response.status };
      } catch ( err ) {
        await VnptLog.create({
          url: apiUrl,
          ip: ctx.meta.client_ip,
          type: 1,
          responseCode: err.response.status,
        });

        return { error: err.response.data, status: err.response.status };
      }
    },
    async handlePaymentResponse(ctx, transaction, params, rspCode, message) {
      const state = rspCode === '00' ? 'done' : 'error';

      if ( transaction ) {
        await this.broker.emit('transactionUpdateState', {
          transactionId: transaction?.transactionId?.toString(),
          state: state,
          responseCode: rspCode,
          responseMessage: message,
        });
      }

      const resultParams = {
        RESPONSE_CODE: rspCode,
        DESCRIPTION: message,
        MERCHANT_SERVICE_ID: params.MERCHANT_SERVICE_ID,
        MERCHANT_ORDER_ID: params.MERCHANT_ORDER_ID,
        CREATE_DATE: params.CREATE_DATE,
      };
      resultParams['SECURE_CODE'] = this.genHash(resultParams, this.settings.vnptPayConfig.vnptSecretKey);

      await VnptLog.create({
        url: ctx?.options?.parentCtx?.params?.req?.originalUrl,
        ip: ctx.meta.client_ip,
        type: 2,
        responseCode: rspCode,
      });

      return resultParams;
    },
    async handleQrResponse(ctx, transaction, rspCode, message) {
      const state = rspCode === '00' ? 'done' : 'error';
      if ( transaction ) {
        await this.broker.emit('transactionUpdateState', {
          transactionId: transaction?.transactionId?.toString(),
          state: state,
          responseCode: rspCode,
          responseMessage: message,
        });
      }

      await VnptLog.create({
        url: ctx?.options?.parentCtx?.params?.req?.originalUrl,
        ip: ctx.meta.client_ip,
        type: 2,
        responseCode: rspCode,
      });

      return {
        responseCode: rspCode,
        description: message,
        data: null,
      };
    },
    async handleCheckQrOrder(ctx, transaction, params, rspCode, message) {
      const state = rspCode === '00' ? 'done' : 'error';

      if ( transaction ) {
        await this.broker.emit('transactionUpdateState', {
          transactionId: transaction?.transactionId?.toString(),
          state: state,
          responseCode: rspCode,
          responseMessage: message,
        });
      }

      const resultParams = {
        responseCode: rspCode,
        description: message,
        merchantClientId: params.merchantClientId,
        merchantCode: params.merchantCode,
        terminalId: params.terminalId,
        billNumber: params.billNumber,
        txnId: params.txnId,
        amount: params.amount,
        createDate: params.createDate,
      };

      resultParams['checksum'] = this.genHash(resultParams, this.settings.vnptPayConfig.vnptSecretKey);

      await VnptLog.create({
        url: ctx?.options?.parentCtx?.params?.req?.originalUrl,
        ip: ctx.meta.client_ip,
        type: 3,
        responseCode: rspCode,
      });

      return resultParams;
    },
    formatDate(date) {
      return dayjs(date).format('YYYYMMDDHHmmss');
    },
    getOrderCode() {
      return `${dayjs().format('YYYYMMDD')}${Math.random().toString(36).substr(2, 15)}`;
    },
    genHash(data, secretKey) {
      const hashData = Object.values(data)
        .filter(value => value !== null && value !== undefined)
        .join('|') + '|' + secretKey;
      return CryptoJS.SHA256(hashData).toString(CryptoJS.enc.Hex);
    },
    checkSum(data, hashSecret, hash) {
      const hashCheck = this.genHash(data, hashSecret);
      return hashCheck === hash;
    },
  },
  events: {},
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};


