const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { PAY_LOG } = require('../../../constants/dbCollections');

let schema = new Schema({
  paymentGate: { type: String, required: true },
  url: { type: String, required: true },
  ip: { type: String },
  /**
   * 1: create payment
   * 2: ipn url
   * 3: check order qr code
   */
  type: { type: Number, required: true },
  is_deleted: { type: Boolean, default: false },
  responseCode: { type: String },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.virtual('params').get(function () {
  const urlParmas = new URLSearchParams(this.url.split('?')[1]);
  return Object.fromEntries(urlParmas.entries());
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(PAY_LOG, schema, PAY_LOG);
