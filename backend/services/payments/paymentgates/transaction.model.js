'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {TRANSACTION, PAY_LOG, PAY_TRANSACTION} = require('../../../constants/dbCollections');
let schema = new Schema({
  orderCode: {type: String, required: true},
  type: {type: String, enum: ['vnptpay', 'vnptqr', 'vnpay'], required: true},
  date: {type: Date, required: true},
  amount: {type: Number, required: true},
  txnRef: {type: String, required: true},
  /**
   * 0: default
   * 1: payment success
   * 2: payment failed/error
   */
  status: {type: Number, required: true, default: 0},
  logId: {type: Schema.Types.ObjectId, ref: PAY_LOG},
  transactionId: {type: Schema.Types.ObjectId, ref: TRANSACTION},
  is_deleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});
module.exports = mongoose.model(PAY_TRANSACTION, schema, PAY_TRANSACTION);
