const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./transactions.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const { MoleculerClientError } = require('moleculer').Errors;

module.exports = {
  name: 'transactions',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      userId: 'users',
      orderId: 'orders'
    },
    populateOptions: ['userId', 'orderId'],
  },

  hooks: {
    before: {
      create: async function(ctx) {
        // Generate transaction number
        ctx.params.transactionNumber = await this.generateTransactionNumber();
      }
    }
  },

  actions: {
    // Lấy giao dịch theo order
    getByOrder: {
      params: {
        orderId: 'string'
      },
      async handler(ctx) {
        const { orderId } = ctx.params;

        return await this.adapter.find({
          query: { orderId },
          sort: { createdAt: -1 },
          populate: this.settings.populateOptions
        });
      }
    },

    // Lấy thống kê giao dịch
    getStats: {
      rest: 'GET /stats',
      admin: true,
      params: {
        startDate: { type: 'string', optional: true },
        endDate: { type: 'string', optional: true },
        paymentMethod: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { startDate, endDate, paymentMethod } = ctx.params;

        const matchQuery = {};

        if (startDate || endDate) {
          matchQuery.createdAt = {};
          if (startDate) matchQuery.createdAt.$gte = new Date(startDate);
          if (endDate) matchQuery.createdAt.$lte = new Date(endDate);
        }

        if (paymentMethod) {
          matchQuery.paymentMethod = paymentMethod;
        }

        const pipeline = [
          { $match: matchQuery },
          {
            $group: {
              _id: {
                status: '$status',
                paymentMethod: '$paymentMethod'
              },
              count: { $sum: 1 },
              totalAmount: { $sum: '$amount' }
            }
          },
          {
            $group: {
              _id: '$_id.paymentMethod',
              stats: {
                $push: {
                  status: '$_id.status',
                  count: '$count',
                  totalAmount: '$totalAmount'
                }
              },
              totalTransactions: { $sum: '$count' },
              totalAmount: { $sum: '$totalAmount' }
            }
          }
        ];

        const result = await this.adapter.model.aggregate(pipeline);

        // Tính tổng toàn bộ
        const overallStats = {
          totalTransactions: 0,
          totalAmount: 0,
          completedTransactions: 0,
          completedAmount: 0,
          pendingTransactions: 0,
          failedTransactions: 0
        };

        result.forEach(method => {
          overallStats.totalTransactions += method.totalTransactions;
          overallStats.totalAmount += method.totalAmount;

          method.stats.forEach(stat => {
            if (stat.status === 'completed') {
              overallStats.completedTransactions += stat.count;
              overallStats.completedAmount += stat.totalAmount;
            } else if (stat.status === 'pending') {
              overallStats.pendingTransactions += stat.count;
            } else if (stat.status === 'failed') {
              overallStats.failedTransactions += stat.count;
            }
          });
        });

        return {
          overall: overallStats,
          byPaymentMethod: result
        };
      }
    },

    // Refund giao dịch
    refund: {
      rest: 'POST /:id/refund',
      admin: true,
      params: {
        id: 'string',
        amount: { type: 'number', optional: true }, // Partial refund
        reason: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { id, amount, reason } = ctx.params;

        const transaction = await this.adapter.findById(id);
        if (!transaction) {
          throw new MoleculerClientError('Giao dịch không tồn tại', 404);
        }

        if (transaction.status !== 'completed') {
          throw new MoleculerClientError('Chỉ có thể hoàn tiền giao dịch đã hoàn thành', 400);
        }

        const refundAmount = amount || transaction.amount;
        if (refundAmount > transaction.amount) {
          throw new MoleculerClientError('Số tiền hoàn không thể lớn hơn số tiền giao dịch', 400);
        }

        // Tạo refund transaction
        const refundTransaction = await this.adapter.insert({
          transactionNumber: await this.generateTransactionNumber(),
          userId: transaction.userId,
          orderId: transaction.orderId,
          amount: -refundAmount, // Negative amount for refund
          paymentMethod: transaction.paymentMethod,
          status: 'completed',
          type: 'refund',
          parentTransactionId: transaction._id,
          note: reason,
          completedAt: new Date()
        });

        // Cập nhật order payment status
        const isFullRefund = refundAmount === transaction.amount;
        await ctx.call('orders.updatePaymentStatus', {
          orderId: transaction.orderId,
          paymentStatus: isFullRefund ? 'refunded' : 'partially_refunded'
        });

        // Emit event
        this.broker.emit('payment.refunded', {
          orderId: transaction.orderId,
          originalTransactionId: transaction._id,
          refundTransactionId: refundTransaction._id,
          refundAmount,
          isFullRefund
        });

        return refundTransaction;
      }
    }
  },

  methods: {
    // Generate transaction number
    async generateTransactionNumber() {
      const today = new Date();
      const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
      
      const count = await this.adapter.count({
        transactionNumber: { $regex: `^TXN${dateStr}` }
      });
      
      return `TXN${dateStr}${String(count + 1).padStart(6, '0')}`;
    }
  },

  events: {
    // Khi có order mới được tạo
    'order.created': {
      async handler(ctx) {
        const order = ctx.params;
        
        // Tạo transaction record cho COD
        if (order.paymentMethod === 'cod') {
          await this.adapter.insert({
            transactionNumber: await this.generateTransactionNumber(),
            userId: order.userId,
            orderId: order._id,
            amount: order.total,
            paymentMethod: 'cod',
            status: 'pending',
            type: 'payment'
          });
        }
      }
    }
  },

  async started() {
    this.logger.info('Transactions service started');
  },

  async stopped() {
    this.logger.info('Transactions service stopped');
  }
};
