const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { TRANSACTION, USER, ORDER } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  transactionNumber: { type: String, required: true, unique: true, index: true },
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },
  orderId: { type: Schema.Types.ObjectId, ref: ORDER, required: true },
  
  // Thông tin giao dịch
  amount: { type: Number, required: true }, // Số tiền (âm cho refund)
  paymentMethod: {
    type: String,
    enum: ['cod', 'vnpay', 'vnptpay', 'bank_transfer', 'credit_card'],
    required: true
  },
  
  // Trạng thái
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  
  // Loại giao dịch
  type: {
    type: String,
    enum: ['payment', 'refund'],
    default: 'payment'
  },
  
  // Thông tin gateway
  gatewayTransactionId: String, // ID từ VNPay, VNPTPay, etc.
  responseCode: String,
  responseMessage: String,
  
  // Refund info
  parentTransactionId: { type: Schema.Types.ObjectId, ref: TRANSACTION }, // For refund transactions
  
  // Timestamps
  completedAt: Date,
  cancelledAt: Date,
  
  // Ghi chú
  note: String,
  
  isDeleted: { type: Boolean, default: false }
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Indexes
schema.index({ userId: 1, createdAt: -1 });
schema.index({ orderId: 1 });
schema.index({ status: 1, paymentMethod: 1 });
schema.index({ gatewayTransactionId: 1 });

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(TRANSACTION, schema, TRANSACTION);
