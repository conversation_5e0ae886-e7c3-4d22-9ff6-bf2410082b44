const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./files.model');
const BaseService = require('../../mixins/baseService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const FileMixin = require('../../mixins/file.mixin');
const path = require('path');
const fs = require('fs');
const i18next = require('i18next');
const storageDir = path.join(__dirname, 'storage');
const { MoleculerClientError } = require('moleculer').Errors;
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'files',
  mixins: [DbMongoose(MODEL), BaseService, FunctionsCommon, FileMixin, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  hooks: {},

  actions: {
    upload: {
      admin: true,
      async handler(ctx) {
        const { filename, mimetype } = ctx.meta;
        const uniqueFileName = this.createUniqueFileName(filename);
        const { folder } = ctx.meta.$multipart;

        await this.save(ctx.params, uniqueFileName, folder);
        const filePath = this.getFilePath(uniqueFileName, this.getDirPath(folder, this.getStoragePath()));
        let stat = fs.statSync(filePath);

        const fileObject = {
          name: uniqueFileName,
          displayName: ctx.meta.displayName || filename,
          size: stat.size,
          mimetype: mimetype,
          storageType: 'local_storage',
          storageLocation: folder,
        };
        return await this.adapter.insert(fileObject);
      },
    },
    uploadImage: {
      rest: 'POST /url',
      admin: true,
      async handler(ctx) {
        try {
          const { url, folder } = ctx.params;

          const filename = path.basename(url, path.extname(url));
          const uniqueFileName = filename + '.webp';
          const filePath = this.getFilePath(uniqueFileName, this.getDirPath(folder, this.getStoragePath()));

          await this.saveImage(url, filePath);
          let stat = fs.statSync(filePath);

          const fileObject = {
            name: uniqueFileName,
            displayName: filename,
            size: stat.size,
            mimetype: 'image/webp',
            storageType: 'local_storage',
            storageLocation: folder,
          };
          return await this.adapter.insert(fileObject);
        } catch ( error ) {
          console.log(error.message);
        }
      },
    },
    uploadVideo: {
      rest: 'POST /video-url',
      admin: true,
      async handler(ctx) {
        const { videoUrl, filename, movieId } = ctx.params;
        const uniqueFileName = this.createUniqueFileName(filename);
        const outputFilePath = this.getFilePath(uniqueFileName, this.getDirPath(folder, this.getStoragePath()));

        const file = await this.adapter.insert({
          name: uniqueWebpFileName,
          displayName: filename,
          mimetype: 'image/webp',
          storageType: 'local_storage',
          storageLocation: 'video',
          movieId: movieId,
        });
        this.saveVideo(videoUrl, outputFilePath, file);

        return file;
      },
    },
    save: {
      auth: 'required',
      admin: true,
      async handler(ctx) {
        const { filename, mimetype, folder } = ctx.meta;
        await this.save(ctx.params, filename, folder);
        const filePath = this.getFilePath(filename, this.getDirPath(folder, this.getStoragePath()));
        let stat = fs.statSync(filePath);

        const fileObject = {
          ownerId: userID,
          name: filename,
          displayName: ctx.meta.displayName || filename,
          size: stat.size,
          mimetype: mimetype,
          storageType: 'local_storage',
          storageLocation: folder,
        };
        return await this.adapter.insert(fileObject);
      },
    },
    stream: {
      rest: 'GET /content/:id',
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const file = await this.adapter.findById(id);
          if ( !file ) return new MoleculerClientError(i18next.t('error_data_not_found'), 404);
          const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, this.getStoragePath()));
          if ( !fs.existsSync(filePath) ) return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            'Content-Type': file.mimetype,
            'Content-Length': stat.size,
            'Content-Disposition': 'attachment;filename=' + encodeURI(file.displayName || file.name),
          };
          return fs.createReadStream(filePath, {});
        } catch ( e ) {
          throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
        }
      },
    },
    url: {
      rest: 'GET /link/:id/image.png',
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const file = await this.adapter.findById(id);
          if ( !file ) return new MoleculerClientError(i18next.t('error_data_not_found'), 404);
          const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, this.getStoragePath()));
          if ( !fs.existsSync(filePath) ) return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            'Content-Type': file.mimetype,
            'Content-Length': stat.size,
            'Content-Disposition': 'attachment;filename=' + encodeURI(file.displayName || file.name),
          };
          return fs.createReadStream(filePath, {});
        } catch ( e ) {
          throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
        }
      },
    },
    data: {
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          let file = await this.adapter.findById(id);
          if ( !file ) return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, storageDir));
          if ( !fs.existsSync(filePath) ) return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            'Content-Type': file.mimetype,
            'Content-Length': stat.size,
            'Content-Disposition': 'attachment;filename=' + encodeURI(file.displayName || file.name),
          };
          return fs.readFileSync(filePath);
        } catch ( e ) {
          throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
        }
      },
    },
    remove: {
      rest: 'DELETE /:id',
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const file = await this.adapter.findById(id);
          if ( !file ) {
            return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }
          const storagePath = this.getStoragePath();
          const dirPath = this.getDirPath(file.storageLocation, storagePath);
          const filePath = this.getFilePath(file.name, dirPath);

          if ( !fs.existsSync(filePath) ) {
            return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          fs.unlinkSync(filePath);

          return await this.adapter.removeById(id);
        } catch ( e ) {
          throw new MoleculerClientError(i18next.t('error_file_not_found'), 404);
        }
      },
    },
    filePath: {
      rest: 'GET /:id/path',
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const file = await this.adapter.findById(id);

          if ( !file ) {
            return new MoleculerClientError(i18next.t('error_data_not_found'), 404);
          }

          const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, this.getStoragePath()));
          if ( !fs.existsSync(filePath) ) {
            return new MoleculerClientError(i18next.t('error_file_not_found'), 404);
          }

          return filePath;
        } catch ( e ) {
          console.log(e);
        }
      },
    },
    checkExists: {
      rest: 'GET /:id/exists',
      admin: true,
      async handler(ctx) {
        try {
          const { id, folder } = ctx.params;
          const file = await this.adapter.findById(id);
          const filePath = this.getFilePath(file.name, this.getDirPath(folder, this.getStoragePath()));
          return fs.existsSync(filePath);
        } catch ( e ) {
          console.log(e);
        }
      },
    },
    getFileSize: {
      rest: 'GET /:id/size',
      admin: true,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const file = await this.adapter.findById(id);
          const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, this.getStoragePath()));

          return fs.existsSync(filePath) ? fs.statSync(filePath).size : 0;
        } catch ( e ) {
          console.log(e);
        }
      },
    },
    videoStream: {
      rest: 'GET /video-stream/:id',
      admin: true,
      async handler(ctx) {
        const { range } = ctx.meta;
        if ( !range ) {
          throw new MoleculerClientError(i18next.t('range_header_is_required'), 400);
        }

        const { id } = ctx.params;
        const file = await this.adapter.findById(id);
        if ( !file ) {
          throw new MoleculerClientError(i18next.t('error_video_not_found'), 404);
        }

        const filePath = this.getFilePath(file.name, this.getDirPath(file.storageLocation, this.getStoragePath()));
        if ( !fs.existsSync(filePath) ) {
          throw new MoleculerClientError(i18next.t('error_video_not_found'), 404);
        }

        const videoSize = fs.statSync(filePath).size;
        const CHUNK_SIZE = 10 ** 6; // 1MB
        const start = Number(range.replace(/\D/g, ''));
        const end = Math.min(start + CHUNK_SIZE - 1, videoSize - 1);
        const contentLength = end - start + 1;

        const headers = {
          'Content-Range': `bytes ${start}-${end}/${videoSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': contentLength,
          'Content-Type': 'video/mp4',
        };

        ctx.meta.res.writeHead(206, headers);
        const readStream = fs.createReadStream(filePath, { start, end });
        readStream.pipe(ctx.meta.res);
      },
    },
  },
  methods: {
    async save(stream, filename, folder) {
      const dirPath = this.getDirPath(folder, this.getStoragePath());
      const filePath = this.getFilePath(filename, dirPath);
      return this.saveToLocalStorage(stream, filePath);
    },
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    getStoragePath() {
      return storageDir;
    },
  },
  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  events: {},
};
