const DbMongoose = require('../../mixins/dbMongo.mixin');
const Model = require('./settings.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');

module.exports = {
  name: 'settings',
  mixins: [DbMongoose(Model), BaseService, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    postman: {
      rest: 'GET /postman',
      admin: true,
      async handler(ctx) {
        const data = await ctx.call('$node.services', { withActions: true });

        const item = data.map(item => {
          if ( ['$node', 'api'].includes(item.name) ) return;
          const actions = item.actions;
          const data = [];
          for ( const key in actions ) {
            const rest = actions[key].rest;
            if ( !rest ) continue;

            let method, path;
            if ( typeof rest === 'string' ) {
              [method, path] = rest.split(' ');
            } else {
              ({ method, path } = rest);
            }

            data.push({
              name: actions[key].rawName,
              request: {
                method,
                url: {
                  raw: `{{base_url}}/api/${item.name}${path}`,
                  host: ['{{base_url}}'],
                  path: ['api', item.name, path.replace('/', '')],
                },
              },
            });
          }
          return {
            name: item.name,
            item: data,
          };
        });

        return {
          info: {
            'name': 'demo-shop',
            'schema': 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
          },
          item: item,
        };
      },
    },
    getOne: {
      rest: 'GET /getOne',
      admin: true,
      async handler(ctx) {
        const data = await this.adapter.findOne({});

        if ( !data ) return;
        return data;
      },
    }
  },

  methods: {},

  events: {},

  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
