const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { NOTIFICATION, USER } = require('../../constants/dbCollections');
const { Schema } = mongoose;

const schema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(NOTIFICATION, schema, NOTIFICATION);
