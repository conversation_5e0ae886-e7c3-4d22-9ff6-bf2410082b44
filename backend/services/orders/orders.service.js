const DbMongoose = require('../../mixins/dbMongo.mixin');
const MODEL = require('./orders.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');
const { MoleculerClientError } = require('moleculer').Errors;
const i18next = require('i18next');
const moment = require('moment');

module.exports = {
  name: 'orders',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      userId: 'users',
      shippingAddressId: 'addresses'
    },
    populateOptions: ['userId', 'shippingAddressId'],
  },

  hooks: {
    before: {
      create: async function(ctx) {
        // Generate order number
        ctx.params.orderNumber = await this.generateOrderNumber();
      }
    },
    after: {
      create: async function(ctx, order) {
        // Emit event để các service khác xử lý
        this.broker.emit('order.created', order);
        return order;
      }
    }
  },

  actions: {
    // Tạo đơn hàng từ giỏ hàng
    createFromCart: {
      rest: 'POST /create-from-cart',
      auth: 'required',
      params: {
        shippingAddressId: 'string',
        paymentMethod: { type: 'enum', values: ['cod', 'vnpay', 'vnptpay', 'bank_transfer', 'credit_card'] },
        shippingMethod: { type: 'string', optional: true },
        customerNote: { type: 'string', optional: true },
        couponCode: { type: 'string', optional: true },
        giftCardCode: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const { shippingAddressId, paymentMethod, shippingMethod, customerNote, couponCode, giftCardCode } = ctx.params;

        // Validate cart
        const cartValidation = await ctx.call('carts.validateCart', { userId });
        if (!cartValidation.valid) {
          throw new MoleculerClientError('Giỏ hàng không hợp lệ', 400);
        }

        // Validate address
        await ctx.call('addresses.validateAddress', { addressId: shippingAddressId, userId });

        // Calculate totals
        const { subtotal, shippingFee, tax, discount, total } = await this.calculateOrderTotals(
          cartValidation.items,
          shippingAddressId,
          shippingMethod,
          couponCode,
          giftCardCode
        );

        // Create order
        const orderData = {
          userId,
          shippingAddressId,
          paymentMethod,
          shippingMethod,
          customerNote,
          couponCode,
          giftCardCode,
          subtotal,
          shippingFee,
          tax,
          discount,
          total,
          status: 'pending',
          paymentStatus: 'pending'
        };

        const order = await this.adapter.insert(orderData);

        // Create order items
        const orderItems = [];
        for (const cartItem of cartValidation.items) {
          const variant = cartItem.variantId;
          const product = variant.productId;

          const orderItem = {
            orderId: order._id,
            productId: product._id,
            variantId: variant._id,
            productName: product.name,
            variantName: variant.name,
            sku: variant.sku,
            price: variant.price,
            quantity: cartItem.quantity,
            totalPrice: variant.price * cartItem.quantity,
            productImage: variant.images?.[0] || product.images
          };

          orderItems.push(orderItem);
        }

        // Bulk insert order items
        await ctx.call('order-items.insertMany', { items: orderItems });

        // Clear cart
        await ctx.call('carts.clearCart');

        // Reserve inventory
        for (const item of orderItems) {
          await ctx.call('inventories.reserveStock', {
            variantId: item.variantId,
            quantity: item.quantity,
            orderId: order._id
          });
        }

        // Get full order with items
        const fullOrder = await this.getOrderWithItems(order._id);

        return fullOrder;
      }
    },

    // Lấy lịch sử đơn hàng của user
    getMyOrders: {
      rest: 'GET /my-orders',
      auth: 'required',
      params: {
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 10 },
        status: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const userId = ctx.meta.user._id;
        const { page, limit, status } = ctx.params;

        const query = { userId, isDeleted: false };
        if (status) {
          query.status = status;
        }

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: this.settings.populateOptions
        };

        const result = await this.adapter.paginate({ query, options });

        // Lấy order items cho mỗi order
        for (const order of result.docs) {
          order.items = await ctx.call('order-items.find', {
            query: { orderId: order._id },
            populate: ['productId', 'variantId']
          });
        }

        return result;
      }
    },

    // Lấy chi tiết đơn hàng
    getOrderDetail: {
      rest: 'GET /:id/detail',
      auth: 'required',
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const userId = ctx.meta.user._id;

        const order = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!order) {
          throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
        }

        return await this.getOrderWithItems(id);
      }
    },

    // Hủy đơn hàng
    cancelOrder: {
      rest: 'PUT /:id/cancel',
      auth: 'required',
      params: {
        id: 'string',
        reason: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { id, reason } = ctx.params;
        const userId = ctx.meta.user._id;

        const order = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!order) {
          throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
        }

        // Chỉ cho phép hủy đơn hàng ở trạng thái pending hoặc confirmed
        if (!['pending', 'confirmed'].includes(order.status)) {
          throw new MoleculerClientError('Không thể hủy đơn hàng ở trạng thái này', 400);
        }

        const updateData = {
          status: 'cancelled',
          cancelledAt: new Date(),
          adminNote: reason ? `Khách hàng hủy: ${reason}` : 'Khách hàng hủy đơn hàng'
        };

        const updated = await this.adapter.updateById(id, updateData);

        // Release reserved inventory
        const orderItems = await ctx.call('order-items.find', { query: { orderId: id } });
        for (const item of orderItems) {
          await ctx.call('inventories.releaseReservedStock', {
            variantId: item.variantId,
            quantity: item.quantity,
            orderId: id
          });
        }

        // Emit event
        this.broker.emit('order.cancelled', { orderId: id, userId, reason });

        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Cập nhật trạng thái đơn hàng (Admin only)
    updateOrderStatus: {
      rest: 'PUT /:id/status',
      admin: true,
      params: {
        id: 'string',
        status: { type: 'enum', values: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'] },
        trackingNumber: { type: 'string', optional: true },
        adminNote: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { id, status, trackingNumber, adminNote } = ctx.params;

        const order = await this.adapter.findById(id);
        if (!order) {
          throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
        }

        const updateData = { status, adminNote };

        // Set timestamps based on status
        switch (status) {
          case 'confirmed':
            updateData.confirmedAt = new Date();
            break;
          case 'shipped':
            updateData.shippedAt = new Date();
            if (trackingNumber) {
              updateData.trackingNumber = trackingNumber;
            }
            break;
          case 'delivered':
            updateData.deliveredAt = new Date();
            updateData.actualDeliveryDate = new Date();
            // Confirm stock reduction
            const orderItems = await ctx.call('order-items.find', { query: { orderId: id } });
            for (const item of orderItems) {
              await ctx.call('inventories.confirmStockReduction', {
                variantId: item.variantId,
                quantity: item.quantity,
                orderId: id
              });
            }
            break;
          case 'cancelled':
            updateData.cancelledAt = new Date();
            // Release reserved inventory
            const cancelledItems = await ctx.call('order-items.find', { query: { orderId: id } });
            for (const item of cancelledItems) {
              await ctx.call('inventories.releaseReservedStock', {
                variantId: item.variantId,
                quantity: item.quantity,
                orderId: id
              });
            }
            break;
        }

        const updated = await this.adapter.updateById(id, updateData);

        // Emit event
        this.broker.emit('order.status.updated', { orderId: id, status, previousStatus: order.status });

        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, updated);
      }
    },

    // Cập nhật trạng thái thanh toán
    updatePaymentStatus: {
      params: {
        orderId: 'string',
        paymentStatus: { type: 'enum', values: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'] },
        transactionId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { orderId, paymentStatus, transactionId } = ctx.params;

        const updateData = { paymentStatus };
        if (transactionId) {
          updateData.transactionId = transactionId;
        }

        const updated = await this.adapter.updateById(orderId, updateData);

        // Emit event
        this.broker.emit('order.payment.updated', { orderId, paymentStatus, transactionId });

        return updated;
      }
    },

    // Tracking đơn hàng
    trackOrder: {
      rest: 'GET /:id/tracking',
      auth: 'required',
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const userId = ctx.meta.user._id;

        const order = await this.adapter.findOne({
          _id: id,
          userId,
          isDeleted: false
        });

        if (!order) {
          throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
        }

        const timeline = this.buildOrderTimeline(order);

        return {
          orderId: order._id,
          orderNumber: order.orderNumber,
          status: order.status,
          trackingNumber: order.trackingNumber,
          estimatedDeliveryDate: order.estimatedDeliveryDate,
          timeline
        };
      }
    }
  },

  methods: {
    // Generate order number
    async generateOrderNumber() {
      const today = moment().format('YYYYMMDD');
      const count = await this.adapter.count({
        orderNumber: { $regex: `^ORD${today}` }
      });

      return `ORD${today}${String(count + 1).padStart(4, '0')}`;
    },

    // Calculate order totals
    async calculateOrderTotals(cartItems, shippingAddressId, shippingMethod, couponCode, giftCardCode) {
      let subtotal = 0;

      // Calculate subtotal
      cartItems.forEach(item => {
        subtotal += item.variantId.price * item.quantity;
      });

      // Calculate shipping fee
      let shippingFee = 0;
      if (shippingMethod) {
        // Call shipping service to calculate fee
        try {
          const shippingResult = await this.broker.call('shipping.calculateFee', {
            addressId: shippingAddressId,
            method: shippingMethod,
            items: cartItems
          });
          shippingFee = shippingResult.fee;
        } catch (err) {
          this.logger.warn('Failed to calculate shipping fee:', err);
        }
      }

      // Calculate tax (if applicable)
      let tax = 0;
      // tax = subtotal * 0.1; // 10% VAT example

      // Calculate discount
      let discount = 0;
      if (couponCode) {
        try {
          const couponResult = await this.broker.call('discounts.applyCoupon', {
            code: couponCode,
            subtotal
          });
          discount += couponResult.discount;
        } catch (err) {
          this.logger.warn('Failed to apply coupon:', err);
        }
      }

      if (giftCardCode) {
        try {
          const giftCardResult = await this.broker.call('giftcodes.applyGiftCard', {
            code: giftCardCode,
            amount: subtotal + shippingFee + tax - discount
          });
          discount += giftCardResult.discount;
        } catch (err) {
          this.logger.warn('Failed to apply gift card:', err);
        }
      }

      const total = subtotal + shippingFee + tax - discount;

      return {
        subtotal,
        shippingFee,
        tax,
        discount,
        total: Math.max(0, total) // Ensure total is not negative
      };
    },

    // Get order with items
    async getOrderWithItems(orderId) {
      const order = await this.adapter.findById(orderId, {
        populate: this.settings.populateOptions
      });

      if (!order) {
        throw new MoleculerClientError('Đơn hàng không tồn tại', 404);
      }

      // Get order items
      order.items = await this.broker.call('order-items.find', {
        query: { orderId },
        populate: [
          {
            path: 'productId',
            select: 'name slug images'
          },
          {
            path: 'variantId',
            select: 'name sku attributes images'
          }
        ]
      });

      return order;
    },

    // Build order timeline
    buildOrderTimeline(order) {
      const timeline = [];

      timeline.push({
        status: 'pending',
        title: 'Đơn hàng được tạo',
        timestamp: order.createdAt,
        completed: true
      });

      if (order.confirmedAt) {
        timeline.push({
          status: 'confirmed',
          title: 'Đơn hàng được xác nhận',
          timestamp: order.confirmedAt,
          completed: true
        });
      }

      if (order.status === 'processing') {
        timeline.push({
          status: 'processing',
          title: 'Đang chuẩn bị hàng',
          timestamp: order.updatedAt,
          completed: true
        });
      }

      if (order.shippedAt) {
        timeline.push({
          status: 'shipped',
          title: 'Đơn hàng đã được giao cho đơn vị vận chuyển',
          timestamp: order.shippedAt,
          completed: true,
          trackingNumber: order.trackingNumber
        });
      }

      if (order.deliveredAt) {
        timeline.push({
          status: 'delivered',
          title: 'Đơn hàng đã được giao thành công',
          timestamp: order.deliveredAt,
          completed: true
        });
      }

      if (order.cancelledAt) {
        timeline.push({
          status: 'cancelled',
          title: 'Đơn hàng đã bị hủy',
          timestamp: order.cancelledAt,
          completed: true
        });
      }

      return timeline;
    }
  },

  events: {
    // Khi thanh toán thành công
    'payment.success': {
      async handler(ctx) {
        const { orderId, transactionId } = ctx.params;

        await this.actions.updatePaymentStatus({
          orderId,
          paymentStatus: 'paid',
          transactionId
        });

        // Auto confirm order if payment is successful
        await this.actions.updateOrderStatus({
          id: orderId,
          status: 'confirmed'
        });
      }
    },

    // Khi thanh toán thất bại
    'payment.failed': {
      async handler(ctx) {
        const { orderId, reason } = ctx.params;

        await this.actions.updatePaymentStatus({
          orderId,
          paymentStatus: 'failed'
        });

        // Optionally cancel order after payment failure
        // await this.actions.updateOrderStatus({
        //   id: orderId,
        //   status: 'cancelled',
        //   adminNote: `Thanh toán thất bại: ${reason}`
        // });
      }
    }
  },

  async started() {
    this.logger.info('Orders service started');
  },

  async stopped() {
    this.logger.info('Orders service stopped');
  }
};
