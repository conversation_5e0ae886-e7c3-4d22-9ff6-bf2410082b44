const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { ORDER, USER, ADDRESS } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  // Thông tin cơ bản
  // orderNumber: { type: String, required: true, unique: true },
  userId: { type: Schema.Types.ObjectId, ref: USER, required: true },

  // Trạng thái đơn hàng
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending'
  },

  // Thông tin giá
  subtotal: { type: Number, required: true, min: 0 }, // Tổng tiền hàng
  shippingFee: { type: Number, default: 0, min: 0 }, // Phí ship
  tax: { type: Number, default: 0, min: 0 }, // Thuế
  discount: { type: Number, default: 0, min: 0 }, // Giảm giá
  total: { type: Number, required: true, min: 0 }, // Tổng thanh toán

  // Thông tin giao hàng
  shippingAddressId: { type: Schema.Types.ObjectId, ref: ADDRESS, required: true },

  // Thông tin thanh toán
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
    default: 'pending',
    index: true
  },

  paymentMethod: {
    type: String,
    enum: ['cod', 'vnpay', 'vnptpay', 'bank_transfer', 'credit_card'],
    required: true
  },

  // Mã giảm giá
  couponCode: String,
  giftCardCode: String,

  // Ghi chú
  customerNote: String,
  adminNote: String,

  // Thông tin vận chuyển
  shippingMethod: String,
  trackingNumber: String,
  estimatedDeliveryDate: Date,
  actualDeliveryDate: Date,

  // Thời gian
  confirmedAt: Date,
  shippedAt: Date,
  deliveredAt: Date,
  cancelledAt: Date,
  isDeleted: { type: Boolean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ORDER, schema, ORDER);
