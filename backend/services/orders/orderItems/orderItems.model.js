const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { ORDER_ITEM, ORDER, PRODUCT, PRODUCT_VARIANT } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  orderId: { type: Schema.Types.ObjectId, ref: ORDER, required: true },
  productId: { type: Schema.Types.ObjectId, ref: PRODUCT, required: true },
  variantId: { type: Schema.Types.ObjectId, ref: PRODUCT_VARIANT, required: true },
  productName: { type: String, required: true },
  variantName: String,
  sku: String,
  price: { type: Number, required: true, min: 0 },
  quantity: { type: Number, required: true, min: 1 },
  totalPrice: { type: Number, required: true, min: 0 },
  productImage: String,
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ORDER_ITEM, schema, ORDER_ITEM);