const DbMongoose = require('../../../mixins/dbMongo.mixin');
const MODEL = require('./orderItems.model');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');

module.exports = {
  name: 'order-items',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      orderId: 'orders',
      productId: 'products',
      variantId: 'variants'
    },
    populateOptions: ['productId', 'variantId'],
  },

  hooks: {},

  actions: {
    // Bulk insert order items
    insertMany: {
      params: {
        items: { type: 'array', items: 'object' }
      },
      async handler(ctx) {
        const { items } = ctx.params;

        const result = await this.adapter.insertMany(items);
        return result;
      }
    },

    // Lấy order items theo orderId
    getByOrder: {
      params: {
        orderId: 'string'
      },
      async handler(ctx) {
        const { orderId } = ctx.params;

        return await this.adapter.find({
          query: { orderId },
          populate: this.settings.populateOptions,
          sort: { createdAt: 1 }
        });
      }
    }
  },

  methods: {},
  events: {},
};
