const config = {
  production: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'https://demo.stealersmile.click',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: `${process.env.DOMAIN || "https://demo.stealersmile.click"}/auth/login-google`,
    backend_base_url: 'http://localhost:3000',
    supportEmail: "<EMAIL>",
    mail: process.env.EMAIL_API_KEY || "re_NUFGvfWm_CM5KGY57X2pTT8AJ1H9DJwRt",
  },
  development: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'http://localhost:8080',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: "http://localhost:8080/auth/login-google",
    backend_base_url: '',
    supportEmail: "<EMAIL>",
    mail: process.env.EMAIL_API_KEY || "re_NUFGvfWm_CM5KGY57X2pTT8AJ1H9DJwRt"
  },
};

exports.getConfig = env => config[env] || config.development;
