const path = require('path');
const webpack = require('webpack');
const Dotenv = require('dotenv-webpack');

module.exports = options => {
  const isDev = options.mode === 'development';
  return {
    mode: options.mode,
    entry: options.entry,
    output: Object.assign(
      {
        // Compile into js/build.js
        path: path.resolve(process.cwd(), 'build'),
        publicPath: '/',
      },
      options.output,
    ), // Merge with env dependent settings
    optimization: options.optimization,
    module: {
      rules: [
        {
          test: /\.jsx?$/, // Transform all .js and .jsx files required somewhere with Babel
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: options.babelQuery,
          },
        },
        {
          test: /\.s[ac]ss$/i,
          use: [
            { loader: 'style-loader' },
            { loader: 'css-loader', options: { sourceMap: isDev } },
            { loader: 'sass-loader', options: { sourceMap: isDev } },
          ],
        },
        {
          test: /\.css$/,
          use: [
            { loader: 'style-loader' },
            { loader: 'css-loader', options: { sourceMap: isDev } },
            { loader: 'postcss-loader' },
          ],
        },
        {
          test: /\.less$/,
          use: [
            { loader: 'style-loader' },
            { loader: 'css-loader', options: { sourceMap: isDev } },
            {
              loader: 'less-loader',
              options: {
                sourceMap: isDev,
                lessOptions: {
                  javascriptEnabled: true,
                },
              },
            },
          ],
        },
        {
          test: /\.(eot|otf|ttf|woff|woff2|mrt|key)$/,
          type: 'asset/resource',
        },
        {
          test: /\.(png|svg|jpg|jpeg|gif|mp3)$/i,
          type: 'asset/resource',
        },
        {
          test: /\.m?js/,
          resolve: {
            fullySpecified: false,
          },
        },
        {
          test: /\.(mp4|webm)$/,
          use: {
            loader: 'url-loader',
            options: {
              limit: 10000,
            },
          },
        },
      ],
    },
    plugins: options.plugins.concat([
      // Always expose NODE_ENV to webpack, in order to use `process.env.NODE_ENV`
      // inside your code for any environment checks; Terser will automatically
      // drop any unreachable code.
      new Dotenv({
        systemvars: true, // Lấy các biến môi trường từ system
      }),
      new webpack.ProvidePlugin({
        process: 'process/browser',
      }),
      new webpack.EnvironmentPlugin({
        NODE_ENV: 'development',
      }),
      new webpack.ProvidePlugin({
        $: 'jquery',
      }),
    ]),
    resolve: {
      modules: [path.resolve(__dirname, '../app'), 'node_modules'],
      extensions: ['.js', '.jsx', '.react.js'],
      mainFields: ['browser', 'jsnext:main', 'main'],
      fallback: {
        assert: require.resolve('assert'),
        buffer: require.resolve('buffer'),
        events: require.resolve('events'),
        punycode: require.resolve('punycode'),
        string_decoder: require.resolve('string_decoder'),
        sys: require.resolve('util'),
        url: require.resolve('url'),
        util: require.resolve('util/'),
        path: require.resolve('path-browserify'),
      },
      alias: {
        '@constant': path.resolve(__dirname, '../src/constants/constant.js'),
        '@link': path.resolve(__dirname, '../src/constants/link.js'),
        '@api': path.resolve(__dirname, '../src/constants/api.js'),
        '@rule': path.resolve(__dirname, '../src/constants/rule.js'),
        '@navigation': path.resolve(__dirname, '../src/app/navigation/index.js'),

        '@src': path.resolve(__dirname, '../src/'),
        '@app': path.resolve(__dirname, '../src/app/'),
        '@component': path.resolve(__dirname, '../src/app/component/'),
        '@services': path.resolve(__dirname, '../src/app/services/'),
        '@common': path.resolve(__dirname, '../src/common/'),
        '@pages': path.resolve(__dirname, '../src/app/pages/'),
      },
    },
    devtool: options.devtool,
    target: 'web', // Make web variables accessible to webpack, e.g. window
    performance: options.performance || {},
  };
};
