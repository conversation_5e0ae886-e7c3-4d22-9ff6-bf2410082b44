const plugin = require('tailwindcss/plugin');

const columns = {};
for (let i = 1; i <= 24; i++) {
  const key = `${i}/24`;
  columns[key] = `${100 / 24} * i`;
}

module.exports = {
  content: [
    './src/**/*.{html,js,jsx,ts,tsx}',
  ],
  theme: {
    extend: {
      gridTemplateColumns: {
        // 24 column grid
        '24': 'repeat(24, minmax(0, 1fr))',
      },
      // width: columns, //
    },
    // screens: {
    //   'sm': '640px',
    //   'md': '768px',
    //   'lg': '1024px',
    //   'xl': '1280px',
    //   '2xl': '1536px',
    // },
    screens: {
      'sm': '480px',
      'md': '640px',
      'lg': '960px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    container: {
      center: true,
      maxWidth: {
        '2xl': '700px',
      },
    },

  },
  plugins: [
    plugin(function({ addBase, addComponents, theme }) {
      addBase({
        // 'h1': { fontSize: theme('fontSize.2xl') },
        // 'h2': { fontSize: theme('fontSize.xl') },
        // 'h3': { fontSize: theme('fontSize.lg') },
      });

      addComponents({
        '.container': {
          width: '100%',
          '@screen sm': { maxWidth: '420px' },
          '@screen md': { maxWidth: '456px' },
          '@screen lg': { maxWidth: '936px' },
          '@screen xl': { maxWidth: '1056px' },
          '@screen 2xl': { maxWidth: '1416px' },
        },
      });

    }),
  ],
  corePlugins: {
    preflight: false, // <== disable this!
  },
};
