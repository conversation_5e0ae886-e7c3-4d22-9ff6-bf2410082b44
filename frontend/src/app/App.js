import React, { Suspense, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import { useTranslation, withTranslation } from 'react-i18next';
import dayjs from 'dayjs';

import AuthInit from './auth/components/AuthInit';

import Theme from '@app/theme';
import { SetupNavigation } from '@navigation';

import { ConfirmProvider, confirmRef } from '@app/component/ConfirmProvider';
import { ToastProvider, toastRef } from '@app/component/ToastProvider';

import * as app from '@src/ducks/app.duck';
// import * as auth from '@src/ducks/auth.duck';
// import * as tracking from '@src/ducks/tracking.duck';

import('dayjs/locale/en');
import('dayjs/locale/vi');

function App({ user, moduleApp, history, trackCustomView, ...props }) {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const location = useLocation();
  dayjs.locale(i18n.language);

  useEffect(() => {
    props.setLanguage(i18n.language);
    handleLogoutAllTab();
  }, []);

  useEffect(() => {
    // trackCustomView();
  }, [location]);

  function handleLogoutAllTab() {
    window.addEventListener('storage', (event) => {
      if (event.storageArea === localStorage && event.key === window.location.host + 'logout') {
        let isLogout = localStorage.getItem(window.location.host + 'logout');
        if (isLogout) {
          props.removeUserState();
        } else {
          props.requestUser();
        }
      }
    }, false);
  }

  return (
    <Theme>
      <SetupNavigation/>
      <ConfirmProvider ref={confirmRef}>
        <ToastProvider ref={toastRef}>
          <Suspense fallback={<></>}>
            <AuthInit>
              <Outlet/>
            </AuthInit>
          </Suspense>
        </ToastProvider>
      </ConfirmProvider>
    </Theme>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  // ...auth.actions,
  ...app.actions,
  // ...tracking.actions,
};

export default withTranslation()(connect(mapStateToProps, mapDispatchToProps)(App));
