import { API } from '@api';
import { deleteBase, deleteManyBase, updateBase } from '@services/Base';

export function updateResponseOutPut(data) {
  return updateBase(API.RESPONSE_OUTPUT_ID, data);
}

export function deleteResponse(id) {
  return deleteBase(API.RESPONSE_ID, id);
}

export function deleteMarkTestResponse(id) {
  return deleteBase(API.DELETE_MARK_TEST_RESPONSE, id);
}

export function deleteManyResponse(projectId, ids) {
  return deleteManyBase(API.RESPONSE, { projectId, ids });
}

export function retryResponse(data, params) {
  return updateBase(API.RETRY_RESPONSE, data, [], false, true, params);
}

export function activateResponse(id) {
  return updateBase(API.ACTIVATE_RESPONSE, { _id: id });
}

export function updateResponse(data) {
  return updateBase(API.RESPONSE_ID, data);
}

export function cancelRetryResponse(responseId) {
  return updateBase(API.CANCEL_RETRY_RESPONSE, { _id: responseId });
}
