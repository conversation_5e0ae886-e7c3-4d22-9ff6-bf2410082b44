import { API } from '@api';
import { createBase, deleteBase, getAllBase, updateBase } from '@services/Base';

export function getModalAI(query, loading) {
  return getAllBase(API.MODAL_AI, query);
}

export function createModalAI(data, toastError = false) {
  return createBase(API.MODAL_AI, data, [], false, toastError);
}

export function updateModalAI(data, toastError = false) {
  return updateBase(API.MODAL_AI_ID, data, [], false, toastError);
}

export function deleteModalAI(id, toastError = false) {
  return deleteBase(API.MODAL_AI_ID, id, false, toastError);
}