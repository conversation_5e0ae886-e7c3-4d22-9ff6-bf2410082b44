import { API } from '@api';
import { createBase, deleteBase, getAllBase, getAllPaginationBase, updateBase } from '@services/Base';

export function getAllAPIKeys(query) {
  return getAllBase(API.API_KEY, query);
}

export function getAllWithPaginationAPIKeys(paging, query) {
  return getAllPaginationBase(API.API_KEY, paging, query, ['apiKey', 'modelInterface']);
}

export function createAPIKey(data, toastError = false) {
  return createBase(API.API_KEY, data, [], false, toastError);
}

export function updateAPIKey(data, toastError = false) {
  return updateBase(API.API_KEY_ID, data, [], false, toastError);
}

export function deleteAPIKey(id, toastError = false) {
  return deleteBase(API.API_KEY_ID, id, false, toastError);
}
