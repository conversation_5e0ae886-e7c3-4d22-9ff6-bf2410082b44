import { API } from '@api';
import { joinParams, updateBase, } from '@services/Base';
import axios from 'axios';
import { convertSnakeCaseToCamelCase } from '@common/dataConverter';
import { genPopulateParam, genQueryParam } from '@common/functionCommons';

export async function getAllUserTool(userId, query, populateOpts = [], loading) {
  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
  ];
  const config = { loading };

  return axios.get(`${API.USER_TOOL.format(userId)}?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function updateUserTool(data, toastError = false) {
  return updateBase(API.USER_TOOL_ID, data, [], false, toastError);
}