import { API } from '@api';
import { createBase, deleteBase, getBase, getDetailBase, streamBase, updateBase } from '@services/Base';
import axios from 'axios';
import { convertSnakeCaseToCamelCase } from '@common/dataConverter';

export function createContent(data) {
  return createBase(API.CONTENT, data);
}

export function createContentBlock(projectId, data, loading = true) {
  const config = { loading };
  return axios
    .put(API.CONTENT_BLOCK.format(projectId), (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      //renderMessageError(err);
      throw err;
    });
}

export function updateContent(data) {
  return updateBase(API.CONTENT_ID, data);
}

export function getContentByProjectId(id) {
  return getBase(API.CONTENT_BY_PROJECT_ID.format(id));
}

export function getContentDetail(id) {
  return getDetailBase(API.CONTENT_ID, id);
}

export function submitInputData(contentId, data, loading, params) {
  const config = { loading, params };
  return axios.put(API.CONTENT_SUBMIT_INPUT_DATA.format(contentId), (data), config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(() => null);
}

export function streamContent(responseId) {

  return streamBase(API.RESPONSE_STREAM_CONTENT.format(responseId));

}

export function streamSseMarkExam(projectId) {
  return new EventSource(
    API.STREAM_MARK_TEST.format(projectId),
  );
}

export function streamSSEResponse(responseId) {
  return new EventSource(
    API.RESPONSE_STREAM_CONTENT.format(responseId),
  );
}

export function moveDownContent(data) {
  return updateBase(API.CONTENT_MOVE_DOWN, data);
}

export function moveUpContent(data) {
  return updateBase(API.CONTENT_MOVE_UP, data);
}

export function deleteContent(id) {
  return deleteBase(API.CONTENT_ID, id);
}

export function moveContentToIndex(data) {
  return updateBase(API.CONTENT_MOVE_TO_INDEX, data);
}

export function getMediaFileName(inputId) {
  return getDetailBase(API.GET_MEDIA_FILE_NAME, inputId, [], false, true);
}

export function submitExam(data) {
  return createBase(API.CONTENT_SUBMIT_EXAM, data);
}

export function submitMark(data) {
  return createBase(API.CONTENT_SUBMIT_MARK, data);
}

export function submitSpeaking(data) {
  return createBase(API.CONTENT_SUBMIT_SPEAKING, data);
}

export function generateTopic(data) {
  return createBase(API.CONTENT_GENERATE_TOPIC, data);
}
