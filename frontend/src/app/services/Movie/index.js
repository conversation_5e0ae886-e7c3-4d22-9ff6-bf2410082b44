import { API } from '@api';
import { deleteBase, getAllPaginationBase } from '@services/Base';

export const getPaginationMovie = async (query = {}) => {
  return getAllPaginationBase(API.MOVIE, query);
  // const queryObj = cloneObj(query);
  // queryObj.page ||= 1;
  // queryObj.limit ||= 0;
  // return axios
  //   .get(API.MOVIE, { params: queryObj })
  //   .then((response) => {
  //     if (response.status === 200) return response?.data;
  //     return null;
  //   })
  //   .catch((err) => {
  //     console.log("err", err);
  //     return null;
  //   });
};

export const deleteMovie = async (movieId) => {
  return deleteBase(API.MOVIE_ID, movieId);
};
