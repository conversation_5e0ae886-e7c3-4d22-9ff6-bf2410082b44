import { API } from '@api';
import axios from 'axios';
import { convertSnakeCaseToCamelCase } from '@common/dataConverter';
import { getDetailBase } from '@services/Base';
import { getFileExtension } from '@src/common/functionCommons';
import fileDownload from 'js-file-download';

export async function uploadAudio(file, workspaceId, requestConfig = {}) {
  const formData = new FormData();
  formData.append('fileType', 'file');
  formData.append('folder', 'audio');
  if (workspaceId) {
    formData.append('workspaceId', workspaceId);
  }
  formData.append('file', file);
  const config = {
    ...requestConfig,
    headers: { 'Content-Type': 'multipart/form-data' },
  };
  return await axios
    .post(API.UPLOAD_FILE, formData, config)
    .then(response => {
      if (response.status === 200) {
        return { success: true, data: convertSnakeCaseToCamelCase(response.data) };
      }
      return null;
    })
    .catch((err) => {
      console.log('err', err);
      // renderMessageError(err);
      return null;
    });
}

export function uploadFile(file, data, requestConfig = {}) {
  const formData = new FormData();
  formData.append('fileType', 'file');
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  formData.append('file', file);
  const config = {
    ...requestConfig,
    headers: { 'Content-Type': 'multipart/form-data' },
  };
  return axios
    .post(API.UPLOAD_FILE, formData, config)
    .then(response => {
      if (response.status === 200) return response.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

//Get file audio or pdf and conver to blob
export async function getFileById(id, config) {
  return axios.get(API.STREAM_ID.format(id), { ...config, responseType: 'arraybuffer', })
    .then(response => {
      if (response.status === 200 && response?.headers['content-disposition']) {
        const contentDisposition = response?.headers['content-disposition'];
        const fileName = contentDisposition?.slice(contentDisposition.indexOf('filename=') + 9).replaceAll('%20', ' ');
        const fileExtension = getFileExtension(fileName);
        const newBlob = new Blob([response.data], { type: fileExtension === 'pdf' ? 'application/pdf' : 'audio/mpeg' });
        const url = URL.createObjectURL(newBlob);
        return { url, fileName };
      }
      return null;
    })
    .catch(err => {
      console.log('err', err);
      //renderMessageError(err);
      return null;
    });
}

export function downloadFileById(id, fileName) {
  const config = { responseType: 'blob' };
  return axios.get(API.STREAM_ID.format(id), config)
    .then(response => {
      if (response.data) fileDownload(response.data, fileName);
      return null;
    })
    .catch(err => {
      return null;
    });
}

export async function uploadImageExtractText(data, workspaceId, config = {}) {
  config.headers = { 'content-type': 'multipart/form-data' };
  const formData = new FormData();
  const { file, top, left, height, width } = data;
  formData.append('top', top);
  formData.append('left', left);
  formData.append('height', height);
  formData.append('width', width);
  if (workspaceId) {
    formData.append('workspaceId', workspaceId);
  }
  formData.append('file', file);
  return axios.post(API.IMAGE_EXTRACT_TEXT, formData, config)
    .then(response => {
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    })
    .catch(err => {
      console.log('err', err);
      return null;
    });
}

export async function uploadAndDescribeImage(data, config = {}) {
  config.headers = { 'content-type': 'multipart/form-data' };
  const formData = new FormData();
  const { file, top, left, height, width } = data;
  formData.append('top', top);
  formData.append('left', left);
  formData.append('height', height);
  formData.append('width', width);
  formData.append('file', file);
  return axios.post(API.IMAGE_DESCRIPTION, formData, config)
    .then(response => {
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    })
    .catch(err => {
      console.log('err', err);
      return null;
    });
}

export async function uploadFileExtractText(data, config = {}) {
  config.headers = { 'content-type': 'multipart/form-data' };
  const formData = new FormData();
  formData.append('firstPage', data.startPage);
  formData.append('lastPage', data.endPage);
  formData.append('totalPages', data.totalPages);
  formData.append('folder', 'office');
  if (data.workspaceId) {
    formData.append('workspaceId', data.workspaceId);
  }

  formData.append('file', data.file);
  return axios.post(API.UPLOAD_FILE_EXTRACT_TEXT, formData, config)
    .then(response => {
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    })
    .catch(err => {
      console.log('err', err);
      return null;
    });
}

export async function extractTextByFileId(data, config = {}) {
  const { fileId, startPage, endPage, totalPages } = data;
  return axios.get(`${API.FILE_ID_EXTRACT_TEXT.format(fileId)}?firstPage=${startPage}&lastPage=${endPage}&totalPages=${totalPages}`, config)
    .then(response => {
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    })
    .catch(err => {
      console.log('err', err);
      return null;
    });
}

export async function uploadImage(file, workspaceId, config = {}, checkUsed) {
  const formData = new FormData();

  if (workspaceId) {
    formData.append('workspaceId', workspaceId);
  }
  if (checkUsed) {
    formData.append('used', false);
  }
  formData.append('file', file);
  const requestConfig = {
    ...config,
    headers: { 'Content-Type': 'multipart/form-data' },
  };

  return await axios
    .post(API.UPLOAD_IMAGE, formData, requestConfig)
    .then(response => {
      if (response.status === 200) return { success: true, data: response.data };
      return response.data;
    })
    .catch((err) => {
      return err?.response?.data || err;
    });
}

export function getFileDetail(id) {
  return getDetailBase(API.FILE_ID, id);
}

export async function deleteImage(id) {
  return axios.delete(API.IMAGE_ID.format(id), { hideNoti: true })
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export async function deleteFile(id) {
  return axios.delete(API.FILE_ID.format(id), { hideNoti: true })
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}