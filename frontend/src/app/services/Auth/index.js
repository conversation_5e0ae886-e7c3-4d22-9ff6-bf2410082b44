import axios from 'axios';
import { API } from '@api';
import { convertSnakeCaseToCamelCase } from '@src/common/dataConverter';

export function login(data) {
  return axios.post(`${API.LOGIN}`, (data), { hideNoti: true })
    .then(response => {
      return {
        data: response.data?.user || response.data,
        code: response.status,
      };
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export function oauthGoogle(data) {
  return axios.post(`${API.LOGIN_GOOGLE}`, (data))
    .then(response => {
      if (response.status === 200) {
        return {
          data: response.data?.user,
          success: true,
        };
      } else {
        return {
          code: response.status,
          message: response.data?.message,
        };
      }

    })
    .catch((err) => err?.response?.data);
}

export function logout(data) {
  return axios.post(`${API.LOGOUT}`, (data))
    .then(response => {
      if (response?.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      // renderMessageError(err);
      return null;
    });
}

export function getUserByToken() {
  return axios.get(API.MY_INFO)
    .then(response => {
      if (response?.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      // renderMessageError(err);
      return null;
    });
}

// Thực hiện get token mới
export function getNewToken(data) {
  return axios.post(`${API.USER_REFRESH_TOKEN}`, data, { hideNoti: true })
    .then(response => {
      if (response.status === 200) return { success: true };
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export async function requestResetPassword(token, data) {
  return axios
    .post(API.USER_RESET_PASSWORD, data, {
      headers: { Authorization: `Bearer ${token}` },
      hideNoti: true
    })
    .then(response => {
      return {
        data: response.data,
        code: response.status,
      };
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export async function changePassword(data) {
  return axios
    .post(API.USER_CHANGE_PASSWORD, data)
    .then(response => response.data)
    .catch((err) => {
      return null;
    });
}

export async function requestForgetPassword(data) {
  return axios
    .post(API.USER_FORGET_PASSWORD, data, { hideNoti: true })
    .then(response => {
      return {
        data: response.data,
        code: response.status,
      };
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export async function register(data) {
  return axios
    .post(API.REGISTER_USER, data, { hideNoti: true })
    .then(response => {
      return {
        data: response.data,
        code: response.status,
      };
    })
    .catch((err) => {
      return err?.response?.data;
    });
}

export function updateInfoUser(data) {
  return axios
    .patch(API.USER_INFO, data)
    .then(response => response.data)
    .catch((err) => {
      return err?.response?.data;
    });
}
