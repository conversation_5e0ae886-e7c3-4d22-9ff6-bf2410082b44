import { useEffect, useMemo, useState } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { Layout } from "antd";

import useWindowDimensions from "@common/windowDimensions";

import Aside from "@app/layout/Aside";
import Header from "@app/layout/Header";
import Footer from "@app/layout/Footer";
import DrawerMenu from "@app/layout/DrawerMenu";

import { SCREEN_PC } from "@constant";
import { LINK } from "@link";

const MasterLayout = () => {
  const { width } = useWindowDimensions();
  const location = useLocation();
  const isShowAside = useMemo(() => {
    // Only show aside for admin pages
    if (location.pathname.includes(LINK.ADMIN_PAGE)) {
      return width >= SCREEN_PC;
    }
    return false;
  }, [width, location.pathname]);

  const mainLayout = {
    height: "100vh",
    display: "flex",
    flexDirection: "row",
  };

  const contentLayout = {
    display: "flex",
    flexDirection: "column",
    flex: 1,
    height: "100vh",
    overflow: "hidden",
  };

  return (
    <Layout style={mainLayout}>
      {isShowAside && <Aside />}
      {!isShowAside && location.pathname.includes(LINK.ADMIN_PAGE) && (
        <DrawerMenu>
          <Aside />
        </DrawerMenu>
      )}

      <Layout style={contentLayout}>
        <Header />
        <Layout.Content
          id="js-layout-content"
          style={{ flex: 1, overflow: "auto" }}
          className="content scrollbar show-scrollbar"
        >
          <Outlet />
          <Footer />
        </Layout.Content>
      </Layout>
    </Layout>
  );
};

export default MasterLayout;
