.shop-footer {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 48px 0 24px;
  margin-top: 48px;

  .shop-footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .footer-main {
    margin-bottom: 32px;

    .footer-section {
      h4 {
        color: #262626;
        font-weight: 600;
        margin-bottom: 16px;
        font-size: 16px;
      }

      .footer-logo {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        img {
          height: 40px;
          margin-right: 12px;
        }

        .logo-text {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
        }
      }

      .company-description {
        color: #666;
        line-height: 1.6;
        margin-bottom: 24px;
      }

      .social-links {
        h5 {
          color: #262626;
          font-size: 14px;
          margin-bottom: 12px;
          font-weight: 600;
        }

        a {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #fff;
          transition: all 0.3s ease;
          border: 1px solid #e9ecef;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          margin-bottom: 8px;

          a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 14px;

            &:hover {
              color: #1890ff;
            }
          }
        }
      }

      .contact-info {
        .contact-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;
          gap: 12px;

          .anticon {
            color: #1890ff;
            font-size: 16px;
            margin-top: 2px;
          }

          div {
            flex: 1;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .footer-support {
    margin-bottom: 32px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      color: #262626;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .support-link {
      color: #666;
      text-decoration: none;
      font-size: 13px;
      transition: color 0.3s ease;
      display: block;
      padding: 4px 0;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .footer-payment {
    margin-bottom: 32px;

    h4 {
      color: #262626;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .payment-methods {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .payment-method {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }

        img {
          height: 24px;
          width: auto;
          display: block;
        }
      }
    }
  }

  .footer-bottom {
    padding-top: 24px;

    .copyright {
      color: #666;
      font-size: 14px;
    }

    .footer-certifications {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 12px;

      img {
        height: 32px;
        width: auto;
        opacity: 0.7;
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  // Mobile Responsive
  @media (max-width: 768px) {
    padding: 32px 0 16px;
    margin-top: 32px;

    .shop-footer-container {
      padding: 0 8px;
    }

    .footer-main {
      margin-bottom: 24px;

      .footer-section {
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        .footer-logo {
          justify-content: center;
          text-align: center;

          .logo-text {
            font-size: 20px;
          }
        }

        .company-description {
          text-align: center;
        }

        .social-links {
          text-align: center;
        }

        h4 {
          text-align: center;
          font-size: 16px;
        }

        .footer-links {
          text-align: center;
        }

        .contact-info {
          .contact-item {
            justify-content: center;
            text-align: center;
            flex-direction: column;
            gap: 8px;

            .anticon {
              margin-top: 0;
            }
          }
        }
      }
    }

    .footer-support {
      padding: 16px;
      margin-bottom: 24px;

      .support-link {
        text-align: center;
        padding: 8px 0;
      }
    }

    .footer-payment {
      margin-bottom: 24px;

      .payment-methods {
        gap: 12px;

        .payment-method {
          padding: 6px 10px;

          img {
            height: 20px;
          }
        }
      }
    }

    .footer-bottom {
      text-align: center;
      padding-top: 16px;

      .footer-certifications {
        justify-content: center;
        margin-top: 12px;

        img {
          height: 28px;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .footer-payment {
      .payment-methods {
        gap: 8px;

        .payment-method {
          padding: 4px 8px;

          img {
            height: 18px;
          }
        }
      }
    }

    .footer-support {
      .ant-row {
        .ant-col {
          flex: 0 0 50%;
          max-width: 50%;
        }
      }
    }
  }
}
