import React from 'react';
import { Layout, Row, Col, Space, Typography, Divider } from 'antd';
import { 
  PhoneOutlined, 
  MailOutlined, 
  EnvironmentOutlined,
  FacebookOutlined,
  TwitterOutlined,
  InstagramOutlined,
  YoutubeOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { LINK } from '@link';
import './Footer.scss';

const { Footer: AntFooter } = Layout;
const { Title, Text, Paragraph } = Typography;

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { title: 'Trang chủ', link: LINK.SHOP.HOME },
    { title: 'Sản phẩm', link: LINK.SHOP.PRODUCTS },
    { title: 'Giỏ hàng', link: LINK.SHOP.CART },
    { title: 'Đơn hàng', link: LINK.SHOP.ORDERS },
    { title: '<PERSON><PERSON><PERSON> thích', link: LINK.SHOP.WISHLIST }
  ];

  const serviceLinks = [
    { title: '<PERSON>ề chúng tôi', link: LINK.ABOUT },
    { title: '<PERSON><PERSON>n hệ', link: LINK.CONTACT },
    { title: 'Trợ giúp', link: LINK.HELP },
    { title: 'Tuyển dụng', link: '/careers' },
    { title: 'Tin tức', link: '/news' }
  ];

  const supportLinks = [
    { title: 'Chính sách bảo hành', link: '/warranty-policy' },
    { title: 'Chính sách đổi trả', link: '/return-policy' },
    { title: 'Chính sách vận chuyển', link: '/shipping-policy' },
    { title: 'Chính sách thanh toán', link: '/payment-policy' },
    { title: 'Điều khoản sử dụng', link: '/terms-of-service' },
    { title: 'Chính sách bảo mật', link: '/privacy-policy' }
  ];

  const paymentMethods = [
    { name: 'Visa', image: '/images/payment/visa.png' },
    { name: 'MasterCard', image: '/images/payment/mastercard.png' },
    { name: 'Momo', image: '/images/payment/momo.png' },
    { name: 'ZaloPay', image: '/images/payment/zalopay.png' },
    { name: 'VNPay', image: '/images/payment/vnpay.png' },
    { name: 'COD', image: '/images/payment/cod.png' }
  ];

  return (
    <AntFooter className="shop-footer">
      <div className="shop-footer-container">
        {/* Main Footer Content */}
        <div className="footer-main">
          <Row gutter={[32, 32]}>
            {/* Company Info */}
            <Col xs={24} sm={12} md={6}>
              <div className="footer-section">
                <div className="footer-logo">
                  <img src="/images/logo.png" alt="ShopMart" />
                  <span className="logo-text">ShopMart</span>
                </div>
                <Paragraph className="company-description">
                  ShopMart - Nền tảng mua sắm trực tuyến uy tín hàng đầu Việt Nam. 
                  Cam kết mang đến trải nghiệm mua sắm tuyệt vời nhất.
                </Paragraph>
                
                <div className="social-links">
                  <Title level={5}>Kết nối với chúng tôi</Title>
                  <Space size="middle">
                    <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                      <FacebookOutlined style={{ fontSize: '24px', color: '#1877f2' }} />
                    </a>
                    <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                      <TwitterOutlined style={{ fontSize: '24px', color: '#1da1f2' }} />
                    </a>
                    <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                      <InstagramOutlined style={{ fontSize: '24px', color: '#e4405f' }} />
                    </a>
                    <a href="https://youtube.com" target="_blank" rel="noopener noreferrer">
                      <YoutubeOutlined style={{ fontSize: '24px', color: '#ff0000' }} />
                    </a>
                  </Space>
                </div>
              </div>
            </Col>

            {/* Quick Links */}
            <Col xs={24} sm={12} md={6}>
              <div className="footer-section">
                <Title level={4}>Liên kết nhanh</Title>
                <ul className="footer-links">
                  {quickLinks.map((item, index) => (
                    <li key={index}>
                      <Link to={item.link}>{item.title}</Link>
                    </li>
                  ))}
                </ul>
              </div>
            </Col>

            {/* Services */}
            <Col xs={24} sm={12} md={6}>
              <div className="footer-section">
                <Title level={4}>Dịch vụ</Title>
                <ul className="footer-links">
                  {serviceLinks.map((item, index) => (
                    <li key={index}>
                      <Link to={item.link}>{item.title}</Link>
                    </li>
                  ))}
                </ul>
              </div>
            </Col>

            {/* Contact Info */}
            <Col xs={24} sm={12} md={6}>
              <div className="footer-section">
                <Title level={4}>Thông tin liên hệ</Title>
                <div className="contact-info">
                  <div className="contact-item">
                    <EnvironmentOutlined />
                    <div>
                      <Text strong>Địa chỉ:</Text>
                      <br />
                      <Text>123 Đường ABC, Phường XYZ, Quận 1, TP.HCM</Text>
                    </div>
                  </div>
                  
                  <div className="contact-item">
                    <PhoneOutlined />
                    <div>
                      <Text strong>Hotline:</Text>
                      <br />
                      <Text>1900-1234 (24/7)</Text>
                    </div>
                  </div>
                  
                  <div className="contact-item">
                    <MailOutlined />
                    <div>
                      <Text strong>Email:</Text>
                      <br />
              <Text><EMAIL></Text>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>

        {/* Support Links */}
        <div className="footer-support">
          <Title level={4}>Chính sách & Hỗ trợ</Title>
          <Row gutter={[16, 8]}>
            {supportLinks.map((item, index) => (
              <Col xs={12} sm={8} md={4} key={index}>
                <Link to={item.link} className="support-link">
                  {item.title}
                </Link>
              </Col>
            ))}
          </Row>
        </div>

        {/* Payment Methods */}
        <div className="footer-payment">
          <Title level={4}>Phương thức thanh toán</Title>
          <div className="payment-methods">
            {paymentMethods.map((method, index) => (
              <div key={index} className="payment-method">
                <img 
                  src={method.image || '/images/placeholder.png'} 
                  alt={method.name}
                  title={method.name}
                />
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <Row justify="space-between" align="middle">
            <Col xs={24} md={12}>
              <Text className="copyright">
                © {currentYear} ShopMart. Tất cả quyền được bảo lưu.
              </Text>
            </Col>
            <Col xs={24} md={12}>
              <div className="footer-certifications">
                <img src="/images/cert/dmca.png" alt="DMCA" />
                <img src="/images/cert/ssl.png" alt="SSL" />
                <img src="/images/cert/verified.png" alt="Verified" />
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </AntFooter>
  );
};

export default Footer;
