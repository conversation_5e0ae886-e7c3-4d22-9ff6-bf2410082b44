import React from 'react';
import { Menu } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  DashboardOutlined, 
  ShoppingOutlined, 
  AppstoreOutlined, 
  ShoppingCartOutlined,
  SettingOutlined 
} from '@ant-design/icons';

import { LINK } from '@link';
import './Aside.scss';

function AdminAside() {
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    {
      key: LINK.ADMIN.DASHBOARD,
      icon: <DashboardOutlined />,
      label: 'Dashboard',
      onClick: () => navigate(LINK.ADMIN.DASHBOARD)
    },
    {
      key: LINK.ADMIN.PRODUCTS,
      icon: <ShoppingOutlined />,
      label: 'Quản lý sản phẩm',
      onClick: () => navigate(LINK.ADMIN.PRODUCTS)
    },
    {
      key: LINK.ADMIN.CATEGORIES,
      icon: <AppstoreOutlined />,
      label: '<PERSON>h mục',
      onClick: () => navigate(LINK.ADMIN.CATEGORIES)
    },
    {
      key: LINK.ADMIN.ORDERS,
      icon: <ShoppingCartOutlined />,
      label: 'Đơn hàng',
      onClick: () => navigate(LINK.ADMIN.ORDERS)
    },
    {
      key: LINK.ADMIN.SETTING,
      icon: <SettingOutlined />,
      label: 'Cài đặt',
      onClick: () => navigate(LINK.ADMIN.SETTING)
    }
  ];

  const getSelectedKey = () => {
    const path = location.pathname;
    // Find the most specific match
    return menuItems.find(item => path.includes(item.key.replace('/admin', '')))?.key || LINK.ADMIN.DASHBOARD;
  };

  return (
    <div className="aside-body">
      <div className="admin-header">
        <h3>Quản trị viên</h3>
      </div>
      <Menu
        mode="vertical"
        selectedKeys={[getSelectedKey()]}
        items={menuItems}
        className="admin-menu"
      />
    </div>
  );
}

export default AdminAside;
