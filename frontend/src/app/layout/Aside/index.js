import { Layout } from 'antd';
import { useLocation } from 'react-router-dom';
import { connect } from 'react-redux';

import AdminAside from '@app/layout/Aside/AdminAside';

import { LINK } from '@link';
import './Aside.scss';

function Aside({ user }) {
  const { pathname } = useLocation();

  // Only show aside for admin pages
  if (!pathname.includes(LINK.ADMIN_PAGE)) return null;
  
  // Check if user has admin permission
  const isAdmin = user?.isSystemAdmin;
  if (!isAdmin) return null;

  return <Layout.Sider id="aside" width={264}>
    <AdminAside/>
  </Layout.Sider>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Aside);
