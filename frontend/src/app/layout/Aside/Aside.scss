// Admin Aside Styles
#aside {
  background: #001529;
  
  .aside-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .admin-header {
      padding: 16px;
      border-bottom: 1px solid #002140;
      
      h3 {
        color: #fff;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .admin-menu {
      flex: 1;
      background: transparent;
      border: none;
      
      .ant-menu-item {
        color: rgba(255, 255, 255, 0.65);
        margin: 0;
        
        &:hover {
          color: #fff;
          background: #1890ff;
        }
        
        &.ant-menu-item-selected {
          color: #fff;
          background: #1890ff;
          
          &::after {
            display: none;
          }
        }
        
        .ant-menu-title-content {
          margin-left: 8px;
        }
        
        .anticon {
          font-size: 16px;
        }
      }
    }
  }
}

// Mobile drawer styles
.ant-drawer {
  .ant-drawer-body {
    padding: 0;
    
    #aside {
      height: 100%;
      
      .ant-layout-sider-children {
        height: 100%;
      }
    }
  }
}