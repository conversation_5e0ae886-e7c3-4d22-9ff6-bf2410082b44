import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { connect } from 'react-redux';
import { Drawer } from 'antd';

import * as app from '@src/ducks/app.duck';

import './DrawerMenu.scss';

function DrawerMenu({ isShowDrawerMenu, ...props }) {
  const location = useLocation();

  const ref = useRef(null);
  const isShow = useRef(false);

  useEffect(() => {
    if (isShowDrawerMenu) props.setShowDrawerMenu(false);
  }, [location]);

  useEffect(() => {
    setTimeout(() => {
      isShow.current = isShowDrawerMenu;
    }, 100);
  }, [isShowDrawerMenu]);

  function onOutsideClick() {
    if (isShow.current) {
      props.setShowDrawerMenu(false);
    }
  }

  const handleClick = (e) => {
    if (ref.current && !ref.current.contains(e.target)) {
      onOutsideClick();
    }
  };

  useEffect(() => {
    window.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('click', handleClick);
      props.setShowDrawerMenu(false);
    };
  }, []);

  return <div className="drawer-menu-container">
    <Drawer
      width={null}
      rootClassName="drawer-menu"
      placement="left"
      closable={false}
      open={isShowDrawerMenu}
      getContainer={false}
    >
      <div className="drawer-menu-aside" ref={ref}>
        {props.children}
      </div>
    </Drawer>
  </div>;
}

function mapStateToProps(store) {
  const { isShowDrawerMenu } = store.app;
  return { isShowDrawerMenu };
}

const mapDispatchToProps = { ...app.actions };

export default connect(mapStateToProps, mapDispatchToProps)(DrawerMenu);