.shop-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 0;
  height: auto;
  line-height: normal;

  .shop-header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }

  // Top Header
  .shop-header-top {
    padding: 8px 0;
    background: #f8f9fa;
    font-size: 12px;
    
    .header-contact {
      color: #666;
      
      span {
        margin-right: 16px;
      }
    }
    
    .header-links {
      a {
        color: #666;
        margin-left: 16px;
        text-decoration: none;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  // Main Header
  .shop-header-main {
    padding: 16px 0;
    
    .shop-logo {
      display: flex;
      align-items: center;
      text-decoration: none;
      
      img {
        height: 40px;
        margin-right: 8px;
      }
      
      .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: #1890ff;
      }
    }
    
    .shop-search {
      .search-input {
        .ant-input {
          border-radius: 8px 0 0 8px;
          height: 42px;
          font-size: 14px;
          padding: 0 16px;
        }
        
        .ant-btn {
          border-radius: 0 8px 8px 0;
          height: 42px;
          background: #1890ff;
          border-color: #1890ff;
          font-weight: 500;
          min-width: 80px;
          
          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
    }
    
    .shop-actions {
      display: flex;
      justify-content: flex-end;
      
      .mobile-menu-btn {
        display: none;
        
        @media (max-width: 768px) {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border: 1px solid #d9d9d9;
          border-radius: 8px;
        }
      }
      
      .action-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #666;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.2s;
        
        &:hover {
          color: #1890ff;
          background: #f5f5f5;
        }
        
        .action-text {
          font-size: 12px;
          margin-top: 4px;
          
          @media (max-width: 992px) {
            display: none;
          }
        }
        
        &.cart-button {
          color: #1890ff;
          font-weight: 500;
        }
      }
      
      .user-button {
        height: auto;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        
        .user-name {
          @media (max-width: 992px) {
            display: none;
          }
        }
      }
      
      .login-button {
        height: 40px;
        border-radius: 8px;
        font-weight: 500;
      }
    }
  }

  // Navigation
  .shop-header-nav {
    background: #f8f9fa;
    border-top: 1px solid #e8e8e8;
    
    .main-navigation {
      border-bottom: none;
      background: transparent;
      
      .ant-menu-item {
        font-weight: 500;
        
        &:hover {
          color: #1890ff;
        }
        
        &.ant-menu-item-selected {
          color: #1890ff;
          border-bottom-color: #1890ff;
        }
      }
      
      .ant-menu-submenu {
        .ant-menu-submenu-title:hover {
          color: #1890ff;
        }
      }
    }
  }

  // Mobile Menu
  .mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e8e8e8;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    
    .mobile-search-section {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .mobile-search-input {
        .ant-input {
          height: 40px;
          border-radius: 8px 0 0 8px;
        }
        
        .ant-btn {
          height: 40px;
          border-radius: 0 8px 8px 0;
          background: #1890ff;
          border-color: #1890ff;
        }
      }
    }
    
    .ant-menu {
      border-right: none;
      
      .ant-menu-item {
        margin: 0;
        padding-left: 24px;
        height: 48px;
        line-height: 48px;
        
        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .shop-header-top {
      .header-contact span:last-child,
      .header-links a:not(:first-child) {
        display: none;
      }
    }
    
    .shop-actions {
      .action-text {
        display: none;
      }
      
      .user-name {
        display: none;
      }
    }
  }

  @media (max-width: 576px) {
    .shop-header-container {
      padding: 0 12px;
    }
    
    .shop-header-main {
      padding: 12px 0;
    }
    
    .shop-logo {
      .logo-text {
        font-size: 20px;
      }
    }
  }
}