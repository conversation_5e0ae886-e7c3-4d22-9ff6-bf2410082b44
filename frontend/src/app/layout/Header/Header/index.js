import React, { useState } from 'react';
import { Layout, Row, Col, Input, Badge, Button, Dropdown, Avatar, Space, Menu } from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  UserOutlined,
  SearchOutlined,
  MenuOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';
import { useAuth } from '@app/hooks/useAuth';
import { LINK } from '@link';
import './Header.scss';

const { Header: AntHeader } = Layout;
const { Search } = Input;

const Header = ({ user, cartItemCount = 0, wishlistCount = 0 }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const handleSearch = (value) => {
    if (value.trim()) {
      navigate(`${LINK.SHOP.SEARCH}?q=${encodeURIComponent(value)}`);
    }
  };

  const handleLogin = () => {
    const redirectUrl = location.pathname + location.search;
    navigate(`/auth?redirect=${encodeURIComponent(redirectUrl)}`);
  };

  const handleLogout = () => {
    navigate('/logout');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân',
      onClick: () => navigate(LINK.SHOP.PROFILE)
    },
    {
      key: 'orders',
      icon: <SettingOutlined />,
      label: 'Đơn hàng của tôi',
      onClick: () => navigate(LINK.SHOP.ORDERS)
    },
    {
      key: 'wishlist',
      icon: <HeartOutlined />,
      label: 'Danh sách yêu thích',
      onClick: () => navigate(LINK.SHOP.WISHLIST)
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: handleLogout
    }
  ];

  const navigationItems = [
    {
      key: LINK.SHOP.HOME,
      label: <Link to={LINK.SHOP.HOME}>Trang chủ</Link>
    },
    {
      key: LINK.SHOP.PRODUCTS,
      label: <Link to={LINK.SHOP.PRODUCTS}>Sản phẩm</Link>
    },
    {
      key: 'categories',
      label: 'Danh mục',
      children: [
        { key: 'phone', label: <Link to={`${LINK.SHOP.CATEGORY}/dien-thoai`}>Điện thoại</Link> },
        { key: 'laptop', label: <Link to={`${LINK.SHOP.CATEGORY}/laptop`}>Laptop</Link> },
        { key: 'tablet', label: <Link to={`${LINK.SHOP.CATEGORY}/tablet`}>Tablet</Link> },
        { key: 'accessories', label: <Link to={`${LINK.SHOP.CATEGORY}/phu-kien`}>Phụ kiện</Link> }
      ]
    }
  ];

  return (
    <AntHeader className="shop-header">
      <div className="shop-header-container">
        {/* Main Header */}
        <div className="shop-header-main">
          <Row justify="space-between" align="middle" gutter={16}>
            {/* Logo */}
            <Col xs={8} sm={6} md={4} lg={4}>
              <Link to={LINK.SHOP.HOME} className="shop-logo">
                <img src="/images/logo.png" alt="ShopMart" />
                <span className="logo-text">ShopMart</span>
              </Link>
            </Col>

            {/* Search - Desktop Only */}
            <Col xs={0} sm={0} md={10} lg={12}>
              <div className="shop-search">
                <Search
                  placeholder="Tìm sản phẩm, thương hiệu..."
                  size="large"
                  onSearch={handleSearch}
                  enterButton="Tìm kiếm"
                  className="search-input"
                />
              </div>
            </Col>

            {/* Actions */}
            <Col xs={16} sm={18} md={10} lg={8}>
              <div className="shop-actions">
                <Space size="middle">
                  {/* Mobile Menu Button */}
                  <Button
                    className="mobile-menu-btn"
                    icon={<MenuOutlined />}
                    onClick={() => setMobileMenuVisible(!mobileMenuVisible)}
                  />

                  {/* Wishlist */}
                  <Link to={LINK.SHOP.WISHLIST} className="action-button">
                    <Badge count={wishlistCount} size="small">
                      <HeartOutlined style={{ fontSize: '20px' }} />
                    </Badge>
                    <span className="action-text">Yêu thích</span>
                  </Link>

                  {/* Cart */}
                  <Link to={LINK.SHOP.CART} className="action-button cart-button">
                    <Badge count={cartItemCount} size="small">
                      <ShoppingCartOutlined style={{ fontSize: '20px' }} />
                    </Badge>
                    <span className="action-text">Giỏ hàng</span>
                  </Link>

                  {/* User Menu */}
                  {isAuthenticated ? (
                    <Dropdown
                      menu={{ items: userMenuItems }}
                      placement="bottomRight"
                      trigger={['click']}
                    >
                      <Button className="user-button">
                        <Space>
                          <Avatar size="small" icon={<UserOutlined />} />
                          <span className="user-name">{user?.name || 'User'}</span>
                        </Space>
                      </Button>
                    </Dropdown>
                  ) : (
                    <Button
                      type="primary"
                      icon={<LoginOutlined />}
                      onClick={handleLogin}
                      className="login-button"
                    >
                      Đăng nhập
                    </Button>
                  )}
                </Space>
              </div>
            </Col>
          </Row>
        </div>

        {/* Navigation */}
        <div className="shop-header-nav">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={navigationItems}
            className="main-navigation"
          />
        </div>

        {/* Mobile Menu */}
        {mobileMenuVisible && (
          <div className="mobile-menu">
            {/* Mobile Search - Priority */}
            <div className="mobile-search-section">
              <Search
                placeholder="Tìm sản phẩm, thương hiệu..."
                onSearch={(value) => {
                  handleSearch(value);
                  setMobileMenuVisible(false);
                }}
                enterButton="Tìm"
                size="large"
                className="mobile-search-input"
              />
            </div>

            <Menu
              mode="vertical"
              selectedKeys={[location.pathname]}
              items={navigationItems}
              onClick={() => setMobileMenuVisible(false)}
            />
          </div>
        )}
      </div>
    </AntHeader>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Header);
