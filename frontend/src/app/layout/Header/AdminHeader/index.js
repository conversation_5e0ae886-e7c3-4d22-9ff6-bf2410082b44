import React, { useMemo } from 'react';
import { Layout, Button } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { connect } from 'react-redux';

import useWindowDimensions from '@common/windowDimensions';
import HeaderBreadcrumb from '@app/layout/Header/HeaderBreadcrumb';

import { SCREEN_PC } from '@constant';
import './AdminHeader.scss';

const AdminHeader = ({ isShowDrawerMenu, onToggleDrawer, user }) => {
  const { width } = useWindowDimensions();
  
  const isShowMenuBars = useMemo(() => width < SCREEN_PC, [width]);
  
  function handleShowDrawer() {
    if (!isShowDrawerMenu) onToggleDrawer(true);
  }
  
  return (
    <Layout.Header className="admin-header">
      <div className="admin-header-content">
        {/* Mobile Menu Button */}
        {isShowMenuBars && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={handleShowDrawer}
            className="mobile-menu-btn"
          />
        )}
        
        {/* Breadcrumb */}
        <div className="admin-breadcrumb">
          <HeaderBreadcrumb />
        </div>
        
        {/* User Info */}
        <div className="admin-user">
          <span className="user-name">{user?.fullName || 'Admin'}</span>
        </div>
      </div>
    </Layout.Header>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminHeader);
