.admin-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
  height: 64px;
  line-height: 64px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .admin-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;

    .mobile-menu-btn {
      padding: 0;
      margin-right: 16px;
      font-size: 18px;
      color: #666;
      
      &:hover {
        color: #1890ff;
        background: #f6ffed;
      }
    }

    .admin-breadcrumb {
      flex: 1;
      
      // Override breadcrumb styles for admin
      .ant-breadcrumb {
        font-size: 16px;
        
        .ant-breadcrumb-link {
          color: #666;
          
          &:hover {
            color: #1890ff;
          }
        }
        
        .ant-breadcrumb-separator {
          color: #d9d9d9;
        }
      }
    }

    .admin-user {
      display: flex;
      align-items: center;
      
      .user-name {
        color: #666;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    padding: 0 8px;
    
    .admin-header-content {
      .admin-user {
        .user-name {
          display: none;
        }
      }
    }
  }
}
