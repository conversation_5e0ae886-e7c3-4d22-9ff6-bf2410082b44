import { useMemo } from 'react';
import { connect } from 'react-redux';
import { Layout } from 'antd';
import { Link, matchPath, useLocation } from 'react-router-dom';

import useWindowDimensions from '@common/windowDimensions';

import { LINK } from '@link';
import * as app from '@src/ducks/app.duck';

import ShopHeader from '@src/app/layout/Header/Header';
import AdminHeader from '@src/app/layout/Header/AdminHeader';

const Header = ({ isShowDrawerMenu, ...props }) => {
  const { pathname } = useLocation();
  const { width } = useWindowDimensions();

  // Shop pages và pages thường: Dùng header đầy đủ
  if (pathname.includes(LINK.SHOP_PAGE) || pathname.includes(LINK.WELCOME)) {
    return <ShopHeader />;
  }

  // Admin pages: Chỉ breadcrumb đơn giản
  if (pathname.includes(LINK.ADMIN_PAGE)) {
    return <AdminHeader isShowDrawerMenu={isShowDrawerMenu} onToggleDrawer={props.setShowDrawerMenu} />;
  }

  // Default: Header cũ cho các page khác
  return <ShopHeader />;
};

function mapStateToProps(store) {
  const { isShowDrawerMenu } = store.app;
  return { isShowDrawerMenu };
}

const mapDispatchToProps = { ...app.actions };
export default connect(mapStateToProps, mapDispatchToProps)(Header);
