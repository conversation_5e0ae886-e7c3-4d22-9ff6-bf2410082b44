import { Outlet, Route, Routes, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Typography, Row, Col } from 'antd';
import { ShoppingCartOutlined, SafetyOutlined, TruckOutlined } from '@ant-design/icons';

import Login from './components/Login';
import ForgotPassword from './components/ForgotPassword';
import Register from '@app/auth/components/Register';

import './AuthPage.scss';
import { CONSTANT } from '@constant';

import * as app from '@src/ducks/app.duck';
import SelectLanguage from '../component/SelectLanguage';
import ModalResendEmail from './components/ModalResendEmail';

const { Title, Text } = Typography;

export const AuthLayout = () => {
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();
  const authMessageRef = useRef();

  const [isShowModalResend, setShowModalResend] = useState(false);
  const [countSubmit, setCountSubmit] = useState(0);
  const [showRequest, setShowRequest] = useState(false);
  const [authMessageProp, setAuthMessageProp] = useState({
    authTitle: '',
    authMessage: '',
    authStatus: '',
    email: ''
  });

  useEffect(() => {
    setAuthMessageProp({
      authTitle: '',
      authMessage: '',
      authStatus: '',
      email: ''
    });
    setShowRequest(false);
  }, [location]);

  useEffect(() => {
    if (countSubmit) {
      scrollToAuthMessage();
    }
  }, [countSubmit]);

  const handleChangeLang = lang => {
    if (i18n.language !== lang) {
      dispatch(app.actions.setLanguage(lang));
      i18n.changeLanguage(lang);
    }
  };

  const scrollToAuthMessage = () => {
    const messageElement = authMessageRef?.current;
    if (!messageElement) return;
    const rect = messageElement?.getBoundingClientRect();

    // Kiểm tra nếu div nằm ngoài khung nhìn (viewport)
    if (rect.top < 0 || rect.bottom > (window.innerHeight || document.documentElement.clientHeight)) {
      messageElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const authMessageClassname = useMemo(() => {
    if (authMessageProp.authStatus === CONSTANT.SUCCESS) {
      return 'auth-success';
    } else if (authMessageProp.authStatus === CONSTANT.WARNING) {
      return 'auth-warning';
    }
    return 'auth-error';
  }, [authMessageProp.authStatus]);

  const onToggleModalResend = () => {
    setShowModalResend(pre => !pre);
  };

  return (
    <div id="auth" className="ecommerce-auth">
      <Row className="auth-layout">
        {/* Left Side - Branding */}
        <Col xs={0} md={12} lg={14} className="auth-branding">
          <div className="branding-content">
            <div className="brand-logo">
              <img src="/images/logo.png" alt="ShopMart" />
              <Title level={1} className="brand-title">ShopMart</Title>
            </div>
            
            <Title level={2} className="brand-welcome">
              {t('AUTH_BRAND_WELCOME')}
            </Title>
            <Text className="brand-subtitle">
              {t('AUTH_BRAND_SUBTITLE')}
            </Text>

            <div className="brand-features">
              <div className="feature-item">
                <ShoppingCartOutlined className="feature-icon" />
                <div className="feature-text">
                  <Text strong>{t('AUTH_BRAND_FEATURE_PRODUCTS')}</Text>
                  <Text>{t('AUTH_BRAND_FEATURE_PRODUCTS_DESC')}</Text>
                </div>
              </div>
              
              <div className="feature-item">
                <TruckOutlined className="feature-icon" />
                <div className="feature-text">
                  <Text strong>{t('AUTH_BRAND_FEATURE_DELIVERY')}</Text>
                  <Text>{t('AUTH_BRAND_FEATURE_DELIVERY_DESC')}</Text>
                </div>
              </div>
              
              <div className="feature-item">
                <SafetyOutlined className="feature-icon" />
                <div className="feature-text">
                  <Text strong>{t('AUTH_BRAND_FEATURE_AUTHENTIC')}</Text>
                  <Text>{t('AUTH_BRAND_FEATURE_AUTHENTIC_DESC')}</Text>
                </div>
              </div>
            </div>
          </div>
        </Col>

        {/* Right Side - Auth Form */}
        <Col xs={24} md={12} lg={10} className="auth-form-section">
          <div className="auth-container">
            <div className="auth-content">
              <Outlet context={{ setAuthMessageProp, setCountSubmit, setShowRequest }}/>
            </div>
            
            {authMessageProp.authMessage && (
              <div ref={authMessageRef} className={`auth-message ${authMessageClassname}`}>
                {authMessageProp?.authTitle && (
                  <div className="auth-message__title">
                    {t(authMessageProp.authTitle)}
                  </div>
                )}
                <div className="auth-message__content">
                  {authMessageProp?.authStatus === CONSTANT.SUCCESS
                    ? t(authMessageProp.authMessage).format(authMessageProp.email)
                    : t(authMessageProp?.authMessage)}

                  {showRequest && (
                    <div className="auth-message__content__resend">
                      {t('RESEND_ACTIVATION_EMAIL')}?
                      <span className="auth-message__content__request" onClick={onToggleModalResend}>
                        {t('REQUEST')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          <div className="auth-select-language">
            <SelectLanguage/>
          </div>
        </Col>
      </Row>
      
      <ModalResendEmail open={isShowModalResend} onCancel={onToggleModalResend}/>
    </div>
  );
};

const AuthPage = () => (
  <Routes>
    <Route element={<AuthLayout/>}>
      <Route path="login" element={<Login/>}/>
      <Route path="forgot-password" element={<ForgotPassword/>}/>
      <Route path="sign-up" element={<Register/>}/>
      <Route index element={<Login/>}/>
    </Route>
  </Routes>
);

export default AuthPage;

