@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

// Variables
$primary-color: #1890ff;
$primary-light: #40a9ff;
$primary-dark: #096dd9;
$secondary-color: #52c41a;
$accent-color: #722ed1;

$gray-50: #fafafa;
$gray-100: #f5f5f5;
$gray-200: #f0f0f0;
$gray-300: #d9d9d9;
$gray-600: #595959;
$gray-700: #434343;
$gray-800: #262626;

$white: #ffffff;
$success: #52c41a;
$warning: #faad14;
$error: #ff4d4f;

// Mixins
@mixin card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

@mixin card-shadow-hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

@mixin transition($property: all, $duration: 0.3s) {
  transition: $property $duration cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin gradient-bg($color1, $color2) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
}

.ecommerce-auth {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: $gray-50;

  .auth-layout {
    min-height: 100vh;
    margin: 0;
  }

  // Left Side - Branding
  .auth-branding {
    @include gradient-bg($primary-color, $primary-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .branding-content {
      position: relative;
      z-index: 1;
      text-align: center;
      color: $white;
      max-width: 500px;

      .brand-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 40px;

        img {
          height: 60px;
          margin-right: 16px;
        }

        .brand-title {
          color: $white;
          margin: 0;
          font-size: 3rem;
          font-weight: 800;
          background: linear-gradient(45deg, #ffd700, #ff6b6b);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .brand-welcome {
        color: $white;
        margin-bottom: 16px;
        font-size: 2rem;
        font-weight: 700;
      }

      .brand-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.125rem;
        margin-bottom: 60px;
        display: block;
        line-height: 1.6;
      }

      .brand-features {
        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 32px;
          text-align: left;

          .feature-icon {
            font-size: 32px;
            color: rgba(255, 255, 255, 0.9);
            margin-right: 20px;
            flex-shrink: 0;
          }

          .feature-text {
            display: flex;
            flex-direction: column;

            .ant-typography {
              color: $white;
              margin: 0;

              &:first-child {
                font-size: 16px;
                margin-bottom: 4px;
              }

              &:last-child {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  // Right Side - Auth Form
  .auth-form-section {
    display: flex;
    flex-direction: column;
    background: $white;
    position: relative;

    .auth-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 40px;
      max-width: 400px;
      margin: 0 auto;
      width: 100%;

      @media (max-width: 768px) {
        padding: 24px;
        max-width: none;
      }

      .auth-content {
        .auth-content__title {
          font-size: 2rem;
          font-weight: 700;
          color: $gray-800;
          text-align: center;
          margin-bottom: 32px;
        }

        .auth-content__form {
          margin-bottom: 24px;

          .ant-form-item {
            margin-bottom: 20px;

            .ant-form-item-label {
              padding-bottom: 4px;

              label {
                font-weight: 500;
                color: $gray-700;
              }
            }

            .ant-input,
            .ant-input-password {
              height: 48px;
              border-radius: 8px;
              border: 1px solid $gray-300;
              @include transition(border-color);

              &:hover,
              &:focus {
                border-color: $primary-color;
              }

              &::placeholder {
                color: $gray-600;
              }
            }

            .ant-input-password {
              .ant-input {
                height: auto;
                border: none;
                box-shadow: none;

                &:focus {
                  box-shadow: none;
                }
              }
            }

            .ant-form-item-explain-error {
              font-size: 12px;
              margin-top: 4px;
            }
          }
        }

        .auth-content__redirect {
          display: block;
          text-align: right;
          color: $primary-color;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 24px;
          @include transition(color);

          &:hover {
            color: $primary-light;
          }
        }

        .auth-content__submit {
          height: 48px;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 24px;
          @include transition();

          &:hover {
            transform: translateY(-1px);
            @include card-shadow-hover();
          }
        }

        .auth-content__question {
          text-align: center;
          color: $gray-600;
          font-size: 14px;

          .auth-content__question__redirect {
            color: $primary-color;
            font-weight: 500;
            margin-left: 8px;
            @include transition(color);

            &:hover {
              color: $primary-light;
            }
          }
        }
      }
    }

    .auth-select-language {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 10;
      
      .language-switcher {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid $gray-300;
        border-radius: 8px;
        padding: 8px 12px;
        @include transition();
        
        &:hover {
          background: $white;
          @include card-shadow();
        }
        
        .ant-select {
          border: none;
          background: transparent;
          
          .ant-select-selector {
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
            padding: 0;
            height: auto;
            
            .ant-select-selection-item {
              color: $gray-700;
              font-weight: 500;
              padding: 0;
            }
          }
          
          .ant-select-arrow {
            color: $gray-600;
          }
        }
      }
    }
  }

  // Auth Messages
  .auth-message {
    margin-top: 24px;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid;

    &.auth-success {
      background: #f6ffed;
      border-left-color: $success;
      color: #389e0d;
    }

    &.auth-warning {
      background: #fffbe6;
      border-left-color: $warning;
      color: #d48806;
    }

    &.auth-error {
      background: #fff2f0;
      border-left-color: $error;
      color: #cf1322;
    }

    .auth-message__title {
      font-weight: 600;
      margin-bottom: 8px;
    }

    .auth-message__content {
      line-height: 1.5;

      .auth-message__content__resend {
        margin-top: 8px;
        font-size: 14px;

        .auth-message__content__request {
          color: $primary-color;
          cursor: pointer;
          font-weight: 500;
          margin-left: 8px;
          @include transition(color);

          &:hover {
            color: $primary-light;
          }
        }
      }
    }
  }

  // Google Section Styles
  .google-section {
    margin-bottom: 24px;

    .google-divider {
      display: flex;
      align-items: center;
      margin: 24px 0;
      color: $gray-600;
      font-size: 14px;

      &::before,
      &::after {
        content: '';
        flex: 1;
        height: 1px;
        background: $gray-300;
      }

      &::before {
        margin-right: 16px;
      }

      &::after {
        margin-left: 16px;
      }
    }

    .google-login-btn {
      height: 48px;
      border-radius: 8px;
      border: 1px solid $gray-300;
      background: $white;
      width: 100%;
      @include transition();

      &:hover {
        border-color: $primary-color;
        @include card-shadow();
      }

      .google-icon {
        margin-right: 12px;
        width: 20px;
        height: 20px;
      }
    }
  }

  // Mobile Responsive
  @media (max-width: 768px) {
    .auth-branding {
      display: none;
    }

    .auth-form-section {
      .auth-container {
        justify-content: flex-start;
        padding-top: 60px;
      }
    }

    .auth-select-language {
      top: 16px;
      right: 16px;
    }
  }

  @media (max-width: 576px) {
    .auth-form-section {
      .auth-container {
        padding: 20px 16px;
        padding-top: 60px;

        .auth-content {
          .auth-content__title {
            font-size: 1.5rem;
            margin-bottom: 24px;
          }
        }
      }
    }
  }

  // Smooth animations
  .auth-content,
  .branding-content {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}