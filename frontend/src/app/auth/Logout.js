import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Navigate, Routes } from 'react-router-dom';

import * as auth from '@src/ducks/auth.duck';

function Logout() {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(auth.actions.logout());
  }, [dispatch]);

  return (
    <Routes>
      <Navigate to="/auth/login"/>
    </Routes>
  );
}

export default Logout;
