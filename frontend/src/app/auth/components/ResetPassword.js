import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Form, Input } from 'antd';
import { useLocation, useNavigate, useOutletContext } from 'react-router-dom';
import queryString from 'query-string';

import { BUTTON, CONSTANT } from '@constant';

import { requestResetPassword } from '@services/Auth';

import * as auth from '@src/ducks/auth.duck';
import AntButton from '@component/AntButton';
import { AntForm } from '@src/app/component/AntForm';

function ResetPassword({ user, ...props }) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const { resetPasswordToken } = queryString.parseUrl(location.search)?.query;
  const navigate = useNavigate();
  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  const isLogout = !user || user === CONSTANT.INITIAL;

  const [password, setPassword] = useState('');

  useEffect(() => {
    if (isLogout && password) {
      resetPassword(password);
    }
  }, [isLogout, password]);

  async function resetPassword(password) {
    const apiResponse = await requestResetPassword(resetPasswordToken, { password });
    if (apiResponse?.code === 200 && apiResponse?.data) {
      setAuthMessageProp({
        authStatus: CONSTANT.SUCCESS,
        authTitle: 'AUTH_TITLE_RESET_PASSWORD_SUCCESS',
        authMessage: 'AUTH_MESSAGE_GET_BACK_TO_LOGIN',
      });
      setTimeout(() => {
        navigate('/auth');
      }, 3000);
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: 'AUTHENTICATION_FAILED',
        authMessage: apiResponse?.message || 'TOKEN_EXPIRED',
      });
    }
    setPassword('');
    setCountSubmit(pre => pre + 1);
  }

  const onSubmit = (values) => {
    setPassword(values.password.trim());
    if (!isLogout) {
      props.logout();
    }
  };

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  };

  return (<>
      <div className="auth-content__title">{t('SET_A_PASSWORD')}</div>

      <AntForm
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        className="auth-content__form"
      >
        <AntForm.Item
          name="password"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t('CAN_NOT_BE_BLANK')));
              }
              if (value.trim().length >= 6) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t('MINIMUM_6_CHARACTERS_PASSWORD')));
            },
          }),]}
        >
          <Input.Password size="large" placeholder={t('CREATE_PASSWORD')} onFocus={() => handleFocus('password')}/>
        </AntForm.Item>

        <AntForm.Item
          name="confirm"
          dependencies={['password']}
          validateTrigger="onBlur"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(new Error(t('CAN_NOT_BE_BLANK')));
                }
                if (getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t('VALIDATE_MESSAGE_CONFIRM_PASSWORD')));
              },
            }),
          ]}
        >
          <Input.Password size="large" placeholder={t('RE_ENTER_PASSWORD')} onFocus={() => handleFocus('confirm')}/>
        </AntForm.Item>
      </AntForm>

      <AntButton
        block
        size="large"
        className="auth-content__submit"
        type={BUTTON.DEEP_NAVY}
        // disabled={disableSubmit}
        onClick={form.submit}
        loading={!!password}
      >
        {t('SET_PASSWORD')}
      </AntButton>
    </>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(ResetPassword));

