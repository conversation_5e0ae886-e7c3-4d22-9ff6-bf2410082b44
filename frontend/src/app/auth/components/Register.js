import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate, useOutletContext } from 'react-router-dom';
import { Form, Input } from 'antd';
import { trim } from 'lodash';

import AntButton from '@component/AntButton';

import { LINK } from '@link';
import { BUTTON, CONSTANT } from '@constant';
import RULE from '@rule';

import { register } from '@services/Auth';

import * as auth from '@src/ducks/auth.duck';
import { AntForm } from '@src/app/component/AntForm';

function Register() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  const [form] = Form.useForm();

  const handleSubmit = async (data) => {
    const dataRequest = {
      ...data,
      fullName: trim(data.fullName),
      password: trim(data.password),
    };
    const apiResponse = await register(dataRequest);
    if (apiResponse?.code === 200) {
      if (apiResponse?.data?._id) {
        setAuthMessageProp({
          authStatus: CONSTANT.SUCCESS,
          authTitle: 'AUTH_TITLE_SIGN_UP_SUCCESS',
          authMessage: 'AUTH_MESSAGE_REGISTER_SUCCESS',
        });
      } else {
        setAuthMessageProp({
          authStatus: CONSTANT.WARNING,
          authTitle: 'AUTH_TITLE_SIGN_UP_WAITING_LIST',
          authMessage: 'AUTH_MESSAGE_SIGN_UP_WAITING_LIST',
        });
      }
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: 'AUTHENTICATION_SIGNUP_FAILED',
        authMessage: apiResponse?.message || t('AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR'),
      });
    }
    setCountSubmit(pre => pre + 1);
  };

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  };

  return (
    <>
      <div className="auth-content__title">{t('AUTH_REGISTER_TITLE')}</div>

      <AntForm
        className="auth-content__form"
        form={form}
        requiredMark={false}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ email: '', password: '' }}
      >
        <AntForm.Item
          label={t('AUTH_REGISTER_FULLNAME_LABEL')}
          name="fullName"
          rules={[RULE.REQUIRED]}
          validateTrigger="onBlur"
        >
          <Input 
            size="large" 
            placeholder={t('AUTH_REGISTER_FULLNAME_PLACEHOLDER')} 
            onFocus={() => handleFocus('fullName')}
          />
        </AntForm.Item>

        <AntForm.Item
          label={t('AUTH_REGISTER_EMAIL_LABEL')}
          name="email"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateTrigger="onBlur"
          validateFirst
        >
          <Input 
            size="large" 
            placeholder={t('AUTH_REGISTER_EMAIL_PLACEHOLDER')} 
            onFocus={() => handleFocus('email')}
          />
        </AntForm.Item>

        <AntForm.Item
          label={t('AUTH_REGISTER_PASSWORD_LABEL')}
          name="password"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t('AUTH_VALIDATION_PASSWORD_REQUIRED')));
              } else if (value.trim().length >= 6) {
                return Promise.resolve();
              } else return Promise.reject(new Error(t('AUTH_VALIDATION_PASSWORD_MIN_LENGTH')));
            },
          }),]}
        >
          <Input.Password 
            size="large" 
            placeholder={t('AUTH_REGISTER_PASSWORD_PLACEHOLDER')} 
            onFocus={() => handleFocus('password')}
          />
        </AntForm.Item>

        <AntForm.Item
          label={t('AUTH_REGISTER_CONFIRM_PASSWORD_LABEL')}
          name="confirm"
          dependencies={['password']}
          validateTrigger="onBlur"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(new Error(t('AUTH_VALIDATION_CONFIRM_PASSWORD_REQUIRED')));
                }
                if (getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t('AUTH_VALIDATION_CONFIRM_PASSWORD_MISMATCH')));
              },
            }),
          ]}
        >
          <Input.Password 
            size="large" 
            placeholder={t('AUTH_REGISTER_CONFIRM_PASSWORD_PLACEHOLDER')} 
            onFocus={() => handleFocus('confirm')}
          />
        </AntForm.Item>
      </AntForm>

      <AntButton
        block
        size="large"
        className="auth-content__submit"
        type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      >
        {t('AUTH_REGISTER_SUBMIT')}
      </AntButton>

      <div className="auth-content__question">
        <span>{t('AUTH_REGISTER_HAVE_ACCOUNT')}</span>
        <Link to="" className="auth-content__question__redirect">
          {t('AUTH_REGISTER_LOGIN_LINK')}
        </Link>
      </div>
    </>);
}

function mapStateToProps(store) {
  return {};
}

export default (connect(mapStateToProps, auth.actions)(Register));
