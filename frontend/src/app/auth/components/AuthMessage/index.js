import { useTranslation } from 'react-i18next';

import './AuthMessage.scss';

function AuthMessage({ authMessage = '', isSuccess = false, authTitle = '', email = '' }) {
  const { t } = useTranslation();
  if (!authMessage) return <></>;
  return <div className={`auth-message ${isSuccess ? 'auth-success' : 'auth-error'}`}>
    <div className="auth-message__title">
      {t(authTitle)}
    </div>
    <div className="auth-message__content">
      {isSuccess ? t(authMessage).format(email) : t(authMessage)}
    </div>
  </div>;
}

export default AuthMessage;
