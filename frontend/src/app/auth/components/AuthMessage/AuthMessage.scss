.auth-message {
  border-radius: 4px;
  display: flex;
  padding: 8px 24px;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 8px;


  &.auth-error {
    background: var(--support-colours-red-light-1);
    color: var(--support-colours-red);
  }

  &.auth-success {
    background: var(--support-colours-green-light-1);

    .auth-message__title {
      color: var(--typo-colours-support-green);
      text-align: center;
    }

    .auth-message__content {
      color: var(--typo-colours-support-blue-dark);
      padding: 0 16px;
    }
  }

  .auth-message__title {
    font-size: 24px;
    font-weight: 600;
  }

  .auth-message__content {
    text-align: center;
  }
}
