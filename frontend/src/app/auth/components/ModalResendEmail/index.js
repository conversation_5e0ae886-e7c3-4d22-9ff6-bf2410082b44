import AntModal from '@src/app/component/AntModal';
import { AntForm } from '@src/app/component/AntForm';
import RULE from '@rule';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { resendActivationEmail } from '@src/app/services/User';
import { toast } from '@src/app/component/ToastProvider';
import { useEffect } from 'react';

const ModalResendEmail = ({ open, onCancel }) => {
  const { t } = useTranslation();

  const [form] = Form.useForm();

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open]);

  const onSubmit = async (values) => {
    const response = await resendActivationEmail({ email: values.email });
    if (response) {
      toast.success(response.message);
      onCancel();
    }
  };

  return (
    <AntModal
      open={open}
      title={t('RESEND_ACTIVATION_EMAIL')}
      width={512}
      onCancel={onCancel}
      okText={t('REQUEST')}
      formId="form-resend-email"
      alignFooter="center"
    >
      <AntForm
        requiredMark={false}
        id="form-resend-email"
        form={form}
        layout="vertical" onFinish={onSubmit}
        className="auth-content__form"
      >
        <AntForm.Item
          name="email"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateFirst
        >
          <Input size="large" placeholder={t('ENTER_EMAIL')}/>
        </AntForm.Item>

      </AntForm>
    </AntModal>
  );
};

export default ModalResendEmail;