import { Link, useOutletContext } from 'react-router-dom';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Form, Input } from 'antd';

import { AntForm } from '@src/app/component/AntForm';
import AntButton from '@component/AntButton';

import { BUTTON, CONSTANT } from '@constant';
import RULE from '@rule';

import { requestForgetPassword } from '@services/Auth';

import CHEVRON_DOWN from '@src/asset/icon/chevron/chevron-down.svg';

import * as auth from '@src/ducks/auth.duck';

function ForgotPassword() {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const values = Form.useWatch([], form);
  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  const handleSubmit = async (data) => {
    const apiResponse = await requestForgetPassword(data);
    if (apiResponse?.code === 200 && apiResponse?.data) {
      setAuthMessageProp({
        authStatus: CONSTANT.SUCCESS,
        authTitle: 'AUTH_TITLE_FORGOT_PASSWORD_SUCCESS',
        authMessage: 'AUTH_MESSAGE_FORGOT_PASSWORD_SUCCESS',
        email: data.email
      });
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: apiResponse?.code === 403 ? 'AUTHENTICATION_WARING' : 'AUTHENTICATION_FAILED',
        authMessage: apiResponse?.message || 'AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR',
      });
    }
    setCountSubmit(pre => pre + 1);
  };

  const handleFocus = () => {
    form.setFields([{
      name: 'email',
      errors: [],
    }]);
  };

  return (<>
      <div className="auth-content__back-to">
        <img src={CHEVRON_DOWN} alt=""/>
        <Link to="/auth">{t('AUTH_FORGOT_BACK_TO_LOGIN')}</Link>
      </div>
      
      <div className="auth-content__title">{t('AUTH_FORGOT_PASSWORD_TITLE')}</div>
      
      <p style={{ 
        color: '#666', 
        marginBottom: '24px', 
        textAlign: 'center',
        lineHeight: '1.5'
      }}>
        {t('AUTH_FORGOT_DESCRIPTION')}
      </p>
      
      <AntForm
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ email: '' }}
        className="auth-content__form"
      >
        <AntForm.Item
          label={t('AUTH_FORGOT_EMAIL_LABEL')}
          name="email"
          validateTrigger="onBlur"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateFirst
        >
          <Input 
            size="large" 
            placeholder={t('AUTH_FORGOT_EMAIL_PLACEHOLDER')} 
            onFocus={handleFocus}
          />
        </AntForm.Item>
      </AntForm>

      <AntButton
        block
        size="large"
        className="auth-content__submit"
        type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      >
        {t('AUTH_FORGOT_SUBMIT')}
      </AntButton>
    </>
  );
}

function mapStateToProps(store) {
  return {};
}

export default (connect(mapStateToProps, auth.actions)(ForgotPassword));
