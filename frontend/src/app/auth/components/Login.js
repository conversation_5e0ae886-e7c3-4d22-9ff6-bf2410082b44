import { connect } from 'react-redux';
import { useTranslation, withTranslation } from 'react-i18next';
import { Form, Input } from 'antd';
import { Link, useOutletContext } from 'react-router-dom';

import AntButton from '@component/AntButton';
import { AntForm } from '@src/app/component/AntForm';

import { BUTTON, CONSTANT } from '@constant';
import RULE from '@rule';

import { login } from '@services/Auth';

import * as auth from '@src/ducks/auth.duck';
import GoogleSection from './LoginGoogle/GoogleSection';

function Login({ history, isLoading, ...props }) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const { setAuthMessageProp, setCountSubmit, setShowRequest } = useOutletContext();

  async function handleLogin(value) {
    const apiResponse = await login(value);
    if (apiResponse) {
      const { data, code } = apiResponse;
      if (code === 200) {
        props.userLoaded(data);
      } else if (code === 202) {
        setAuthMessageProp({
          authTitle: 'AUTHENTICATION_WARING',
          authMessage: data.message,
          authStatus: CONSTANT.WARNING
        });
      } else if (code === 403) {
        setAuthMessageProp({
          authTitle: 'AUTHENTICATION_FAILED',
          authMessage: apiResponse?.message || 'AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR',
          authStatus: CONSTANT.ERROR
        });
        setShowRequest(true);
      } else {
        setAuthMessageProp({
          authTitle: 'AUTHENTICATION_FAILED',
          authMessage: apiResponse?.message || 'AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR',
          authStatus: CONSTANT.ERROR
        });
      }
    } else {
      setAuthMessageProp({
        authTitle: 'AUTHENTICATION_FAILED',
        authMessage: 'AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR',
        authStatus: CONSTANT.ERROR
      });
    }
    setCountSubmit(pre => pre + 1);
  }

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  };

  return (
    <>
      <div className="auth-content__title">{t('AUTH_LOGIN_TITLE')}</div>
      <GoogleSection
        setAuthMessageProp={setAuthMessageProp}
        setCountSubmit={setCountSubmit}
      />
      <AntForm
        requiredMark={false}
        form={form}
        layout="vertical" onFinish={handleLogin}
        className="auth-content__form"
      >
        <AntForm.Item
          label={t('AUTH_LOGIN_EMAIL_LABEL')}
          name="email"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateTrigger="onBlur"
          validateFirst
        >
          <Input size="large" placeholder={t('AUTH_LOGIN_EMAIL_PLACEHOLDER')} onFocus={() => handleFocus('email')}/>
        </AntForm.Item>

        <AntForm.Item 
          label={t('AUTH_LOGIN_PASSWORD_LABEL')}
          name="password" 
          rules={[RULE.REQUIRED]} 
          validateTrigger="onBlur"
        >
          <Input.Password
            size="large"
            placeholder={t('AUTH_LOGIN_PASSWORD_PLACEHOLDER')}
            onFocus={() => handleFocus('password')}
            onPressEnter={form.submit}/>
        </AntForm.Item>
      </AntForm>

      <Link to="forgot-password" className="auth-content__redirect">{t('AUTH_LOGIN_FORGOT_LINK')}</Link>

      <AntButton
        size="large"
        className="auth-content__submit"
        block
        type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      >
        {t('AUTH_LOGIN_SUBMIT')}
      </AntButton>

      <div className="auth-content__question">
        <span>{t('AUTH_LOGIN_NO_ACCOUNT')}</span>
        <Link to="sign-up" className="auth-content__question__redirect">{t('AUTH_LOGIN_SIGNUP_LINK')}</Link>
      </div>
    </>
  );
}

function mapStateToProps(store) {
  return {};
}

export default withTranslation()(connect(mapStateToProps, auth.actions)(Login));

