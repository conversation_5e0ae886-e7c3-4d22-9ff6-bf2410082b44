@mixin markdown-base-styles {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #000;
  overflow-y: auto;
  height: auto;

  h1, h2, h3, h4 {
    margin-top: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #000;
    font-size: 16px;
  }

  p {
    margin-bottom: 12px;
  }

  ul, ol {
    margin-left: 0px;
    margin-bottom: 8px;
    padding-left: 16px;
  }

  li {
    margin-bottom: 8px;
    margin-left: 4px;
  }

  blockquote {
    border-left: 3px solid #e0e0e0;
    padding-left: 12px;
    color: #555;
    font-style: italic;
    margin: 12px 0;
  }

  strong {
    font-weight: 600;
    color: #000;
  }

  &--error {
    color: #d32f2f;
    text-align: center;
    padding: 16px;
  }
}

.markdown-helper {
  @include markdown-base-styles;
}

@mixin markdown-scroll-styling {
  // Giả sử bạn có một mixin scrollbar được đ<PERSON>nh nghĩa ở đâu đó, ví dụ:
  // @import './scroll'; // Nếu scroll.scss chứa mixin scrollbar
  // Hoặc bạn định nghĩa trực tiếp ở đây:
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  // Cần thêm @include scrollbar; nếu bạn muốn giữ lại dòng đó và scroll.scss có định nghĩa
}
