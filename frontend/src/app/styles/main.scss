html {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Segoe UI', serif;
  line-height: 1.25;

  body {
    margin: 0;
    line-height: inherit;
  }

  img {
    user-drag: none;
    -webkit-user-drag: none;
  }
}

#root {
  height: 100vh;
  overflow: hidden;
}

.content {
  flex: 1;
  padding: 32px 18px; // padding 24px scrollbar both-edges -> 18px
  position: relative;
  outline: none !important;
  scrollbar-gutter: stable both-edges;

  @media screen and (max-width: 1023.98px) {
    padding: 24px 16px;
  }
}

.more-vertical-btn {
  display: flex;
  cursor: pointer;

  &.ant-dropdown-open {
    svg {
      rect {
        fill: var(--navy) !important;
      }

      path {
        stroke: white !important;
      }
    }
  }
}

.action-dropdown-menu {
  gap: 0 !important;
}

@for $i from 1 through 10 {
  .line-clamp-#{$i} {
    display: -webkit-box;
    -webkit-line-clamp: #{$i};
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }
}

a {
  transition: all var(--transition-timing) !important;
}