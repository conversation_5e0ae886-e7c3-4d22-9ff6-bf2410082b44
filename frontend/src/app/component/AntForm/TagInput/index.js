import React, { useEffect, useRef, useState } from 'react';
import { Input, Tag } from 'antd';

import { AntForm, useAntForm } from '@component/AntForm';
import { cloneObj, formatUnique } from '@common/functionCommons';

import './TagInput.scss';

export function TagInput({ name, label, max, placeholder }) {
  const antForm = useAntForm();
  const isPasting = useRef(false);

  const [tags, setTags] = useState([]);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (antForm?.form) {
      antForm.form.setFieldsValue({ [name]: tags.join(',') });
      antForm.form.validateFields();
    }
  }, [tags]);

  const handleInputChange = (e) => {
    if (isPasting.current) return;
    setInputValue(e.target.value);
  };

  const handleInputKeyDown = (e) => {
    // Backspace
    if (e.keyCode === 8 && !inputValue) {
      setTags(prevState => {
        const newState = cloneObj(prevState);
        newState.pop();
        return newState;
      });
    }
    // comma
    if (e.keyCode === 188) {
      handleInputConfirm(e);
    }
  };

  const handleInputPaste = (e) => {
    if (inputValue) return;
    isPasting.current = true;

    const pastedText = e.clipboardData.getData('text');
    const pastedArr = !!pastedText ? pastedText.split(',').map(x => x?.trim()).filter(x => !!x) : [];
    const newTagArr = formatUnique([...tags, ...pastedArr]);

    if (JSON.stringify(tags) !== JSON.stringify(newTagArr)) {
      setTags(newTagArr);
    }

    setTimeout(() => {
      isPasting.current = false;
    }, 100);
  };

  const handleInputConfirm = (e) => {
    e.preventDefault();
    const inputData = inputValue?.trim();
    if (inputData && tags.indexOf(inputData) === -1) {
      setTags([...tags, inputData]);
      setInputValue('');
    }
  };

  const handleTagClose = (removedTag) => {
    const updatedTags = tags.filter(tag => tag !== removedTag);
    setTags(updatedTags);
  };

  function shouldUpdate(prevValues, curValues) {
    const tagStr = curValues[name];
    const tagArr = !!tagStr ? tagStr.split(',') : [];

    if (JSON.stringify(tags) !== JSON.stringify(tagArr)) {
      setTags(tagArr);
    }

    return prevValues.additional !== curValues.additional;
  }

  const renderRules = () => ({
    validator(_, value) {
      const tagArr = !!value ? value.split(',') : [];
      if (!!max && Number.isInteger(max) && tagArr.length > max) {
        return Promise.reject(`Please enter less than ${max} words!`);
      }
      return Promise.resolve();
    },
  });

  return <>
    <AntForm.Item
      name={name}
      label={label}
      shouldUpdate={shouldUpdate}
      rules={[renderRules]}
    >
      <Input
        className="tag-input"
        count={{
          show: !!max,
          max: max,
          strategy: (value) => {
            const tagArr = !!value ? value.split(',') : [];
            return tagArr.length;
          },
        }}
        prefix={
          <div className="tag-input__tags" onClick={e => e.stopPropagation()}>
            {tags.map((tag, index) => (
              <Tag
                key={tag + index}
                closable
                onClose={() => handleTagClose(tag)}
              >
                <span className="tag-input__tag-label" title={tag}>
                  {tag}
                </span>
              </Tag>
            ))}
            <Input
              variant="borderless"
              className="tag-input__input"
              onChange={handleInputChange}
              onKeyDown={handleInputKeyDown}
              onPaste={handleInputPaste}
              value={inputValue}
              onPressEnter={handleInputConfirm}
              onBlur={handleInputConfirm}
              {...!tags.length ? { placeholder } : {}}
            />
          </div>
        }
      />
    </AntForm.Item>
  </>;
}