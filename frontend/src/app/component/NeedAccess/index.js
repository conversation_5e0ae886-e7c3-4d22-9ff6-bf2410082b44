import React from 'react';
import { useTranslation } from 'react-i18next';

import CLICKEE_LIGHT from '@src/asset/logo/clickee-light.svg';
import NEED_ACCESS from '@src/asset/image/need-access.png';
import './NeedAccess.scss';

export default function NeedAccess({ ...props }) {
  const { t } = useTranslation();

  return <div className="need-access">
    {/*<div className="need-access__inner">*/}
    <div className="need-access__content">
      <div className="need-access__logo">
        <img src={CLICKEE_LIGHT} alt=""/>
      </div>
      <div className="need-access__text">
        <div className="need-access__text-1">{t('NEED_ACCESS')}</div>
        <div className="need-access__text-2">{t('PLEASE_REQUEST_ACCESS_FOR_CONTENT')}</div>
      </div>
    </div>
    <div className="need-access__image">
      <img src={NEED_ACCESS} alt=""/>
    </div>
    {/*</div>*/}
  </div>;
}

