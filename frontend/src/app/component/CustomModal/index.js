import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import AntButton from '@component/AntButton';
import clsx from 'clsx';

import { BUTTON } from '@constant';

import './CustomModal.scss';

const CustomModal = ({ ...props }) => {
  const {
    okText, cancelText, footer, className, disableOkButton, closeIcon, title,
    disableMaskCloseable, form, width, loadingOkButton, footerAlign
  } = props;
  const { isShowModal, handleOk, handleCancel } = props;
  const { t } = useTranslation();

  const footerClassName = footerAlign ? `footer-align-${footerAlign}` : '';

  return <>
    <Modal
      width={width}
      classNames={{
        mask: 'mask-custom-modal',
        wrapper: 'wrap-custom-modal',
        body: 'custom-modal__content',
      }}
      footer={null}
      closeIcon={closeIcon ? closeIcon : null}
      open={isShowModal}
      onCancel={handleCancel}
      maskClosable={!disableMaskCloseable}
    >
      <div className="custom-modal__title">{title}</div>
      <div className={clsx('custom-modal__body', className)}>
        {props?.children}
      </div>

      {footer || <div className={clsx('custom-modal__footer', footerClassName)}>
        <AntButton
          onClick={handleCancel}
          type={BUTTON.WHITE}
          size="large"
        >
          {cancelText || t('CANCEL')}
        </AntButton>
        <AntButton
          type={BUTTON.DEEP_NAVY}
          size="large"
          disabled={disableOkButton}
          loading={loadingOkButton}
          {...(form ? { htmlType: 'submit', form: form } : { onClick: handleOk })}
        >
          {okText || t('DONE')}
        </AntButton>
      </div>}
    </Modal>
  </>;
};

export default (CustomModal);
