import { useCallback, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';

import { CONSTANT } from '@constant';
import { cloneObj, convertHHMMSSToSecond, getHMS, timeIsValid } from '@common/functionCommons';

import './TimeStepper.scss';

function TimeStepper({ value, disabled, onChange = () => null, ...props }) {
  const [, updateState] = useState();
  const forceUpdate = useCallback(() => updateState({}), []);

  const wrapperRef = useRef(null);
  const itemSelected = useRef(undefined);
  const prevNumber = useRef(null);

  const isCurrentChange = useRef(false);
  const minValue = useRef(false);
  const maxValue = useRef(false);

  const timeInput = useRef({
    hour: '00',
    minute: '00',
    second: '00',
  });

  useEffect(() => {
    if (isCurrentChange.current) {
      // do nothing
      isCurrentChange.current = false;
    } else {
      // props value input changed
      const { formattedHours, formattedMinutes, formattedSeconds } = getHMS(value);
      timeInput.current = { hour: formattedHours, minute: formattedMinutes, second: formattedSeconds };
      forceUpdate();
    }
  }, [value]);

  useEffect(() => {
    minValue.current = props.min;
  }, [props.min]);

  useEffect(() => {
    maxValue.current = props.max;
  }, [props.max]);

  function handleChangeTime(currentNumber) {
    const timeUnit = itemSelected.current?.toLowerCase();

    const timeCurrent = cloneObj(timeInput.current);
    const timeWithPrev = cloneObj(timeInput.current);

    timeCurrent[timeUnit] = `0${currentNumber}`;
    timeWithPrev[timeUnit] = `${prevNumber.current}${currentNumber}`;

    if (handleCheckTimeValid(timeWithPrev)) {
      timeInput.current = timeWithPrev;
      isCurrentChange.current = true;
      prevNumber.current = null;
    } else if (handleCheckTimeValid(timeCurrent)) {
      timeInput.current = timeCurrent;
      isCurrentChange.current = true;
      prevNumber.current = currentNumber;
    } else {
      prevNumber.current = currentNumber;
    }

    forceUpdate();
  }

  function timeString(timeObj) {
    try {
      return `${timeObj.hour}:${timeObj.minute}:${timeObj.second}`;
    } catch (_) {
      return '';
    }
  }

  function handleCheckTimeValid(input) {
    const timeStr = timeString(input);
    const timeValid = timeIsValid(timeStr);
    const seconds = convertHHMMSSToSecond(timeStr);
    const minValid = !minValue.current || seconds >= minValue.current;
    const maxValid = !maxValue.current || seconds <= maxValue.current;

    return timeValid && minValid && maxValid;
  }

  useEffect(() => {
    if (isCurrentChange.current) {
      const valueOutput = +timeInput.current.hour * 60 * 60 + (+timeInput.current.minute) * 60 + (+timeInput.current.second);
      onChange(valueOutput);
    }
  }, [timeInput.current]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        itemSelected.current = undefined;
        prevNumber.current = null;
        forceUpdate();
      }
    }

    function handleCheckKeydown(event) {
      if (itemSelected.current && /^\d$/.test(event.key)) {
        handleChangeTime(event.key);
      }
    }

    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleCheckKeydown);

    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleCheckKeydown);
    };
  }, [wrapperRef]);

  function handleSelectItem(item = undefined) {
    if (disabled) return;
    itemSelected.current = item;
    prevNumber.current = null;
    forceUpdate();
  }

  return <>

    <div className={clsx('time-stepper', { 'time-stepper-disabled': disabled })}>
      <div className="time-stepper-content" ref={wrapperRef}>
        <div className="time-stepper-item">
          <span
            className={clsx('time-stepper-item__input', { 'time-stepper-item__selected': itemSelected.current === CONSTANT.HOUR })}
            onClick={() => handleSelectItem(CONSTANT.HOUR)}>{timeInput.current.hour}</span>
        </div>
        <div className="time-stepper-item">
          <span
            className={clsx('time-stepper-item__input', { 'time-stepper-item__selected': itemSelected.current === CONSTANT.MINUTE })}
            onClick={() => handleSelectItem(CONSTANT.MINUTE)}>{timeInput.current.minute}</span>
        </div>
        <div className="time-stepper-item">
          <span
            className={clsx('time-stepper-item__input', { 'time-stepper-item__selected': itemSelected.current === CONSTANT.SECOND })}
            onClick={() => handleSelectItem(CONSTANT.SECOND)}>{timeInput.current.second}</span>
        </div>
      </div>
    </div>
  </>;
}

TimeStepper.propTypes = {
  onChange: PropTypes.func,
  max: PropTypes.number,
  min: PropTypes.number,
};

export default TimeStepper;