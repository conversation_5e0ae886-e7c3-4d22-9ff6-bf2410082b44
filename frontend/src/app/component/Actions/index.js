import React from 'react';
import { Link } from 'react-router-dom';
import { Popconfirm, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { CopyOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

import AntButton from '../AntButton';

import { BUTTON } from '@constant';

import './Actions.scss';
import clsx from 'clsx';

const Actions = ({ center, prefix, suffix, ...props }) => {
  const { t } = useTranslation();

  const {
    linkToEdit, handleEdit,
    editText = t('EDIT'),
    editIcon, disabledEdit,
  } = props;

  const {
    handleDelete,
    deleteText = t('DELETE'),
    confirmText = t('CONFIRM_DELETE'),
    deleteIcon, disabledDelete,
  } = props;

  function renderBtnEdit() {
    if (!handleEdit && !linkToEdit) return;

    const editButton = (
      <Tooltip title={editText}>
        <AntButton
          type={BUTTON.LIGHT_NAVY}
          size="small"
          disabled={disabledEdit}
          onClick={handleEdit}
          icon={<EditOutlined/>}
        />
      </Tooltip>
    );

    if (linkToEdit) {
      return <Link to={linkToEdit}>{editButton}</Link>;
    }

    return editButton;
  }

  return (
    <div className={clsx('actions-button', { 'actions-button__center': center })}>
      {prefix}
      {props.handleCopy &&
        <Tooltip title={t('COPY')}>
          <AntButton
            type={BUTTON.LIGHT_GREEN}
            onClick={props.handleCopy}
            size="small"
            icon={<CopyOutlined/>}
          />
        </Tooltip>
      }

      {renderBtnEdit()}

      {handleDelete &&
        <Popconfirm
          placement="topRight"
          icon={null}
          title={confirmText}
          onConfirm={handleDelete}
          okText={t('DELETE')}
          cancelText={t('CANCEL')}
          cancelButtonProps={{ className: 'ant-btn-light-navy ant-btn-xsmall' }}
          okButtonProps={{ className: 'ant-btn-deep-red ant-btn-xsmall' }}
        >
          <Tooltip title={deleteText}>
            <AntButton
              type={BUTTON.LIGHT_RED}
              size="small"
              disabled={disabledDelete}
              icon={<DeleteOutlined/>}
            />
          </Tooltip>
        </Popconfirm>
      }

      {suffix}
    </div>
  );
};

export default Actions;
