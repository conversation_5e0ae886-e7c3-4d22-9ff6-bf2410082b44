import React from 'react';
import clsx from 'clsx';
import Avatar from 'antd/es/avatar/avatar';

import { generateClassNameByCharacter } from '@src/common/functionCommons';

import './AntAvatar.scss';

function Index({ name = '', shape = 'square', size = 'large' }) {
  const character = name[0] || '';
  const className = generateClassNameByCharacter(character);
  return (
    <Avatar
      className={clsx('custom-avatar', className)}
      style={{
        backgroundSize: 'cover',
        border: 0,
      }}
      shape={shape}
      size={size}
    >
      {character.toUpperCase()}
    </Avatar>
  );
}

export default Index;