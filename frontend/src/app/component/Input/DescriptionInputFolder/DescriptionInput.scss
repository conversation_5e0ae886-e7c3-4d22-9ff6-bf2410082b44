.description-input-folder {
  display: flex;

  max-width: 100%;

  .description-input-new {
    cursor: pointer;
    display: flex;
    gap: 8px;

    &:active {
      color: var(--typo-colours-support-blue);

      svg path {
        stroke: var(--typo-colours-support-blue) !important;
      }
    }

    .description-input__icon {
      display: flex;
      align-self: center;

      svg path {
        stroke: #000000;
      }
    }

    .description-input__value {
      text-align: left;
    }
  }

  .description-input__value {
    display: flex;
    align-items: center;
    gap: 8px;

    &.description-input__value-editing {
      gap: 7px;
    }

    .description-input__icon-edit {

      width: 16px;
      height: 16px;
      cursor: pointer;

      &:active {
        svg path {
          stroke: var(--typo-colours-support-blue);
        }
      }
    }
  }

}