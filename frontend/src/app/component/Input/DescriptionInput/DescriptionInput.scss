.description-input {
  display: flex;

  max-width: 100%;

  .description-input-new {
    cursor: pointer;
    display: flex;
    gap: 8px;

    .description-input__icon {
      display: flex;
      align-self: center;

      svg path {
        stroke: #000000;
      }
    }

    .description-input__value {

      text-align: justify;
      line-height: 20px;
    }
  }

  .description-edit {
    display: flex;
    gap: 8px;
    max-width: 100%;

    .description-edit__input {
      height: 20px;
      display: flex;
      max-width: calc(100% - 28px);

      input {
        font-size: inherit;
        padding: 0;
        border-radius: 0;
        transition-duration: 0s;
      }
    }

    .description-edit__icon-submit {
      width: 20px;
      height: 20px;
      cursor: pointer;
      display: flex;
      justify-content: center;

      .description-edit__icon-edit, .description-edit__icon-check {
        width: 20px;
        height: 20px;
      }

      .description-edit__icon-loading {
        font-size: 16px;
        //width: 16px;
        //height: 16px;
      }
    }
  }

  .description-input__value {
    display: flex;
    gap: 8px;

    &.description-input__value-editing {
      gap: 7px;
    }

    .description-input__text {
      line-height: 20px;
    }

    .description-input__action {

      width: 20px;
      height: 20px;
      cursor: pointer;

      svg {
        width: 20px;
        height: 20px;

        path {
          fill: transparent;
          stroke: #858585;
        }
      }

    }
  }

}