import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';

import AntButton from '@component/AntButton';

import { BUTTON, CONSTANT } from '@constant';

import Upload40 from '@component/SvgIcons/Upload/Upload40';

import './Dropzone.scss';
import UploadError from '@component/UploadError';

const DROPZONE_TYPE = {
  [CONSTANT.VIDEO]: {
    accept: {
      'video/mp4': ['.mp4'],
      'video/webm': ['.webm'],
      'video/x-matroska': ['.mkv'],
    },
    file: ['.mp4', 'mkv', 'webm'],
  },
  [CONSTANT.AUDIO]: {
    accept: {
      'audio/mpeg': ['.mp3'],
      'audio/wav': ['.wav'],
      'audio/ogg': ['.ogg'],
      'audio/mp4': ['.m4a'],
    },
    file: ['.mp3', '.wav', '.ogg', '.m4a'],
  },
  [CONSTANT.DOCUMENT]: {
    accept: {
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx', '.doc'],
    },
    file: ['docx', 'doc'],
  },
};

function Dropzone({ type, maxSizeMB, hideDesc = false, ...props }) {
  const { t } = useTranslation();

  const [errorContent, setErrorContent] = useState('');

  const handlePaste = useCallback(async (event) => {
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    handleDrop(items);
  }, []);

  function handleDrop(files) {
    let isValid = true;

    if (!files.length) {
      isValid = false;
      setErrorContent(t('PLEASE_SELECT_A_FILE_CORRECT_FORMAT'));
    }
    for (let i = 0; i < files.length; i++) {
      let file = files[i];
      const maxSize = maxSizeMB * 1024 * 1024;
      if (file.size >= maxSize) {
        isValid = false;
        setErrorContent(t('THE_AUDIO_FILE_EXCEEDS_THE_ALLOWED_CAPACITY'));
        break;
      }
    }
    if (isValid && files.length) {
      if (errorContent) setErrorContent('');
      props.onDrop(files);
    }
  }

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleDrop,
    noClick: true,
    accept: DROPZONE_TYPE[type]?.accept,
    multiple: false,
    //disabled,
  });

  return <div className="dropzone-container">

    {errorContent && <UploadError
      content={errorContent}
      onCancel={() => setErrorContent('')}
      onTryAgain={open}
    />}

    {!errorContent && <div {...getRootProps()} onPaste={handlePaste}>
      <input {...getInputProps()} />

      <div className="dropzone-content">
        <div className="dropzone-inner">
          <div className="">
            <Upload40/>
          </div>
          <div className="dropzone__title">
            {t('DROP_YOUR_FILE_HERE')}
          </div>
          <div className="dropzone__description">
            {t('WE_CAN_WORK_WITH')} {DROPZONE_TYPE[type]?.file?.join(', ')}
          </div>
          {maxSizeMB && <div className="dropzone__description">
            {`${t('MAXIMUM_FILE_SIZE')}: ${maxSizeMB}MB`}
          </div>}
          {!hideDesc && <div className="upload-image__description">
            {t(`DROPZONE_${type}_DESCRIPTION`)}
          </div>}

          <div>
            <AntButton
              size="large"
              type={BUTTON.DEEP_NAVY}
              onClick={open}
            >
              {t(`SELECT_${type}`)}
            </AntButton>
          </div>
        </div>
      </div>
    </div>}
  </div>;
}

export default Dropzone;