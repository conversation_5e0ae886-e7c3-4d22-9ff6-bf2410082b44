.select-language {
  display: flex;
  gap: 14px;
  width: 108px;

  .language__flag {
    width: 24px;
    height: 24px;
  }

  .language__text {
    color: var(--primary-colours-blue-navy);
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    width: 30px;
  }

  .language__dropdown-btn {
    margin-left: 2px;
  }

  .ant-dropdown {
    min-width: unset !important;

    .ant-dropdown-menu.ant-dropdown-menu-vertical {
      padding: 0;
      gap: 0;
      margin-top: 10px;

      .ant-dropdown-menu-item {
        padding: 12px 16px;

        .ant-dropdown-menu-title-content {
          display: flex;
          gap: 14px;

          .output-language-item__flag {
            height: 24px;
            width: 24px;
            object-fit: cover;
          }

          .output-language-item__lang {
            line-height: 24px;
            font-weight: 500;
            width: 91px;
          }
        }

        &:hover {
          background-color: #E7E5FF;
        }

        &.ant-dropdown-menu-item-selected,
        &.ant-dropdown-menu-item:active {
          background: var(--navy);

          .ant-dropdown-menu-title-content .output-language-item__lang {
            font-weight: 600;
          }
        }

      }
    }
  }
}