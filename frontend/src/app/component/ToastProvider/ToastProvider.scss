.ant-notification .ant-notification-notice-wrapper .ant-notification-notice {
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.05);
  padding-right: 34px;

  .ant-notification-notice-with-icon {
    .ant-notification-notice-message {
      font-weight: 700;
      margin-inline-start: 24px;
    }

    .ant-notification-notice-description {
      margin-inline-start: 0;
    }
  }

  .ant-notification-notice-close {
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;

    .ant-notification-notice-close-x {
      display: flex;
      align-items: center;

      svg {
        width: 12px;
        height: 12px;
        fill: #000;
      }
    }
  }

  &.notification-success .ant-notification-notice-message {
    color: #0db4aa;
  }

  &.notification-error .ant-notification-notice-message {
    color: #f50606;
  }

  &.notification-warning .ant-notification-notice-message {
    color: #ed9209;
  }
}
