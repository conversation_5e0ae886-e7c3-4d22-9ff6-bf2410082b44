.admin-table {
  .ant-table {
    border-radius: 8px;

    .ant-table-thead {
      .ant-table-cell {
        &::before {
          display: none;
        }

        &:first-child {
          border-top-left-radius: 8px !important;
        }

        &:last-child {
          border-top-right-radius: 8px !important;
        }
      }
    }
  }

  .ant-table-thead {
    font-weight: 600;
    line-height: 20px;
  }

  .ant-table-row {
    > .ant-table-cell {
      line-height: 20px;
    }
  }

  .ant-table-pagination {
    align-items: center;

    .ant-pagination-prev,
    .ant-pagination-next {
      height: fit-content;

      &:not(.ant-pagination-disabled) {
        &:hover {
          .ant-pagination-item-link {
            background-color: var(--primary-colours-blue-light-2) !important;
          }
        }
      }
    }

    .ant-pagination-item-link {
      background-color: var(--background-light-background-2) !important;
      border: var(--background-light-background-grey) !important;
      border-radius: 8px !important;
      color: var(--typo-colours-support-blue-light) !important;
      width: 40px !important;
      height: 40px !important;
    }

    .ant-pagination-item {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      background-color: var(--background-light-background-2) !important;
      border: var(--background-light-background-grey) !important;
      border-radius: 8px !important;
      width: 40px !important;
      height: 40px !important;

      a {
        color: var(--typo-colours-support-blue-light) !important;
      }

      &:hover {
        background-color: var(--primary-colours-blue-light-2) !important;
      }

      &.ant-pagination-item-active {
        background-color: var(--primary-colours-blue) !important;

        a {
          color: var(--background-light-background-2) !important;
        }
      }
    }

    .ant-pagination-total-text {
      display: flex;
      vertical-align: middle;
      margin-inline-end: 16px;
    }

    .ant-pagination-options-size-changer {
      height: 40px;

      .ant-select-selector {
        border-radius: 8px;
      }
    }
  }
}