import React from 'react';
import Avatar from 'antd/es/avatar/avatar';
import { convertFileName } from '@src/common/functionCommons';
import './AvatarName.scss';

AvatarName.propTypes = {};
AvatarName.defaultProps = {
  type: 'square',
  name: ' ',
  size: 'large'
};

function AvatarName({ name, type, size }) {
  let backgroundImageAvatar = null;
  const avatarCharacter = convertFileName(name[0].toUpperCase());
  if (avatarCharacter >= 'A' && avatarCharacter <= 'G') backgroundImageAvatar = 'linear-gradient(180deg, #6EBCFB 0%, #2196F3 100%)';
  else if (avatarCharacter >= 'H' && avatarCharacter <= 'N') backgroundImageAvatar = 'linear-gradient(180deg, #83FCC2 0%, #3FDB90 100%)';
  else if (avatarCharacter >= 'O' && avatarCharacter <= 'U') backgroundImageAvatar = 'linear-gradient(180deg, #E76EFB 0%, #9B6EFB 100%)';
  else 'linear-gradient(180deg, #FFD89E 0%, #FD9F12 100%)';
  return (
    <div className="custom-avatar-container">
      <Avatar
        style={{
          backgroundImage: backgroundImageAvatar,
          backgroundSize: 'cover',
          border: 0,
        }}
        shape={type}
        size={size}
      >
        {name[0]}
      </Avatar>
    </div>
  );
}

export default AvatarName;