import { useState } from 'react';
import { connect } from 'react-redux';
import clsx from 'clsx';

import AntButton from '@component/AntButton';

import { API } from '@api';
import { BUTTON, OWLLEE_CONFIG } from '@constant';

import OwlleeCloseIcon from '@src/asset/icon/OwlleeIcon/CloseIcon.svg';

import './OwleeChat.scss';

function OwleeChat(props) {
  const { user, chatBotId, icon, className } = props;

  const [isShow, setShow] = useState(false);

  const toggleOwlee = () => {
    setShow(prevState => !prevState);
  };

  return (
    <div className={clsx('owllee-chat', { [className]: !!className })}>
      <AntButton
        className="toggle-owllee"
        type={BUTTON.DEEP_NAVY}
        icon={<img
          src={isShow ? OwlleeCloseIcon : icon || OWLLEE_CONFIG.avatarDefault}
          className={clsx('toggle-owllee__icon', { 'toggle-owllee__icon-close': isShow })}
          alt=""
        />}
        onClick={toggleOwlee}
      />

      <iframe
        className={clsx('owllee-chat-iframe', { 'owllee-chat-iframe__hide': !isShow })}
        src={API.OWLEE_URL.format(OWLLEE_CONFIG.domainChat, chatBotId)}
      />
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(OwleeChat);
