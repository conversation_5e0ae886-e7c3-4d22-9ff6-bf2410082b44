.movie-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5);
    border-color: #1890ff;

    .movie-overlay {
      opacity: 1;
    }
  }

  .movie-poster {
    position: relative;
    height: 360px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }

    .movie-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .overlay-content {
        text-align: center;

        .ant-btn {
          border-radius: 20px;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
          }
        }

        .overlay-info {
          margin-top: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .rating {
            color: #fadb14;
            font-weight: bold;
          }
        }
      }
    }

    .movie-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .quality-badge {
        background: rgba(255, 0, 0, 0.9);
        border: none;
        font-weight: bold;
      }

      .favorite-btn {
        background: rgba(0, 0, 0, 0.6);
        border: none;
        color: #fff;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(255, 0, 0, 0.8);
          color: #fff;
        }

        .anticon-heart-filled {
          color: #ff4d4f;
        }
      }
    }
  }

  .ant-card-body {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;

    .movie-title {
      .ant-typography {
        color: #fff !important;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .movie-meta {
      .meta-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;

        .anticon {
          font-size: 12px;
        }
      }

      .genre-list {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;

        .ant-tag {
          margin: 0;
          border-radius: 12px;
          background: rgba(24, 144, 255, 0.2);
          border: 1px solid rgba(24, 144, 255, 0.3);
          color: #1890ff;
        }
      }
    }
  }
}