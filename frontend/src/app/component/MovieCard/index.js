import { Button, Card, Col, Rate, Tag } from 'antd';
import { CalendarOutlined, EyeOutlined, HeartFilled, HeartOutlined, PlayCircleOutlined } from '@ant-design/icons';
import React from 'react';
import Meta from 'antd/es/card/Meta';
import Text from '@component/SvgIcons/Text';
import { useNavigate } from 'react-router-dom';
import { toast } from '@component/ToastProvider';

const MovieCard = ({ movie }) => {
  const navigate = useNavigate();

  const handleWatchNow = (e, movie) => {
    e.stopPropagation();
    navigate(`/movie/${movie.id}/watch`, { state: { movie } });
  };


  const toggleFavorite = (e, movieId) => {
    e.stopPropagation();
    const newFavorites = new Set(favoriteMovies);
    if (newFavorites.has(movieId)) {
      newFavorites.delete(movieId);
      toast.success('Đã xóa khỏi danh sách yêu thích')
    } else {
      newFavorites.add(movieId);
      toast.success('Đã thêm vào danh sách yêu thích')
    }
    setFavoriteMovies(newFavorites);
  };


  return <Col key={movie.id} xs={12} sm={8} md={6} lg={4} xl={4}>
    <Card hoverable className="movie-card" cover={
      <div className="movie-poster">
        <img alt={movie.title} src={movie.poster}/>
        <div className="movie-overlay">
          <div className="overlay-content">
            <Button
              type="primary"
              icon={<PlayCircleOutlined/>}
              size="large"
              onClick={(e) => handleWatchNow(e, movie)}
            >
              Xem ngay
            </Button>
            <div className="overlay-info">
              <Rate disabled defaultValue={movie.rating / 2} size="small"/>
              <Text className="rating">{movie.rating}/10</Text>
            </div>
          </div>
        </div>
        <div className="movie-badges">
          <Tag color="red" className="quality-badge">
            {movie.quality}
          </Tag>
          <Button
            type="text"
            icon={favoriteMovies.has(movie.id) ? <HeartFilled/> : <HeartOutlined/>}
            className="favorite-btn"
            onClick={(e) => toggleFavorite(e, movie.id)}
          />
        </div>
      </div>
    }
          onClick={() => handleMovieClick(movie)}
    >
      <Meta
        title={
          <div className="movie-title">
            <Text strong ellipsis={{ tooltip: movie.title }}>
              {movie.title}
            </Text>
          </div>
        }
        description={
          <div className="movie-meta">
            <div className="meta-row">
              <CalendarOutlined/>
              <span>{movie.year}</span>
              <EyeOutlined/>
              <span>{movie.views}</span>
            </div>
            <div className="genre-list">
              {movie.genre.slice(0, 2).map(g => (
                <Tag key={g} size="small">{g}</Tag>
              ))}
              {movie.genre.length > 2 && (
                <Tag size="small">+{movie.genre.length - 2}</Tag>
              )}
            </div>
          </div>
        }
      />
    </Card>
  </Col>;

};

export default MovieCard;