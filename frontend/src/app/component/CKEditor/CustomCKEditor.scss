.ckeditor-container {
  position: relative;

  &:hover {
    .ck-editor {
      .ck.ck-sticky-panel .ck-sticky-panel__content,
      .ck.ck-editor__main .ck-editor__editable {
        border-color: var(--primary-colours-blue-navy);
      }
    }
  }

  .ck-editor {
    border-radius: 4px;
    transition: box-shadow-color var(--transition-timing);

    .ck.ck-sticky-panel .ck-sticky-panel__content {
      border-radius: 4px 4px 0 0;
      border-color: var(--lighttheme-content-background-stroke);
      transition: border-color var(--transition-timing);

      .ck-toolbar {
        border-radius: 4px 4px 0;
      }
    }

    .ck.ck-editor__main .ck-editor__editable {
      border-radius: 0 0 4px 4px;
      border-color: var(--lighttheme-content-background-stroke);
      transition: border-color var(--transition-timing);
    }

    &:focus-within {
      box-shadow: 0 0 0 2px rgba(3, 15, 45, 0.4);
      outline: 0;

      .ck.ck-sticky-panel .ck-sticky-panel__content,
      .ck.ck-editor__main .ck-editor__editable {
        border-color: var(--primary-colours-blue-navy);
      }
    }
  }


  .ckeditor__count {
    position: absolute;
    right: 0;

    bottom: -28px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.ant-form-item-has-error {
  .ckeditor-container {
    .ck-editor {
      .ck.ck-sticky-panel .ck-sticky-panel__content,
      .ck.ck-editor__main .ck-editor__editable {
        border-color: #ff4d4f;
      }

      &:focus-within {
        box-shadow: 0 0 0 2px rgba(255, 38, 5, 0.06) !important;
        outline: 0;

        .ck.ck-sticky-panel .ck-sticky-panel__content,
        .ck.ck-editor__main .ck-editor__editable {
          border-color: #ff4d4f;
        }
      }
    }

    .ckeditor__count {
      color: #ff4d4f;
    }
  }
}