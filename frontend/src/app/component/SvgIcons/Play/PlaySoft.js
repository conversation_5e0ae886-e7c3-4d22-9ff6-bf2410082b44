import React from 'react';
import './Play.scss';

export default function PlaySoft() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd"
            d="M8.00033 1.66797C11.4976 1.66797 14.3337 4.5034 14.3337 8.0013C14.3337 11.4992 11.4976 14.3346 8.00033 14.3346C4.50243 14.3346 1.66699 11.4992 1.66699 8.0013C1.66699 4.5034 4.50243 1.66797 8.00033 1.66797Z"
            stroke="#2196F3" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path fillRule="evenodd" clipRule="evenodd"
            d="M10.0003 7.99676C10.0003 7.45601 7.22868 5.72608 6.91427 6.03713C6.59986 6.34818 6.56962 9.61603 6.91427 9.9564C7.25891 10.298 10.0003 8.53752 10.0003 7.99676Z"
            stroke="#2196F3" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>

  );
}
