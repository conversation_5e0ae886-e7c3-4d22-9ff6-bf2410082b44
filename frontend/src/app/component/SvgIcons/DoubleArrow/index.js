import React from 'react';
import PropTypes from 'prop-types';

const DoubleArrow = ({ color = '#09196B', width = 20, height = 20, className = '' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M13.3333 2.5L19.1667 9.16667L13.3333 15.8333M0.833344 2.5L6.66668 9.16667L0.833344 15.8333"
        stroke={color}
        strokeWidth="1.67"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

DoubleArrow.propTypes = {
  color: PropTypes.string,
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
};

export default DoubleArrow;
