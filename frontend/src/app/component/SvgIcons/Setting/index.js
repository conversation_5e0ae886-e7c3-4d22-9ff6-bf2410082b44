import React from 'react';

import './Setting.scss';

export default function Setting() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
      <g clipPath="url(#clip0_1811_52760)">
        <path
          d="M7.99984 10.4998C9.10441 10.4998 9.99984 9.60441 9.99984 8.49984C9.99984 7.39527 9.10441 6.49984 7.99984 6.49984C6.89527 6.49984 5.99984 7.39527 5.99984 8.49984C5.99984 9.60441 6.89527 10.4998 7.99984 10.4998Z"
          stroke="#858585" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path
          d="M12.9332 10.4998C12.8444 10.7009 12.818 10.924 12.8572 11.1402C12.8964 11.3565 12.9995 11.5561 13.1532 11.7132L13.1932 11.7532C13.3171 11.877 13.4155 12.0241 13.4826 12.1859C13.5497 12.3478 13.5842 12.5213 13.5842 12.6965C13.5842 12.8717 13.5497 13.0452 13.4826 13.2071C13.4155 13.369 13.3171 13.516 13.1932 13.6398C13.0693 13.7638 12.9223 13.8622 12.7604 13.9293C12.5986 13.9963 12.4251 14.0309 12.2498 14.0309C12.0746 14.0309 11.9011 13.9963 11.7393 13.9293C11.5774 13.8622 11.4303 13.7638 11.3065 13.6398L11.2665 13.5998C11.1094 13.4461 10.9098 13.343 10.6936 13.3038C10.4773 13.2646 10.2542 13.2911 10.0532 13.3798C9.85599 13.4643 9.68783 13.6047 9.56938 13.7835C9.45093 13.9624 9.38736 14.172 9.3865 14.3865V14.4998C9.3865 14.8535 9.24603 15.1926 8.99598 15.4426C8.74593 15.6927 8.40679 15.8332 8.05317 15.8332C7.69955 15.8332 7.36041 15.6927 7.11036 15.4426C6.86031 15.1926 6.71984 14.8535 6.71984 14.4998V14.4398C6.71468 14.2192 6.64325 14.0052 6.51484 13.8256C6.38644 13.6461 6.20699 13.5094 5.99984 13.4332C5.79876 13.3444 5.57571 13.318 5.35945 13.3572C5.14318 13.3964 4.94362 13.4995 4.7865 13.6532L4.7465 13.6932C4.62267 13.8171 4.47562 13.9155 4.31376 13.9826C4.15189 14.0497 3.97839 14.0842 3.80317 14.0842C3.62795 14.0842 3.45445 14.0497 3.29258 13.9826C3.13072 13.9155 2.98367 13.8171 2.85984 13.6932C2.73587 13.5693 2.63752 13.4223 2.57042 13.2604C2.50333 13.0986 2.46879 12.9251 2.46879 12.7498C2.46879 12.5746 2.50333 12.4011 2.57042 12.2393C2.63752 12.0774 2.73587 11.9303 2.85984 11.8065L2.89984 11.7665C3.05353 11.6094 3.15663 11.4098 3.19584 11.1936C3.23505 10.9773 3.20858 10.7542 3.11984 10.5532C3.03533 10.356 2.89501 10.1878 2.71615 10.0694C2.53729 9.95093 2.3277 9.88736 2.11317 9.8865H1.99984C1.64622 9.8865 1.30708 9.74603 1.05703 9.49598C0.80698 9.24593 0.666504 8.90679 0.666504 8.55317C0.666504 8.19955 0.80698 7.86041 1.05703 7.61036C1.30708 7.36031 1.64622 7.21984 1.99984 7.21984H2.05984C2.2805 7.21468 2.49451 7.14325 2.67404 7.01484C2.85357 6.88644 2.99031 6.70699 3.0665 6.49984C3.15525 6.29876 3.18172 6.07571 3.14251 5.85945C3.10329 5.64318 3.0002 5.44362 2.8465 5.2865L2.8065 5.2465C2.68254 5.12267 2.58419 4.97562 2.51709 4.81376C2.44999 4.65189 2.41546 4.47839 2.41546 4.30317C2.41546 4.12795 2.44999 3.95445 2.51709 3.79258C2.58419 3.63072 2.68254 3.48367 2.8065 3.35984C2.93033 3.23587 3.07739 3.13752 3.23925 3.07042C3.40111 3.00333 3.57462 2.96879 3.74984 2.96879C3.92506 2.96879 4.09856 3.00333 4.26042 3.07042C4.42229 3.13752 4.56934 3.23587 4.69317 3.35984L4.73317 3.39984C4.89029 3.55353 5.08985 3.65663 5.30611 3.69584C5.52237 3.73505 5.74543 3.70858 5.9465 3.61984H5.99984C6.19702 3.53533 6.36518 3.39501 6.48363 3.21615C6.60208 3.03729 6.66565 2.8277 6.6665 2.61317V2.49984C6.6665 2.14622 6.80698 1.80708 7.05703 1.55703C7.30708 1.30698 7.64622 1.1665 7.99984 1.1665C8.35346 1.1665 8.6926 1.30698 8.94265 1.55703C9.19269 1.80708 9.33317 2.14622 9.33317 2.49984V2.55984C9.33403 2.77436 9.39759 2.98395 9.51604 3.16281C9.63449 3.34167 9.80266 3.482 9.99984 3.5665C10.2009 3.65525 10.424 3.68172 10.6402 3.64251C10.8565 3.60329 11.0561 3.5002 11.2132 3.3465L11.2532 3.3065C11.377 3.18254 11.5241 3.08419 11.6859 3.01709C11.8478 2.94999 12.0213 2.91546 12.1965 2.91546C12.3717 2.91546 12.5452 2.94999 12.7071 3.01709C12.869 3.08419 13.016 3.18254 13.1398 3.3065C13.2638 3.43033 13.3622 3.57739 13.4293 3.73925C13.4963 3.90111 13.5309 4.07462 13.5309 4.24984C13.5309 4.42506 13.4963 4.59856 13.4293 4.76042C13.3622 4.92229 13.2638 5.06934 13.1398 5.19317L13.0998 5.23317C12.9461 5.39029 12.843 5.58985 12.8038 5.80611C12.7646 6.02237 12.7911 6.24543 12.8798 6.4465V6.49984C12.9643 6.69702 13.1047 6.86518 13.2835 6.98363C13.4624 7.10208 13.672 7.16565 13.8865 7.1665H13.9998C14.3535 7.1665 14.6926 7.30698 14.9426 7.55703C15.1927 7.80708 15.3332 8.14622 15.3332 8.49984C15.3332 8.85346 15.1927 9.1926 14.9426 9.44265C14.6926 9.69269 14.3535 9.83317 13.9998 9.83317H13.9398C13.7253 9.83403 13.5157 9.89759 13.3369 10.016C13.158 10.1345 13.0177 10.3027 12.9332 10.4998Z"
          stroke="#858585" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </g>
      <defs>
        <clipPath id="clip0_1811_52760">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
        </clipPath>
      </defs>
    </svg>
  );
}
