import React from 'react';
import './SavePdf.scss';

export default function SavePdf() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
      <g clipPath="url(#clip0_2449_20659)">
        <mask id="mask0_2449_20659" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="17">
          <path d="M16 0.5H0V16.5H16V0.5Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_2449_20659)">
          <path
            d="M8.66602 1.83398H3.99935C3.26602 1.83398 2.66602 2.43398 2.66602 3.16732V13.834C2.66602 14.5673 3.26602 15.1673 3.99935 15.1673H11.9993C12.7327 15.1673 13.3327 14.5673 13.3327 13.834V6.50065L8.66602 1.83398Z"
            stroke="#8786DD" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8.66602 1.83398V6.50065H13.3327" stroke="#8786DD" strokeWidth="1.5" strokeLinecap="round"
                strokeLinejoin="round"/>
          <path d="M6.19922 10.834L7.99922 12.7007L9.79922 10.834" stroke="#8786DD" strokeWidth="1.5"
                strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8 12.7008V8.30078" stroke="#8786DD" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_2449_20659">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
        </clipPath>
      </defs>
    </svg>
  );
}
