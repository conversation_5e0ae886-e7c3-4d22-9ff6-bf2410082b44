import { AntForm } from '@component/AntForm';
import { Input, InputNumber, Select } from 'antd';
import { cloneObj, stringSplit } from '@common/functionCommons';
import React from 'react';
import { useTranslation } from 'react-i18next';

function DynamicForm({
  formFields = [],
}) {

  const { t, i18n } = useTranslation();

  return <>
    {formFields?.map(field => {
      const extraOptions = cloneObj(field.selectOptions) || [];
      extraOptions.forEach(option => {
        if (typeof option.label === 'object') {
          if (option.label?.en || option.label?.vi) {
            option.label = option.label?.[i18n.language] || option.label?.en || option.label?.vi;
          }
        }
      });

      const { placeholder } = field || {};
      let rules = [];
      switch (field.rule) {
        case 'required':
          rules = [{ required: true, message: `${field.name} is required` }];
          break;
        default:
          break;
      }

      if (field.type.toLowerCase() === 'tag') {
        return <AntForm.TagItem
          key={field._id}
          name={field.code} label={field.name} rules={rules}
          placeholder={placeholder}
        />;
      }

      const itemProps = { placeholder };
      if (field.width) {
        itemProps.style ||= {};
        itemProps.style.width = `${field.width}px`;

      }
      return <AntForm.Item
        key={field._id}
        name={field.code}
        label={field.localization?.name?.[i18n.language] || field.name}
        rules={rules}
        initialValue={field.defaultValue}
      >
        {field.type.toLowerCase() === 'select' && <Select options={extraOptions} {...itemProps} />}
        {field.type.toLowerCase() === 'select_multiple' &&
          <Select mode="multiple" options={extraOptions} {...itemProps} />}
        {field.type.toLowerCase() === 'text' && <Input {...itemProps} />}
        {field.type.toLowerCase() === 'textarea' && <Input.TextArea
          count={{
            show: true, max: 1000,
            strategy: (txt) => stringSplit(txt).length,
          }}
          autoSize={{ minRows: 1 }}
          {...itemProps}
        />}
        {field.type.toLowerCase() === 'number' && <InputNumber placeholder={placeholder}/>}
      </AntForm.Item>;
    })}
  </>;
}

export default DynamicForm;