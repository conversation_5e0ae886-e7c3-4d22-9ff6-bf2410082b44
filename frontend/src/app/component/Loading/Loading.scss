.loading {
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 30px 50px;
  margin: 20px 0;
}

.loading-component {
  position: relative;

  .loading-backdrop {
    z-index: 100;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, .5);

    .loading-spin {
      max-height: 80px;
      height: 100%;
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .fixed-middle {
      max-height: 80px;
      position: sticky;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &.loading-backdrop-transparent {
      background-color: transparent;
    }
  }
}
