import React, { createRef, forwardRef, useImperativeHandle } from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';

import './ConfirmProvider.scss';
import { BUTTON } from '@constant';

export const confirmRef = createRef();
export const confirm = {
  delete: (args) => confirmRef.current?._delete(args),
};

export const ConfirmProvider = forwardRef(function ConfirmProvider(props, ref) {
  const { t } = useTranslation();

  const { children } = props;

  const [modal, contextHolder] = Modal.useModal({
    width: '400px',
  });

  useImperativeHandle(ref, () => ({
    _delete: renderConfirm,
  }));

  function renderConfirm({ title, content, okText, cancelText, ...props }) {
    const isExistIcon = props.hasOwnProperty('icon');

    modal.confirm({
      className: clsx('confirm-modal', { 'confirm-modal-has-icon': isExistIcon }),
      width: 400,
      title,
      content,
      icon: isExistIcon ? props.icon : null,
      okText: okText || t('DELETE'),
      cancelText: cancelText || t('CANCEL'),
      onOk: props.handleConfirm,
      okButtonProps: { size: 'large', type: BUTTON.DEEP_NAVY },
      cancelButtonProps: { size: 'large', type: BUTTON.WHITE },
    });
  }

  return (
    <>
      {contextHolder}
      {children}
    </>
  );
});
