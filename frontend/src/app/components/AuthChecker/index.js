import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { message } from 'antd';

const AuthChecker = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const user = useSelector(state => state.auth.user);

  useEffect(() => {
    // <PERSON><PERSON><PERSON> tra redirect sau khi đăng nhập
    const urlParams = new URLSearchParams(location.search);
    const redirectUrl = urlParams.get('redirect');
    
    if (redirectUrl && user && user !== 'INITIAL') {
      // Đã đăng nhập và có redirect URL
      navigate(decodeURIComponent(redirectUrl), { replace: true });
      message.success('Đăng nhập thành công!');
    }
  }, [user, location.search, navigate]);

  return children;
};

export default AuthChecker;
