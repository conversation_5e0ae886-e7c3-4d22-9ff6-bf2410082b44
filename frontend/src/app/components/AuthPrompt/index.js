import React from 'react';
import { Modal, Button, Typography, Space } from 'antd';
import { LoginOutlined, UserAddOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Title, Text } = Typography;

const AuthPrompt = ({ 
  visible, 
  onCancel, 
  title = 'Yêu cầu đăng nhập',
  content = 'Bạn cần đăng nhập để sử dụng tính năng này.',
  loginText = 'Đăng nhập',
  registerText = 'Đăng ký'
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogin = () => {
    const redirectUrl = location.pathname + location.search;
    navigate(`/auth/login?redirect=${encodeURIComponent(redirectUrl)}`);
    onCancel();
  };

  const handleRegister = () => {
    const redirectUrl = location.pathname + location.search;
    navigate(`/auth/register?redirect=${encodeURIComponent(redirectUrl)}`);
    onCancel();
  };

  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      footer={null}
      centered
      width={400}
      className="auth-prompt-modal"
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <Title level={4}>{title}</Title>
        <Text type="secondary" style={{ marginBottom: 24, display: 'block' }}>
          {content}
        </Text>
        
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Button 
            type="primary" 
            size="large" 
            block
            icon={<LoginOutlined />}
            onClick={handleLogin}
          >
            {loginText}
          </Button>
          
          <Button 
            size="large" 
            block
            icon={<UserAddOutlined />}
            onClick={handleRegister}
          >
            {registerText}
          </Button>
        </Space>
        
        <Text type="secondary" style={{ fontSize: '12px', marginTop: 16, display: 'block' }}>
          Chưa có tài khoản? Đăng ký ngay để trải nghiệm đầy đủ tính năng!
        </Text>
      </div>
    </Modal>
  );
};

export default AuthPrompt;
