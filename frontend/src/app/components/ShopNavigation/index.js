import React from 'react';
import { <PERSON>u, <PERSON>ge, Space, Button, Dropdown, Avatar } from 'antd';
import { 
  ShoppingCartOutlined, 
  HeartOutlined, 
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@app/hooks/useAuth';
import { LINK } from '@link';

const ShopNavigation = ({ cartItemCount = 0, wishlistCount = 0 }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();

  const handleMenuClick = (key) => {
    navigate(key);
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân',
      onClick: () => navigate(LINK.SHOP.PROFILE)
    },
    {
      key: 'orders',
      icon: <SettingOutlined />,
      label: 'Đơn hàng của tôi',
      onClick: () => navigate(LINK.SHOP.ORDERS)
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: () => navigate('/logout')
    }
  ];

  const mainMenuItems = [
    {
      key: LINK.SHOP.HOME,
      label: 'Trang chủ'
    },
    {
      key: LINK.SHOP.PRODUCTS,
      label: 'Sản phẩm'
    }
  ];

  return (
    <div className="shop-navigation">
      <Menu 
        mode="horizontal" 
        selectedKeys={[location.pathname]}
        items={mainMenuItems}
        onClick={({ key }) => handleMenuClick(key)}
        style={{ flex: 1, border: 'none' }}
      />
      
      <Space size="middle">
        {/* Wishlist */}
        <Button 
          type="text" 
          icon={
            <Badge count={wishlistCount} size="small">
              <HeartOutlined style={{ fontSize: '20px' }} />
            </Badge>
          }
          onClick={() => navigate(LINK.SHOP.WISHLIST)}
        />
        
        {/* Cart */}
        <Button 
          type="text" 
          icon={
            <Badge count={cartItemCount} size="small">
              <ShoppingCartOutlined style={{ fontSize: '20px' }} />
            </Badge>
          }
          onClick={() => navigate(LINK.SHOP.CART)}
        />
        
        {/* User Menu */}
        {isAuthenticated ? (
          <Dropdown 
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <Button type="text">
              <Space>
                <Avatar size="small" icon={<UserOutlined />} />
                {user?.name || 'User'}
              </Space>
            </Button>
          </Dropdown>
        ) : (
          <Button 
            type="primary" 
            icon={<LoginOutlined />}
            onClick={() => {
              const redirectUrl = location.pathname + location.search;
              navigate(`/auth?redirect=${encodeURIComponent(redirectUrl)}`);
            }}
          >
            Đăng nhập
          </Button>
        )}
      </Space>
    </div>
  );
};

export default ShopNavigation;
