import React, { useState, useEffect } from 'react';
import './CheckoutPage.scss';
import { Layout, Typography, Card, Row, Col, Form, Input, Select, Radio, Button, Steps, Divider, Space, message } from 'antd';
import { CreditCardOutlined, BankOutlined, WalletOutlined, EnvironmentOutlined, UserOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const CheckoutPage = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [orderItems, setOrderItems] = useState([]);
  const [paymentMethod, setPaymentMethod] = useState('cod');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mock order items - sẽ lấy từ cart hoặc API
    setOrderItems([
      {
        id: 1,
        name: 'iPhone 15 Pro Max',
        price: ********,
        quantity: 1,
        variant: '256GB - Natural Titanium',
        image: '/images/iphone-15-pro.jpg'
      },
      {
        id: 2,
        name: 'AirPods Pro 3',
        price: 6290000,
        quantity: 1,
        variant: 'Trắng',
        image: '/images/airpods-pro-3.jpg'
      }
    ]);
  }, []);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const calculateSubtotal = () => {
    return orderItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateShipping = () => {
    const subtotal = calculateSubtotal();
    return subtotal >= 2000000 ? 0 : 50000;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping();
  };

  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
    }).catch(() => {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
    });
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handlePlaceOrder = () => {
    form.validateFields().then((values) => {
      setLoading(true);
      
      const orderData = {
        ...values,
        items: orderItems,
        paymentMethod,
        total: calculateTotal(),
        subtotal: calculateSubtotal(),
        shipping: calculateShipping()
      };

      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        message.success('Đặt hàng thành công! Mã đơn hàng: #DH123456');
        console.log('Order data:', orderData);
        // Navigate to order confirmation page
      }, 2000);
    }).catch(() => {
      message.error('Vui lòng kiểm tra lại thông tin');
    });
  };

  const OrderSummary = () => (
    <Card className="order-summary" title="Thông tin đơn hàng">
      <div className="order-items">
        {orderItems.map(item => (
          <div key={item.id} className="order-item">
            <Row gutter={12} align="middle">
              <Col span={6}>
                <img 
                  src={item.image || '/images/placeholder.jpg'} 
                  alt={item.name}
                  className="item-image"
                />
              </Col>
              <Col span={12}>
                <Text strong className="item-name">{item.name}</Text>
                <br />
                <Text type="secondary" className="item-variant">{item.variant}</Text>
                <br />
                <Text type="secondary">x{item.quantity}</Text>
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Text strong>{formatPrice(item.price * item.quantity)}</Text>
              </Col>
            </Row>
          </div>
        ))}
      </div>

      <Divider />

      <div className="summary-calculations">
        <div className="summary-row">
          <Text>Tạm tính:</Text>
          <Text strong>{formatPrice(calculateSubtotal())}</Text>
        </div>
        <div className="summary-row">
          <Text>Phí vận chuyển:</Text>
          <Text strong>
            {calculateShipping() === 0 ? 'Miễn phí' : formatPrice(calculateShipping())}
          </Text>
        </div>
        <Divider />
        <div className="summary-row total">
          <Text strong style={{ fontSize: '1.1em' }}>Tổng cộng:</Text>
          <Text strong style={{ fontSize: '1.2em', color: '#ff4d4f' }}>
            {formatPrice(calculateTotal())}
          </Text>
        </div>
      </div>
    </Card>
  );

  const ShippingForm = () => (
    <Card title={<><EnvironmentOutlined /> Thông tin giao hàng</>}>
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            name="fullName"
            label="Họ và tên"
            rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
          >
            <Input placeholder="Nhập họ và tên" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="phone"
            label="Số điện thoại"
            rules={[
              { required: true, message: 'Vui lòng nhập số điện thoại' },
              { pattern: /^[0-9]{10}$/, message: 'Số điện thoại không hợp lệ' }
            ]}
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Vui lòng nhập email' },
              { type: 'email', message: 'Email không hợp lệ' }
            ]}
          >
            <Input placeholder="Nhập email" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="city"
            label="Tỉnh/Thành phố"
            rules={[{ required: true, message: 'Vui lòng chọn tỉnh/thành phố' }]}
          >
            <Select placeholder="Chọn tỉnh/thành phố">
              <Option value="hanoi">Hà Nội</Option>
              <Option value="hcm">TP. Hồ Chí Minh</Option>
              <Option value="danang">Đà Nẵng</Option>
              <Option value="haiphong">Hải Phòng</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="district"
            label="Quận/Huyện"
            rules={[{ required: true, message: 'Vui lòng chọn quận/huyện' }]}
          >
            <Select placeholder="Chọn quận/huyện">
              <Option value="district1">Quận 1</Option>
              <Option value="district2">Quận 2</Option>
              <Option value="district3">Quận 3</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="ward"
            label="Phường/Xã"
            rules={[{ required: true, message: 'Vui lòng chọn phường/xã' }]}
          >
            <Select placeholder="Chọn phường/xã">
              <Option value="ward1">Phường 1</Option>
              <Option value="ward2">Phường 2</Option>
              <Option value="ward3">Phường 3</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="address"
            label="Địa chỉ cụ thể"
            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}
          >
            <Input placeholder="Số nhà, tên đường..." />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="note" label="Ghi chú">
            <TextArea 
              placeholder="Ghi chú cho đơn hàng (tùy chọn)"
              rows={3}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const PaymentForm = () => (
    <Card title={<><CreditCardOutlined /> Phương thức thanh toán</>}>
      <Radio.Group
        value={paymentMethod}
        onChange={(e) => setPaymentMethod(e.target.value)}
        className="payment-methods"
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Radio value="cod" className="payment-option">
            <div className="payment-content">
              <WalletOutlined className="payment-icon" />
              <div>
                <Text strong>Thanh toán khi nhận hàng (COD)</Text>
                <br />
                <Text type="secondary">Thanh toán bằng tiền mặt khi nhận hàng</Text>
              </div>
            </div>
          </Radio>
          
          <Radio value="bank_transfer" className="payment-option">
            <div className="payment-content">
              <BankOutlined className="payment-icon" />
              <div>
                <Text strong>Chuyển khoản ngân hàng</Text>
                <br />
                <Text type="secondary">Chuyển khoản qua ngân hàng hoặc ví điện tử</Text>
              </div>
            </div>
          </Radio>
          
          <Radio value="credit_card" className="payment-option">
            <div className="payment-content">
              <CreditCardOutlined className="payment-icon" />
              <div>
                <Text strong>Thẻ tín dụng/Ghi nợ</Text>
                <br />
                <Text type="secondary">Visa, MasterCard, JCB</Text>
              </div>
            </div>
          </Radio>
        </Space>
      </Radio.Group>

      {paymentMethod === 'credit_card' && (
        <div className="credit-card-form">
          <Divider />
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="cardNumber"
                label="Số thẻ"
                rules={[{ required: true, message: 'Vui lòng nhập số thẻ' }]}
              >
                <Input placeholder="1234 5678 9012 3456" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="cardName"
                label="Tên trên thẻ"
                rules={[{ required: true, message: 'Vui lòng nhập tên trên thẻ' }]}
              >
                <Input placeholder="Tên chủ thẻ" />
              </Form.Item>
            </Col>
            <Col xs={12} md={6}>
              <Form.Item
                name="expiry"
                label="Ngày hết hạn"
                rules={[{ required: true, message: 'Vui lòng nhập ngày hết hạn' }]}
              >
                <Input placeholder="MM/YY" />
              </Form.Item>
            </Col>
            <Col xs={12} md={6}>
              <Form.Item
                name="cvv"
                label="CVV"
                rules={[{ required: true, message: 'Vui lòng nhập CVV' }]}
              >
                <Input placeholder="123" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      )}
    </Card>
  );

  const steps = [
    { title: 'Thông tin giao hàng', icon: <EnvironmentOutlined /> },
    { title: 'Thanh toán', icon: <CreditCardOutlined /> },
    { title: 'Xác nhận', icon: <UserOutlined /> }
  ];

  return (
    <Layout className="checkout-page">
      <Content>
        <div className="container">
          <Title level={2}>Thanh toán</Title>
          
          <Steps current={currentStep} className="checkout-steps">
            {steps.map((step, index) => (
              <Step key={index} title={step.title} icon={step.icon} />
            ))}
          </Steps>

          <Form form={form} layout="vertical" className="checkout-form">
            <Row gutter={24}>
              <Col xs={24} lg={16}>
                {currentStep === 0 && <ShippingForm />}
                {currentStep === 1 && <PaymentForm />}
                {currentStep === 2 && (
                  <Card title="Xác nhận đơn hàng">
                    <Text>Vui lòng kiểm tra lại thông tin đơn hàng trước khi đặt hàng.</Text>
                  </Card>
                )}
              </Col>

              <Col xs={24} lg={8}>
                <OrderSummary />
              </Col>
            </Row>

            <div className="checkout-actions">
              <Space>
                {currentStep > 0 && (
                  <Button size="large" onClick={handlePrevious}>
                    Quay lại
                  </Button>
                )}
                {currentStep < steps.length - 1 && (
                  <Button type="primary" size="large" onClick={handleNext}>
                    Tiếp tục
                  </Button>
                )}
                {currentStep === steps.length - 1 && (
                  <Button 
                    type="primary" 
                    size="large" 
                    onClick={handlePlaceOrder}
                    loading={loading}
                  >
                    Đặt hàng
                  </Button>
                )}
              </Space>
            </div>
          </Form>
        </div>
      </Content>
    </Layout>
  );
};

export default CheckoutPage;
