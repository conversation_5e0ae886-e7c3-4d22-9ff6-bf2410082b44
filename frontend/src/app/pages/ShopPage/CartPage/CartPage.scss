.cart-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      margin: 0;
    }
  }

  .empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    border-radius: 8px;
  }

  .cart-items {
    margin-bottom: 2rem;
  }

  .cart-item {
    border-radius: 8px;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .item-image {
      position: relative;
      width: 100%;
      height: 80px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }

      .out-of-stock-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.6);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        border-radius: 4px;
      }
    }

    .item-details {
      .item-name {
        margin-bottom: 0.25rem;
        
        &.out-of-stock {
          color: #999;
        }
      }

      .item-variant {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      .item-price {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.25rem;

        .current-price {
          color: #ff4d4f;
          font-size: 1rem;
        }

        .original-price {
          font-size: 0.85rem;
        }
      }

      .stock-status {
        font-size: 0.85rem;
        font-weight: 500;
      }
    }

    .quantity-controls {
      display: flex;
      justify-content: center;
      align-items: center;

      .quantity-input {
        width: 60px;
        text-align: center;
      }
    }

    .item-actions {
      display: flex;
      justify-content: center;
    }
  }

  .order-summary {
    position: sticky;
    top: 2rem;
    border-radius: 8px;

    .ant-card-head {
      background: #f5f5f5;
      border-radius: 8px 8px 0 0;
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;

      &.total {
        margin-bottom: 1rem;
        padding: 0.75rem 0;
        border-top: 1px solid #f0f0f0;
      }
    }

    .free-shipping-note {
      display: block;
      text-align: center;
      margin: 0.5rem 0;
      font-size: 0.9rem;
    }

    .checkout-btn {
      margin: 1rem 0 0.5rem 0;
      height: 48px;
      font-size: 1rem;
      font-weight: 600;
    }

    .continue-shopping-btn {
      height: 40px;
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .cart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .cart-item {
      .ant-row {
        .ant-col {
          margin-bottom: 0.5rem;
        }
      }

      .item-image {
        height: 60px;
      }

      .quantity-controls {
        margin-top: 0.5rem;
      }

      .item-actions {
        margin-top: 0.5rem;
      }
    }

    .order-summary {
      position: static;
      margin-top: 1rem;
    }
  }

  @media (max-width: 576px) {
    .cart-item {
      .quantity-controls,
      .item-actions {
        justify-content: flex-start;
      }
    }
  }
}
