import React, { useState, useEffect } from 'react';
import './CartPage.scss';
import { Layout, Typography, Card, Row, Col, Button, InputNumber, Space, Divider, Empty, message } from 'antd';
import { DeleteOutlined, ShoppingOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

const CartPage = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mock data - sẽ thay thế bằng API calls thực tế
    setCartItems([
      {
        id: 1,
        productId: 1,
        name: 'iPhone 15 Pro Max',
        price: 29990000,
        originalPrice: 32990000,
        image: '/images/iphone-15-pro.jpg',
        quantity: 1,
        variant: '256GB - Natural Titanium',
        inStock: true,
        maxQuantity: 5
      },
      {
        id: 2,
        productId: 3,
        name: 'MacBook Air M3',
        price: 31990000,
        originalPrice: 34990000,
        image: '/images/macbook-air-m3.jpg',
        quantity: 1,
        variant: '13-inch - 8GB RAM - 256GB SSD',
        inStock: true,
        maxQuantity: 3
      },
      {
        id: 3,
        productId: 5,
        name: 'AirPods Pro 3',
        price: 6290000,
        image: '/images/airpods-pro-3.jpg',
        quantity: 2,
        variant: 'Trắng',
        inStock: false,
        maxQuantity: 10
      }
    ]);
  }, []);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity < 1) return;
    
    const item = cartItems.find(item => item.id === itemId);
    if (newQuantity > item.maxQuantity) {
      message.warning(`Số lượng tối đa cho sản phẩm này là ${item.maxQuantity}`);
      return;
    }

    setCartItems(cartItems.map(item => 
      item.id === itemId 
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const removeItem = (itemId) => {
    setCartItems(cartItems.filter(item => item.id !== itemId));
    message.success('Đã xóa sản phẩm khỏi giỏ hàng');
  };

  const clearCart = () => {
    setCartItems([]);
    message.success('Đã xóa tất cả sản phẩm khỏi giỏ hàng');
  };

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateDiscount = () => {
    return cartItems.reduce((total, item) => {
      const discount = item.originalPrice ? (item.originalPrice - item.price) * item.quantity : 0;
      return total + discount;
    }, 0);
  };

  const calculateShipping = () => {
    const subtotal = calculateSubtotal();
    return subtotal >= 2000000 ? 0 : 50000; // Free shipping for orders over 2M VND
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping();
  };

  const handleCheckout = () => {
    const unavailableItems = cartItems.filter(item => !item.inStock);
    if (unavailableItems.length > 0) {
      message.error('Một số sản phẩm trong giỏ hàng đã hết hàng. Vui lòng xóa hoặc thay thế.');
      return;
    }
    
    setLoading(true);
    // Simulate checkout process
    setTimeout(() => {
      setLoading(false);
      message.success('Chuyển đến trang thanh toán...');
      // Navigate to checkout page
    }, 1000);
  };

  const CartItem = ({ item }) => (
    <Card className="cart-item" size="small">
      <Row gutter={16} align="middle">
        <Col xs={24} sm={6} md={4}>
          <div className="item-image">
            <img src={item.image || '/images/placeholder.jpg'} alt={item.name} />
            {!item.inStock && <div className="out-of-stock-overlay">Hết hàng</div>}
          </div>
        </Col>
        
        <Col xs={24} sm={12} md={12}>
          <div className="item-details">
            <Title level={5} className={`item-name ${!item.inStock ? 'out-of-stock' : ''}`}>
              {item.name}
            </Title>
            <Text type="secondary" className="item-variant">{item.variant}</Text>
            <div className="item-price">
              <Text strong className="current-price">{formatPrice(item.price)}</Text>
              {item.originalPrice && (
                <Text delete type="secondary" className="original-price">
                  {formatPrice(item.originalPrice)}
                </Text>
              )}
            </div>
            {!item.inStock && (
              <Text type="danger" className="stock-status">Sản phẩm hiện đã hết hàng</Text>
            )}
          </div>
        </Col>
        
        <Col xs={12} sm={4} md={6}>
          <div className="quantity-controls">
            <Space>
              <Button 
                size="small" 
                icon={<MinusOutlined />}
                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                disabled={!item.inStock || item.quantity <= 1}
              />
              <InputNumber
                min={1}
                max={item.maxQuantity}
                value={item.quantity}
                onChange={(value) => updateQuantity(item.id, value)}
                disabled={!item.inStock}
                size="small"
                className="quantity-input"
              />
              <Button 
                size="small" 
                icon={<PlusOutlined />}
                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                disabled={!item.inStock || item.quantity >= item.maxQuantity}
              />
            </Space>
          </div>
        </Col>
        
        <Col xs={12} sm={2} md={2}>
          <div className="item-actions">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              onClick={() => removeItem(item.id)}
              size="small"
            />
          </div>
        </Col>
      </Row>
    </Card>
  );

  if (cartItems.length === 0) {
    return (
      <Layout className="cart-page">
        <Content>
          <div className="container">
            <Title level={2}>Giỏ hàng</Title>
            <Card className="empty-cart">
              <Empty
                image={<ShoppingOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
                imageStyle={{ height: 80 }}
                description="Giỏ hàng của bạn đang trống"
              >
                <Button type="primary" size="large">
                  Tiếp tục mua sắm
                </Button>
              </Empty>
            </Card>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="cart-page">
      <Content>
        <div className="container">
          <div className="cart-header">
            <Title level={2}>Giỏ hàng ({cartItems.length} sản phẩm)</Title>
            <Button type="link" danger onClick={clearCart}>
              Xóa tất cả
            </Button>
          </div>

          <Row gutter={24}>
            <Col xs={24} lg={16}>
              <div className="cart-items">
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  {cartItems.map(item => (
                    <CartItem key={item.id} item={item} />
                  ))}
                </Space>
              </div>
            </Col>

            <Col xs={24} lg={8}>
              <Card className="order-summary" title="Tóm tắt đơn hàng">
                <div className="summary-row">
                  <Text>Tạm tính:</Text>
                  <Text strong>{formatPrice(calculateSubtotal())}</Text>
                </div>
                
                {calculateDiscount() > 0 && (
                  <div className="summary-row">
                    <Text>Giảm giá:</Text>
                    <Text strong style={{ color: '#52c41a' }}>
                      -{formatPrice(calculateDiscount())}
                    </Text>
                  </div>
                )}
                
                <div className="summary-row">
                  <Text>Phí vận chuyển:</Text>
                  <Text strong>
                    {calculateShipping() === 0 ? 'Miễn phí' : formatPrice(calculateShipping())}
                  </Text>
                </div>
                
                {calculateShipping() === 0 && (
                  <Text type="success" className="free-shipping-note">
                    🎉 Bạn được miễn phí vận chuyển!
                  </Text>
                )}
                
                <Divider />
                
                <div className="summary-row total">
                  <Text strong style={{ fontSize: '1.1em' }}>Tổng cộng:</Text>
                  <Text strong style={{ fontSize: '1.2em', color: '#ff4d4f' }}>
                    {formatPrice(calculateTotal())}
                  </Text>
                </div>
                
                <Button 
                  type="primary" 
                  size="large" 
                  block
                  className="checkout-btn"
                  onClick={handleCheckout}
                  loading={loading}
                  disabled={cartItems.some(item => !item.inStock)}
                >
                  Tiến hành thanh toán
                </Button>
                
                <Button 
                  type="default" 
                  size="large" 
                  block
                  className="continue-shopping-btn"
                >
                  Tiếp tục mua sắm
                </Button>
              </Card>
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  );
};

export default CartPage;
