import React, { useState, useEffect } from 'react';
import './HomePage.scss';
import { Layout, Typography, Button, Card, Row, Col, Carousel, Space, Tag, Input, message } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, SearchOutlined, StarFilled } from '@ant-design/icons';
import { useAuth } from '@app/hooks/useAuth';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

const HomePage = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [newProducts, setNewProducts] = useState([]);
  const { requireAuth } = useAuth();

  useEffect(() => {
    // Mock data - sẽ thay thế bằng API calls thực tế
    setFeaturedProducts([
      {
        id: 1,
        name: 'iPhone 15 Pro Max',
        price: 29990000,
        originalPrice: 32990000,
        image: '/images/iphone-15-pro.jpg',
        rating: 4.8,
        reviews: 156,
        discount: 10
      },
      {
        id: 2,
        name: 'Samsung Galaxy S24 Ultra',
        price: 27490000,
        originalPrice: 29990000,
        image: '/images/samsung-s24.jpg',
        rating: 4.7,
        reviews: 89,
        discount: 8
      },
      {
        id: 3,
        name: 'MacBook Air M3',
        price: 31990000,
        originalPrice: 34990000,
        image: '/images/macbook-air-m3.jpg',
        rating: 4.9,
        reviews: 234,
        discount: 9
      }
    ]);

    setCategories([
      { id: 1, name: 'Điện thoại', icon: '📱', count: 150 },
      { id: 2, name: 'Laptop', icon: '💻', count: 80 },
      { id: 3, name: 'Tablet', icon: '📱', count: 45 },
      { id: 4, name: 'Phụ kiện', icon: '🎧', count: 200 },
      { id: 5, name: 'Đồng hồ', icon: '⌚', count: 60 },
      { id: 6, name: 'TV', icon: '📺', count: 35 }
    ]);

    setNewProducts([
      {
        id: 4,
        name: 'iPad Pro 13-inch M4',
        price: 32990000,
        image: '/images/ipad-pro-m4.jpg',
        rating: 4.8,
        reviews: 67,
        isNew: true
      },
      {
        id: 5,
        name: 'AirPods Pro 3',
        price: 6290000,
        image: '/images/airpods-pro-3.jpg',
        rating: 4.6,
        reviews: 123,
        isNew: true
      }
    ]);
  }, []);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleSearch = (value) => {
    console.log('Tìm kiếm:', value);
    // Implement search logic
  };

  const handleAddToCart = (productId) => {
    requireAuth(() => {
      console.log('Thêm vào giỏ hàng:', productId);
      // TODO: Implement add to cart logic
      message.success('Đã thêm sản phẩm vào giỏ hàng!');
    }, {
      title: 'Đăng nhập để mua hàng',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.'
    });
  };

  const handleAddToWishlist = (productId) => {
    requireAuth(() => {
      console.log('Thêm vào yêu thích:', productId);
      // TODO: Implement add to wishlist logic
      message.success('Đã thêm sản phẩm vào danh sách yêu thích!');
    }, {
      title: 'Đăng nhập để lưu yêu thích',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào danh sách yêu thích.'
    });
  };

  const ProductCard = ({ product, showDiscount = false }) => (
    <Card 
      className="product-card"
      hoverable
      cover={
        <div className="product-image-container">
          <img src={product.image || '/images/placeholder.jpg'} alt={product.name} />
          {showDiscount && product.discount && (
            <Tag color="red" className="discount-tag">-{product.discount}%</Tag>
          )}
          {product.isNew && <Tag color="green" className="new-tag">Mới</Tag>}
          <div className="product-actions">
            <Button 
              icon={<HeartOutlined />} 
              size="small" 
              onClick={() => handleAddToWishlist(product.id)}
            />
            <Button 
              type="primary" 
              icon={<ShoppingCartOutlined />} 
              size="small"
              onClick={() => handleAddToCart(product.id)}
            >
              Thêm vào giỏ
            </Button>
          </div>
        </div>
      }
    >
      <Card.Meta
        title={<Text className="product-name">{product.name}</Text>}
        description={
          <div className="product-info">
            <div className="product-rating">
              <StarFilled style={{ color: '#faad14' }} />
              <Text>{product.rating}</Text>
              <Text type="secondary">({product.reviews} đánh giá)</Text>
            </div>
            <div className="product-price">
              <Text strong className="current-price">{formatPrice(product.price)}</Text>
              {product.originalPrice && (
                <Text delete type="secondary" className="original-price">
                  {formatPrice(product.originalPrice)}
                </Text>
              )}
            </div>
          </div>
        }
      />
    </Card>
  );

  const bannerImages = [
    '/images/banner-1.jpg',
    '/images/banner-2.jpg',
    '/images/banner-3.jpg'
  ];

  return (
    <Layout className="shop-homepage">
      <Content>
        {/* Hero Banner */}
        <section className="hero-section">
          <Carousel autoplay className="hero-banner">
            {bannerImages.map((image, index) => (
              <div key={index} className="banner-slide">
                <img src={image} alt={`Banner ${index + 1}`} />
                <div className="banner-content">
                  <Title level={1}>Chào mừng đến TechStore</Title>
                  <Paragraph>Khám phá những sản phẩm công nghệ mới nhất với giá tốt nhất</Paragraph>
                  <Button type="primary" size="large">Mua sắm ngay</Button>
                </div>
              </div>
            ))}
          </Carousel>
        </section>

        <div className="container">
          {/* Search Section */}
          <section className="search-section">
            <div className="search-container">
              <Search
                placeholder="Tìm kiếm sản phẩm..."
                size="large"
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
            </div>
          </section>

          {/* Categories Section */}
          <section className="categories-section">
            <Title level={2}>Danh mục sản phẩm</Title>
            <Row gutter={[16, 16]} className="categories-grid">
              {categories.map((category) => (
                <Col xs={12} sm={8} md={6} lg={4} key={category.id}>
                  <Card className="category-card" hoverable>
                    <div className="category-content">
                      <div className="category-icon">{category.icon}</div>
                      <Title level={4}>{category.name}</Title>
                      <Text type="secondary">{category.count} sản phẩm</Text>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </section>

          {/* Featured Products */}
          <section className="featured-section">
            <div className="section-header">
              <Title level={2}>Sản phẩm nổi bật</Title>
              <Button type="link">Xem tất cả</Button>
            </div>
            <Row gutter={[24, 24]} className="products-grid">
              {featuredProducts.map((product) => (
                <Col xs={24} sm={12} md={8} lg={6} key={product.id}>
                  <ProductCard product={product} showDiscount={true} />
                </Col>
              ))}
            </Row>
          </section>

          {/* New Products */}
          <section className="new-products-section">
            <div className="section-header">
              <Title level={2}>Sản phẩm mới</Title>
              <Button type="link">Xem tất cả</Button>
            </div>
            <Row gutter={[24, 24]} className="products-grid">
              {newProducts.map((product) => (
                <Col xs={24} sm={12} md={8} lg={6} key={product.id}>
                  <ProductCard product={product} />
                </Col>
              ))}
            </Row>
          </section>

          {/* Promotion Section */}
          <section className="promotion-section">
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Card className="promotion-card" cover={<img src="/images/promo-1.jpg" alt="Khuyến mãi 1" />}>
                  <Title level={3}>Giảm giá lên đến 50%</Title>
                  <Paragraph>Cho tất cả sản phẩm điện thoại</Paragraph>
                  <Button type="primary">Mua ngay</Button>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card className="promotion-card" cover={<img src="/images/promo-2.jpg" alt="Khuyến mãi 2" />}>
                  <Title level={3}>Miễn phí giao hàng</Title>
                  <Paragraph>Cho đơn hàng từ 2 triệu đồng</Paragraph>
                  <Button type="primary">Tìm hiểu thêm</Button>
                </Card>
              </Col>
            </Row>
          </section>
        </div>
      </Content>
    </Layout>
  );
};

export default HomePage;

