.shop-homepage {
  .hero-section {
    margin-bottom: 2rem;

    .hero-banner {
      .banner-slide {
        position: relative;
        height: 400px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .banner-content {
          position: absolute;
          top: 50%;
          left: 10%;
          transform: translateY(-50%);
          color: white;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.5);

          .ant-typography {
            color: white !important;
            margin-bottom: 1rem;
          }

          h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
          }

          .ant-btn {
            margin-top: 1rem;
          }
        }
      }
    }
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .search-section {
    margin: 2rem 0;

    .search-container {
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .categories-section {
    margin: 3rem 0;

    .categories-grid {
      margin-top: 1.5rem;
    }

    .category-card {
      text-align: center;
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
      }

      .category-content {
        padding: 1rem;

        .category-icon {
          font-size: 2.5rem;
          margin-bottom: 0.5rem;
        }

        h4 {
          margin: 0.5rem 0;
          color: #1890ff;
        }
      }
    }
  }

  .featured-section,
  .new-products-section {
    margin: 3rem 0;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;

      h2 {
        margin: 0;
      }
    }

    .products-grid {
      margin-top: 1.5rem;
    }
  }

  .product-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0,0,0,0.15);
    }

    .product-image-container {
      position: relative;
      height: 200px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .discount-tag,
      .new-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        font-weight: bold;
        border: none;
        border-radius: 4px;
      }

      .product-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .product-actions {
        opacity: 1;
      }
    }

    .ant-card-body {
      padding: 1rem;
    }

    .product-name {
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 0.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .product-info {
      .product-rating {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 0.5rem;

        .anticon {
          font-size: 14px;
        }
      }

      .product-price {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .current-price {
          font-size: 1.2rem;
          color: #ff4d4f;
        }

        .original-price {
          font-size: 0.9rem;
        }
      }
    }
  }

  .promotion-section {
    margin: 3rem 0;

    .promotion-card {
      border-radius: 12px;
      overflow: hidden;

      .ant-card-cover img {
        height: 200px;
        object-fit: cover;
      }

      .ant-card-body {
        text-align: center;
        padding: 1.5rem;

        h3 {
          color: #1890ff;
          margin-bottom: 0.5rem;
        }

        p {
          margin-bottom: 1rem;
          color: #666;
        }
      }
    }
  }

  // Responsive
  @media (max-width: 768px) {
    .hero-section .hero-banner .banner-slide {
      height: 250px;

      .banner-content {
        left: 5%;
        text-align: center;

        h1 {
          font-size: 2rem;
        }
      }
    }

    .container {
      padding: 0 0.5rem;
    }

    .categories-section .category-card .category-content {
      padding: 0.5rem;

      .category-icon {
        font-size: 2rem;
      }
    }

    .product-card .product-image-container {
      height: 150px;
    }
  }
}

