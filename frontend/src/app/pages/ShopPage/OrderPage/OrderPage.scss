.order-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      margin: 0;
    }
  }

  .orders-table {
    border-radius: 8px;

    .ant-table {
      .ant-table-thead th {
        background: #f8f9fa;
        font-weight: 600;
      }

      .ant-table-tbody tr:hover {
        background: #f6ffed;
      }
    }
  }

  .order-detail-modal {
    .order-detail-content {
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding: 0.25rem 0;

        &:not(:last-child) {
          border-bottom: 1px solid #f0f0f0;
        }
      }

      .products-card {
        margin-top: 1rem;

        .order-item {
          .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
          }
        }

        .order-total {
          margin-top: 1rem;
          padding-top: 1rem;
          border-top: 2px solid #f0f0f0;
        }
      }
    }
  }

  .review-content {
    .product-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;

      .product-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
      }

      .product-details {
        flex: 1;
      }
    }

    .review-form {
      .rating-section,
      .comment-section {
        margin-bottom: 1rem;

        > span {
          display: block;
          margin-bottom: 0.5rem;
        }
      }

      .review-rating {
        font-size: 1.5rem;
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .orders-table {
      .ant-table {
        font-size: 0.85rem;

        .ant-table-tbody td {
          padding: 8px;
        }
      }
    }

    .order-detail-modal {
      .ant-modal {
        margin: 0;
        max-width: 100vw;
        width: 100vw !important;
        height: 100vh;
        
        .ant-modal-content {
          height: 100vh;
          border-radius: 0;
        }
      }

      .order-detail-content {
        .ant-row {
          flex-direction: column;
        }

        .info-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;
        }
      }
    }

    .review-content {
      .product-info {
        flex-direction: column;
        text-align: center;
      }
    }
  }

  @media (max-width: 576px) {
    .orders-table {
      .ant-table-wrapper {
        .ant-table {
          .ant-table-content {
            overflow-x: auto;
          }
          
          .ant-table-thead th,
          .ant-table-tbody td {
            white-space: nowrap;
            min-width: 100px;
          }
        }
      }
    }
  }
}
