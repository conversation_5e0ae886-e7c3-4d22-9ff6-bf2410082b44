import React, { useState, useEffect } from 'react';
import './OrderPage.scss';
import { Layout, Typography, Card, Row, Col, Table, Button, Tag, Space, Divider, Modal, Rate, Input, message } from 'antd';
import { EyeOutlined, StarOutlined, ShoppingCartOutlined, ReloadOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const OrderPage = () => {
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [reviewVisible, setReviewVisible] = useState(false);
  const [reviewProduct, setReviewProduct] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = () => {
    setLoading(true);
    // Mock data - sẽ thay thế bằng API calls thực tế
    setTimeout(() => {
      setOrders([
        {
          id: 'DH001',
          orderDate: '2024-01-15',
          status: 'delivered',
          total: 36280000,
          items: [
            {
              id: 1,
              name: 'iPhone 15 Pro Max',
              price: 29990000,
              quantity: 1,
              variant: '256GB - Natural Titanium',
              image: '/images/iphone-15-pro.jpg',
              canReview: true
            },
            {
              id: 2,
              name: 'AirPods Pro 3',
              price: 6290000,
              quantity: 1,
              variant: 'Trắng',
              image: '/images/airpods-pro-3.jpg',
              canReview: true
            }
          ],
          shippingAddress: 'Số 123 Đường ABC, Phường XYZ, Quận 1, TP.HCM',
          paymentMethod: 'cod',
          trackingNumber: 'TN123456789'
        },
        {
          id: 'DH002',
          orderDate: '2024-01-20',
          status: 'shipping',
          total: ********,
          items: [
            {
              id: 3,
              name: 'MacBook Air M3',
              price: ********,
              quantity: 1,
              variant: '13-inch - 8GB RAM - 256GB SSD',
              image: '/images/macbook-air-m3.jpg',
              canReview: false
            }
          ],
          shippingAddress: 'Số 456 Đường DEF, Phường ABC, Quận 2, TP.HCM',
          paymentMethod: 'bank_transfer',
          trackingNumber: 'TN987654321'
        },
        {
          id: 'DH003',
          orderDate: '2024-01-22',
          status: 'processing',
          total: ********,
          items: [
            {
              id: 4,
              name: 'Samsung Galaxy S24 Ultra',
              price: ********,
              quantity: 1,
              variant: '512GB - Titanium Black',
              image: '/images/samsung-s24.jpg',
              canReview: false
            }
          ],
          shippingAddress: 'Số 789 Đường GHI, Phường DEF, Quận 3, TP.HCM',
          paymentMethod: 'credit_card',
          trackingNumber: null
        }
      ]);
      setLoading(false);
    }, 1000);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'orange',
      'processing': 'blue',
      'shipping': 'cyan',
      'delivered': 'green',
      'cancelled': 'red',
      'returned': 'purple'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': 'Chờ xử lý',
      'processing': 'Đang xử lý',
      'shipping': 'Đang giao hàng',
      'delivered': 'Đã giao hàng',
      'cancelled': 'Đã hủy',
      'returned': 'Đã trả hàng'
    };
    return texts[status] || status;
  };

  const getPaymentMethodText = (method) => {
    const texts = {
      'cod': 'Thanh toán khi nhận hàng',
      'bank_transfer': 'Chuyển khoản ngân hàng',
      'credit_card': 'Thẻ tín dụng'
    };
    return texts[method] || method;
  };

  const handleViewDetail = (order) => {
    setSelectedOrder(order);
    setDetailVisible(true);
  };

  const handleReorder = (order) => {
    message.success(`Đã thêm ${order.items.length} sản phẩm vào giỏ hàng`);
    console.log('Reorder:', order);
  };

  const handleCancelOrder = (orderId) => {
    Modal.confirm({
      title: 'Xác nhận hủy đơn hàng',
      content: 'Bạn có chắc chắn muốn hủy đơn hàng này?',
      onOk: () => {
        setOrders(orders.map(order => 
          order.id === orderId 
            ? { ...order, status: 'cancelled' }
            : order
        ));
        message.success('Đã hủy đơn hàng thành công');
      }
    });
  };

  const handleReview = (product) => {
    setReviewProduct(product);
    setReviewVisible(true);
  };

  const handleSubmitReview = (values) => {
    console.log('Review submitted:', values);
    message.success('Cảm ơn bạn đã đánh giá sản phẩm!');
    setReviewVisible(false);
    setReviewProduct(null);
  };

  const columns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Ngày đặt',
      dataIndex: 'orderDate',
      key: 'orderDate',
      render: formatDate
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total',
      key: 'total',
      render: (total) => <Text strong style={{ color: '#ff4d4f' }}>{formatPrice(total)}</Text>
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            Chi tiết
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            size="small"
            onClick={() => handleReorder(record)}
          >
            Đặt lại
          </Button>
          {record.status === 'pending' && (
            <Button 
              danger 
              size="small"
              onClick={() => handleCancelOrder(record.id)}
            >
              Hủy
            </Button>
          )}
        </Space>
      )
    }
  ];

  const OrderDetailModal = () => (
    <Modal
      title={`Chi tiết đơn hàng ${selectedOrder?.id}`}
      open={detailVisible}
      onCancel={() => setDetailVisible(false)}
      footer={null}
      width={800}
      className="order-detail-modal"
    >
      {selectedOrder && (
        <div className="order-detail-content">
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Card size="small" title="Thông tin đơn hàng">
                <div className="info-row">
                  <Text strong>Mã đơn hàng:</Text>
                  <Text>{selectedOrder.id}</Text>
                </div>
                <div className="info-row">
                  <Text strong>Ngày đặt:</Text>
                  <Text>{formatDate(selectedOrder.orderDate)}</Text>
                </div>
                <div className="info-row">
                  <Text strong>Trạng thái:</Text>
                  <Tag color={getStatusColor(selectedOrder.status)}>
                    {getStatusText(selectedOrder.status)}
                  </Tag>
                </div>
                <div className="info-row">
                  <Text strong>Phương thức thanh toán:</Text>
                  <Text>{getPaymentMethodText(selectedOrder.paymentMethod)}</Text>
                </div>
                {selectedOrder.trackingNumber && (
                  <div className="info-row">
                    <Text strong>Mã vận đơn:</Text>
                    <Text>{selectedOrder.trackingNumber}</Text>
                  </div>
                )}
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card size="small" title="Địa chỉ giao hàng">
                <Paragraph>{selectedOrder.shippingAddress}</Paragraph>
              </Card>
            </Col>
          </Row>

          <Card size="small" title="Sản phẩm đã đặt" className="products-card">
            {selectedOrder.items.map(item => (
              <div key={item.id} className="order-item">
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <img 
                      src={item.image || '/images/placeholder.jpg'} 
                      alt={item.name}
                      className="item-image"
                    />
                  </Col>
                  <Col span={12}>
                    <Text strong>{item.name}</Text>
                    <br />
                    <Text type="secondary">{item.variant}</Text>
                    <br />
                    <Text type="secondary">Số lượng: {item.quantity}</Text>
                  </Col>
                  <Col span={6}>
                    <Text strong>{formatPrice(item.price)}</Text>
                  </Col>
                  <Col span={2}>
                    {item.canReview && selectedOrder.status === 'delivered' && (
                      <Button 
                        type="link" 
                        icon={<StarOutlined />}
                        size="small"
                        onClick={() => handleReview(item)}
                      >
                        Đánh giá
                      </Button>
                    )}
                  </Col>
                </Row>
                <Divider />
              </div>
            ))}
            
            <div className="order-total">
              <Row justify="end">
                <Col>
                  <Text strong style={{ fontSize: '1.2em', color: '#ff4d4f' }}>
                    Tổng cộng: {formatPrice(selectedOrder.total)}
                  </Text>
                </Col>
              </Row>
            </div>
          </Card>
        </div>
      )}
    </Modal>
  );

  const ReviewModal = () => (
    <Modal
      title="Đánh giá sản phẩm"
      open={reviewVisible}
      onCancel={() => setReviewVisible(false)}
      onOk={() => {
        const rating = document.querySelector('.review-rating .ant-rate')?.getAttribute('aria-valuenow') || 5;
        const comment = document.querySelector('.review-comment textarea')?.value || '';
        handleSubmitReview({ rating: parseInt(rating), comment, product: reviewProduct });
      }}
    >
      {reviewProduct && (
        <div className="review-content">
          <div className="product-info">
            <img 
              src={reviewProduct.image || '/images/placeholder.jpg'} 
              alt={reviewProduct.name}
              className="product-image"
            />
            <div className="product-details">
              <Text strong>{reviewProduct.name}</Text>
              <br />
              <Text type="secondary">{reviewProduct.variant}</Text>
            </div>
          </div>
          
          <div className="review-form">
            <div className="rating-section">
              <Text strong>Đánh giá của bạn:</Text>
              <Rate defaultValue={5} className="review-rating" />
            </div>
            
            <div className="comment-section">
              <Text strong>Nhận xét:</Text>
              <TextArea 
                placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm..."
                rows={4}
                className="review-comment"
              />
            </div>
          </div>
        </div>
      )}
    </Modal>
  );

  return (
    <Layout className="order-page">
      <Content>
        <div className="container">
          <div className="page-header">
            <Title level={2}>Đơn hàng của tôi</Title>
            <Button icon={<ReloadOutlined />} onClick={fetchOrders} loading={loading}>
              Làm mới
            </Button>
          </div>

          <Card className="orders-table">
            <Table
              dataSource={orders}
              columns={columns}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} của ${total} đơn hàng`
              }}
            />
          </Card>

          <OrderDetailModal />
          <ReviewModal />
        </div>
      </Content>
    </Layout>
  );
};

export default OrderPage;
