import React, { useState, useEffect } from 'react';
import './ProductPage.scss';
import { Layout, Typography, Card, Row, Col, Button, Select, Input, Pagination, Space, Tag, Slider, Checkbox, message } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, FilterOutlined, StarFilled } from '@ant-design/icons';
import { useAuth } from '@app/hooks/useAuth';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const ProductPage = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const [sortBy, setSortBy] = useState('newest');
  const [priceRange, setPriceRange] = useState([0, 50000000]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const { requireAuth } = useAuth();

  useEffect(() => {
    // Mock data - sẽ thay thế bằng API calls thực tế
    const mockProducts = [
      {
        id: 1, name: 'iPhone 15 Pro Max', price: 29990000, originalPrice: 32990000,
        image: '/images/iphone-15-pro.jpg', rating: 4.8, reviews: 156,
        category: 'Điện thoại', brand: 'Apple', discount: 10
      },
      {
        id: 2, name: 'Samsung Galaxy S24 Ultra', price: 27490000, originalPrice: 29990000,
        image: '/images/samsung-s24.jpg', rating: 4.7, reviews: 89,
        category: 'Điện thoại', brand: 'Samsung', discount: 8
      },
      {
        id: 3, name: 'MacBook Air M3', price: 31990000, originalPrice: 34990000,
        image: '/images/macbook-air-m3.jpg', rating: 4.9, reviews: 234,
        category: 'Laptop', brand: 'Apple', discount: 9
      },
      {
        id: 4, name: 'iPad Pro 13-inch M4', price: 32990000,
        image: '/images/ipad-pro-m4.jpg', rating: 4.8, reviews: 67,
        category: 'Tablet', brand: 'Apple', isNew: true
      },
      {
        id: 5, name: 'AirPods Pro 3', price: 6290000,
        image: '/images/airpods-pro-3.jpg', rating: 4.6, reviews: 123,
        category: 'Phụ kiện', brand: 'Apple', isNew: true
      },
      {
        id: 6, name: 'Dell XPS 13', price: 28990000,
        image: '/images/dell-xps-13.jpg', rating: 4.5, reviews: 98,
        category: 'Laptop', brand: 'Dell'
      }
    ];

    setProducts(mockProducts);
    setFilteredProducts(mockProducts);

    setCategories(['Điện thoại', 'Laptop', 'Tablet', 'Phụ kiện', 'Đồng hồ', 'TV']);
    setBrands(['Apple', 'Samsung', 'Dell', 'HP', 'Asus', 'Xiaomi']);
  }, []);

  useEffect(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by categories
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(product => 
        selectedCategories.includes(product.category)
      );
    }

    // Filter by brands
    if (selectedBrands.length > 0) {
      filtered = filtered.filter(product => 
        selectedBrands.includes(product.brand)
      );
    }

    // Filter by price range
    filtered = filtered.filter(product => 
      product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
      default:
        filtered.sort((a, b) => b.id - a.id);
        break;
    }

    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [products, searchTerm, selectedCategories, selectedBrands, priceRange, sortBy]);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleAddToCart = (productId) => {
    requireAuth(() => {
      console.log('Thêm vào giỏ hàng:', productId);
      // TODO: Implement add to cart logic
      message.success('Đã thêm sản phẩm vào giỏ hàng!');
    }, {
      title: 'Đăng nhập để mua hàng',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.'
    });
  };

  const handleAddToWishlist = (productId) => {
    requireAuth(() => {
      console.log('Thêm vào yêu thích:', productId);
      // TODO: Implement add to wishlist logic
      message.success('Đã thêm sản phẩm vào danh sách yêu thích!');
    }, {
      title: 'Đăng nhập để lưu yêu thích',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào danh sách yêu thích.'
    });
  };

  const ProductCard = ({ product }) => (
    <Card 
      className="product-card"
      hoverable
      cover={
        <div className="product-image-container">
          <img src={product.image || '/images/placeholder.jpg'} alt={product.name} />
          {product.discount && (
            <Tag color="red" className="discount-tag">-{product.discount}%</Tag>
          )}
          {product.isNew && <Tag color="green" className="new-tag">Mới</Tag>}
          <div className="product-actions">
            <Button 
              icon={<HeartOutlined />} 
              size="small" 
              onClick={() => handleAddToWishlist(product.id)}
            />
            <Button 
              type="primary" 
              icon={<ShoppingCartOutlined />} 
              size="small"
              onClick={() => handleAddToCart(product.id)}
            >
              Thêm vào giỏ
            </Button>
          </div>
        </div>
      }
    >
      <Card.Meta
        title={<Text className="product-name">{product.name}</Text>}
        description={
          <div className="product-info">
            <div className="product-rating">
              <StarFilled style={{ color: '#faad14' }} />
              <Text>{product.rating}</Text>
              <Text type="secondary">({product.reviews} đánh giá)</Text>
            </div>
            <div className="product-price">
              <Text strong className="current-price">{formatPrice(product.price)}</Text>
              {product.originalPrice && (
                <Text delete type="secondary" className="original-price">
                  {formatPrice(product.originalPrice)}
                </Text>
              )}
            </div>
          </div>
        }
      />
    </Card>
  );

  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <Layout className="product-page">
      <Content>
        <div className="container">
          {/* Header */}
          <div className="page-header">
            <Title level={2}>Danh sách sản phẩm</Title>
            <Text type="secondary">Tìm thấy {filteredProducts.length} sản phẩm</Text>
          </div>

          {/* Search and Sort */}
          <div className="search-sort-section">
            <Row gutter={16} align="middle">
              <Col xs={24} md={16}>
                <Search
                  placeholder="Tìm kiếm sản phẩm..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onSearch={setSearchTerm}
                  size="large"
                />
              </Col>
              <Col xs={24} md={8}>
                <Select
                  value={sortBy}
                  onChange={setSortBy}
                  size="large"
                  style={{ width: '100%' }}
                >
                  <Option value="newest">Mới nhất</Option>
                  <Option value="price-low">Giá thấp đến cao</Option>
                  <Option value="price-high">Giá cao đến thấp</Option>
                  <Option value="rating">Đánh giá cao nhất</Option>
                </Select>
              </Col>
            </Row>
          </div>

          <Row gutter={24}>
            {/* Filters Sidebar */}
            <Col xs={24} lg={6}>
              <Card className="filters-card">
                <Title level={4}>
                  <FilterOutlined /> Bộ lọc
                </Title>
                
                {/* Price Range */}
                <div className="filter-section">
                  <Title level={5}>Khoảng giá</Title>
                  <Slider
                    range
                    min={0}
                    max={50000000}
                    step={1000000}
                    value={priceRange}
                    onChange={setPriceRange}
                    tipFormatter={(value) => formatPrice(value)}
                  />
                  <div className="price-range-text">
                    <Text>{formatPrice(priceRange[0])}</Text>
                    <Text>{formatPrice(priceRange[1])}</Text>
                  </div>
                </div>

                {/* Categories */}
                <div className="filter-section">
                  <Title level={5}>Danh mục</Title>
                  <Checkbox.Group
                    options={categories}
                    value={selectedCategories}
                    onChange={setSelectedCategories}
                  />
                </div>

                {/* Brands */}
                <div className="filter-section">
                  <Title level={5}>Thương hiệu</Title>
                  <Checkbox.Group
                    options={brands}
                    value={selectedBrands}
                    onChange={setSelectedBrands}
                  />
                </div>

                {/* Clear Filters */}
                <Button 
                  type="link" 
                  onClick={() => {
                    setSelectedCategories([]);
                    setSelectedBrands([]);
                    setPriceRange([0, 50000000]);
                    setSearchTerm('');
                  }}
                >
                  Xóa tất cả bộ lọc
                </Button>
              </Card>
            </Col>

            {/* Products Grid */}
            <Col xs={24} lg={18}>
              <Row gutter={[16, 16]} className="products-grid">
                {paginatedProducts.map((product) => (
                  <Col xs={24} sm={12} md={8} xl={6} key={product.id}>
                    <ProductCard product={product} />
                  </Col>
                ))}
              </Row>

              {/* Pagination */}
              {filteredProducts.length > pageSize && (
                <div className="pagination-container">
                  <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={filteredProducts.length}
                    onChange={setCurrentPage}
                    showSizeChanger={false}
                    showQuickJumper
                    showTotal={(total, range) => 
                      `${range[0]}-${range[1]} của ${total} sản phẩm`
                    }
                  />
                </div>
              )}
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  );
};

export default ProductPage;

