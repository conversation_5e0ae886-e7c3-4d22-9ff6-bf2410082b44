.product-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-header {
    margin-bottom: 2rem;
    
    h2 {
      margin-bottom: 0.5rem;
    }
  }

  .search-sort-section {
    margin-bottom: 2rem;
  }

  .filters-card {
    margin-bottom: 1rem;
    border-radius: 8px;

    .filter-section {
      margin-bottom: 1.5rem;
      
      h5 {
        margin-bottom: 0.75rem;
        color: #1890ff;
      }

      .ant-checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .ant-checkbox-wrapper {
          margin: 0;
        }
      }

      .price-range-text {
        display: flex;
        justify-content: space-between;
        margin-top: 0.5rem;
        font-size: 0.9rem;
      }
    }
  }

  .products-grid {
    margin-bottom: 2rem;
  }

  .product-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }

    .product-image-container {
      position: relative;
      height: 200px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .discount-tag,
      .new-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        font-weight: bold;
        border: none;
        border-radius: 4px;
      }

      .product-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .product-actions {
        opacity: 1;
      }
    }

    .ant-card-body {
      padding: 1rem;
    }

    .product-name {
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 0.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .product-info {
      .product-rating {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 0.5rem;

        .anticon {
          font-size: 14px;
        }
      }

      .product-price {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .current-price {
          font-size: 1.1rem;
          color: #ff4d4f;
        }

        .original-price {
          font-size: 0.85rem;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .search-sort-section {
      .ant-row {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .filters-card {
      margin-bottom: 1rem;
      
      .filter-section {
        margin-bottom: 1rem;
      }
    }

    .product-card .product-image-container {
      height: 150px;
    }
  }

  @media (max-width: 576px) {
    .products-grid {
      .ant-col {
        flex: 0 0 50%;
        max-width: 50%;
      }
    }
  }
}

