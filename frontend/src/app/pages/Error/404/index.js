import { connect } from 'react-redux';

import './Error404.scss';

function Error404({ ...props }) {
  return (
    <div id="404" className="error-page">
      <h1>404</h1>
      <p><PERSON><PERSON> lỗi, trang bạn tìm kiếm không tồn tại.</p>
      <a href="/" className="home-link">Quay lại trang chủ</a>
    </div>
  );
}

function mapStateToProps(store) {
  return {};
}

export default connect(mapStateToProps)(Error404);
