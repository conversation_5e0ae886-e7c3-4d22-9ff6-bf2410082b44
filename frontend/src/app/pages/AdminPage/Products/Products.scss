.admin-products {
  .products-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        margin: 0;
        color: #1890ff;
      }

      .ant-btn {
        height: 40px;
        font-weight: 600;
      }
    }

    .filter-section {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-card-body {
        padding: 16px;
      }
    }

    .products-table {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-table {
        .ant-table-thead th {
          background: #fafafa;
          font-weight: 600;
          border-bottom: 2px solid #f0f0f0;
        }

        .ant-table-tbody tr {
          transition: background-color 0.3s ease;

          &:hover {
            background: #f6ffed;
          }
        }
      }
    }
  }

  .product-modal {
    .ant-modal-body {
      padding: 24px;
    }

    .form-row {
      display: flex;
      gap: 16px;

      .ant-form-item {
        margin-bottom: 16px;
      }
    }

    .ant-upload-wrapper {
      .ant-upload-list {
        .ant-upload-list-item {
          border-radius: 8px;
        }
      }

      .ant-upload-select {
        border-radius: 8px;
        border: 2px dashed #d9d9d9;
        transition: border-color 0.3s ease;

        &:hover {
          border-color: #1890ff;
        }
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .products-container {
      padding: 16px;

      .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .ant-btn {
          width: 100%;
        }
      }

      .filter-section {
        .ant-space {
          flex-direction: column;
          width: 100%;

          .ant-space-item {
            width: 100%;

            .ant-input-search,
            .ant-select {
              width: 100% !important;
            }
          }
        }
      }

      .products-table {
        .ant-table {
          font-size: 12px;

          .ant-table-thead th,
          .ant-table-tbody td {
            padding: 8px 4px;
          }
        }
      }
    }

    .product-modal {
      margin: 0;
      max-width: 100vw;
      width: 100vw !important;

      .ant-modal-content {
        border-radius: 0;
      }

      .form-row {
        flex-direction: column;
        gap: 0;
      }
    }
  }

  @media (max-width: 576px) {
    .products-container {
      .products-table {
        .ant-table-wrapper {
          overflow-x: auto;
        }
      }
    }
  }
}
