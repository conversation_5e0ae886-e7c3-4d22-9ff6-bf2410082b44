import React, { useState, useEffect } from 'react';
import './Products.scss';
import { Layout, Typography, Card, Table, Button, Space, Tag, Modal, message, Input, Select, Form, Upload, InputNumber } from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined, 
  UploadOutlined,
  EyeOutlined
} from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const Products = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [form] = Form.useForm();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, []);

  useEffect(() => {
    filterProducts();
  }, [products, searchTerm, selectedCategory]);

  const fetchProducts = async () => {
    setLoading(true);
    // Mock data - sẽ thay thế bằng API calls thực tế
    setTimeout(() => {
      setProducts([
        {
          id: 1,
          name: 'iPhone 15 Pro Max',
          sku: 'IP15PM256',
          price: 29990000,
          originalPrice: 32990000,
          stock: 25,
          category: 'Điện thoại',
          status: 'active',
          image: '/images/iphone-15-pro.jpg',
          description: 'iPhone 15 Pro Max với chip A17 Pro mạnh mẽ',
          createdAt: '2024-01-15'
        },
        {
          id: 2,
          name: 'Samsung Galaxy S24 Ultra',
          sku: 'SGS24U512',
          price: 27490000,
          originalPrice: 29990000,
          stock: 18,
          category: 'Điện thoại',
          status: 'active',
          image: '/images/samsung-s24.jpg',
          description: 'Samsung Galaxy S24 Ultra với camera 200MP',
          createdAt: '2024-01-12'
        },
        {
          id: 3,
          name: 'MacBook Air M3',
          sku: 'MBA13M3',
          price: 31990000,
          originalPrice: 34990000,
          stock: 8,
          category: 'Laptop',
          status: 'active',
          image: '/images/macbook-air-m3.jpg',
          description: 'MacBook Air 13 inch với chip M3 mới nhất',
          createdAt: '2024-01-10'
        },
        {
          id: 4,
          name: 'AirPods Pro 3',
          sku: 'APP3',
          price: 6290000,
          stock: 0,
          category: 'Phụ kiện',
          status: 'inactive',
          image: '/images/airpods-pro-3.jpg',
          description: 'AirPods Pro thế hệ 3 với tính năng chống ồn tiên tiến',
          createdAt: '2024-01-08'
        }
      ]);
      setLoading(false);
    }, 1000);
  };

  const fetchCategories = () => {
    setCategories(['Điện thoại', 'Laptop', 'Tablet', 'Phụ kiện', 'Đồng hồ', 'TV']);
  };

  const filterProducts = () => {
    let filtered = products;

    if (searchTerm) {
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    setFilteredProducts(filtered);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingProduct(record);
    form.setFieldsValue({
      ...record,
      image: record.image ? [{ url: record.image, name: 'image.jpg' }] : []
    });
    setModalVisible(true);
  };

  const handleDelete = (record) => {
    Modal.confirm({
      title: 'Xác nhận xóa sản phẩm',
      content: `Bạn có chắc chắn muốn xóa sản phẩm "${record.name}"?`,
      onOk: () => {
        setProducts(products.filter(p => p.id !== record.id));
        message.success('Đã xóa sản phẩm thành công');
      }
    });
  };

  const handleSave = (values) => {
    const productData = {
      ...values,
      id: editingProduct ? editingProduct.id : Date.now(),
      image: values.image?.[0]?.url || '/images/placeholder.jpg',
      createdAt: editingProduct ? editingProduct.createdAt : new Date().toISOString().split('T')[0]
    };

    if (editingProduct) {
      setProducts(products.map(p => p.id === editingProduct.id ? productData : p));
      message.success('Đã cập nhật sản phẩm thành công');
    } else {
      setProducts([...products, productData]);
      message.success('Đã thêm sản phẩm thành công');
    }

    setModalVisible(false);
    form.resetFields();
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'green' : 'red';
  };

  const getStatusText = (status) => {
    return status === 'active' ? 'Đang bán' : 'Ngừng bán';
  };

  const getStockStatus = (stock) => {
    if (stock === 0) return { color: 'red', text: 'Hết hàng' };
    if (stock < 10) return { color: 'orange', text: 'Sắp hết' };
    return { color: 'green', text: 'Còn hàng' };
  };

  const columns = [
    {
      title: 'Hình ảnh',
      dataIndex: 'image',
      key: 'image',
      width: 80,
      render: (image, record) => (
        <img 
          src={image || '/images/placeholder.jpg'} 
          alt={record.name}
          style={{ width: 50, height: 50, objectFit: 'cover', borderRadius: 4 }}
        />
      )
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>SKU: {record.sku}</Text>
        </div>
      )
    },
    {
      title: 'Danh mục',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: 'Giá bán',
      dataIndex: 'price',
      key: 'price',
      render: (price, record) => (
        <div>
          <Text strong style={{ color: '#1890ff' }}>{formatPrice(price)}</Text>
          {record.originalPrice && (
            <>
              <br />
              <Text delete type="secondary" style={{ fontSize: '12px' }}>
                {formatPrice(record.originalPrice)}
              </Text>
            </>
          )}
        </div>
      )
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
      render: (stock) => {
        const stockStatus = getStockStatus(stock);
        return <Tag color={stockStatus.color}>{stock} - {stockStatus.text}</Tag>;
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('vi-VN')
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button 
            icon={<EyeOutlined />} 
            size="small" 
            title="Xem chi tiết"
          />
          <Button 
            icon={<EditOutlined />} 
            size="small" 
            onClick={() => handleEdit(record)}
            title="Chỉnh sửa"
          />
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            onClick={() => handleDelete(record)}
            title="Xóa"
          />
        </Space>
      )
    }
  ];

  return (
    <Layout className="admin-products">
      <Content>
        <div className="products-container">
          <div className="page-header">
            <Title level={2}>Quản lý sản phẩm</Title>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              Thêm sản phẩm
            </Button>
          </div>

          <Card className="filter-section">
            <Space size="middle" wrap>
              <Search
                placeholder="Tìm kiếm sản phẩm..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: 300 }}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="Chọn danh mục"
                value={selectedCategory}
                onChange={setSelectedCategory}
                style={{ width: 200 }}
                allowClear
              >
                {categories.map(category => (
                  <Option key={category} value={category}>{category}</Option>
                ))}
              </Select>
            </Space>
          </Card>

          <Card className="products-table">
            <Table
              dataSource={filteredProducts}
              columns={columns}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} của ${total} sản phẩm`
              }}
            />
          </Card>

          {/* Product Modal */}
          <Modal
            title={editingProduct ? 'Chỉnh sửa sản phẩm' : 'Thêm sản phẩm mới'}
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            onOk={() => form.submit()}
            width={800}
            className="product-modal"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
            >
              <div className="form-row">
                <Form.Item
                  name="name"
                  label="Tên sản phẩm"
                  rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
                  style={{ flex: 2, marginRight: 16 }}
                >
                  <Input placeholder="Nhập tên sản phẩm" />
                </Form.Item>
                
                <Form.Item
                  name="sku"
                  label="Mã SKU"
                  rules={[{ required: true, message: 'Vui lòng nhập mã SKU' }]}
                  style={{ flex: 1 }}
                >
                  <Input placeholder="Nhập mã SKU" />
                </Form.Item>
              </div>

              <div className="form-row">
                <Form.Item
                  name="category"
                  label="Danh mục"
                  rules={[{ required: true, message: 'Vui lòng chọn danh mục' }]}
                  style={{ flex: 1, marginRight: 16 }}
                >
                  <Select placeholder="Chọn danh mục">
                    {categories.map(category => (
                      <Option key={category} value={category}>{category}</Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item
                  name="status"
                  label="Trạng thái"
                  rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
                  style={{ flex: 1 }}
                >
                  <Select placeholder="Chọn trạng thái">
                    <Option value="active">Đang bán</Option>
                    <Option value="inactive">Ngừng bán</Option>
                  </Select>
                </Form.Item>
              </div>

              <div className="form-row">
                <Form.Item
                  name="price"
                  label="Giá bán"
                  rules={[{ required: true, message: 'Vui lòng nhập giá bán' }]}
                  style={{ flex: 1, marginRight: 16 }}
                >
                  <InputNumber
                    placeholder="Nhập giá bán"
                    style={{ width: '100%' }}
                    formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value.replace(/\$\s?|(,*)/g, '')}
                  />
                </Form.Item>
                
                <Form.Item
                  name="originalPrice"
                  label="Giá gốc"
                  style={{ flex: 1, marginRight: 16 }}
                >
                  <InputNumber
                    placeholder="Nhập giá gốc"
                    style={{ width: '100%' }}
                    formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value.replace(/\$\s?|(,*)/g, '')}
                  />
                </Form.Item>
                
                <Form.Item
                  name="stock"
                  label="Tồn kho"
                  rules={[{ required: true, message: 'Vui lòng nhập số lượng tồn kho' }]}
                  style={{ flex: 1 }}
                >
                  <InputNumber
                    placeholder="Nhập số lượng"
                    style={{ width: '100%' }}
                    min={0}
                  />
                </Form.Item>
              </div>

              <Form.Item
                name="description"
                label="Mô tả sản phẩm"
              >
                <Input.TextArea 
                  placeholder="Nhập mô tả sản phẩm"
                  rows={3}
                />
              </Form.Item>

              <Form.Item
                name="image"
                label="Hình ảnh sản phẩm"
              >
                <Upload
                  listType="picture-card"
                  maxCount={1}
                  beforeUpload={() => false}
                >
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>Tải lên</div>
                  </div>
                </Upload>
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </Content>
    </Layout>
  );
};

export default Products;
