.form-setting {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  font-family: Segoe UI, serif;

  .form-setting-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .form-setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .settings-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .settings-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  .settings-form {
    .ant-form-item {
      margin-bottom: 24px;

      .ant-form-item-label > label {
        display: flex;
        align-items: center;

        .info-icon {
          margin-left: 6px;
          color: var(--typo-colours-secondary-grey);
          font-size: 14px;
        }
      }
    }

    .settings-card {
      border-radius: 8px;
      box-shadow: var(--shadow-level-1);
      margin-bottom: 24px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--shadow-level-2);
      }

      .ant-card-head {
        border-bottom: 1px solid var(--background-light-background-grey);

        .ant-card-head-title {
          font-size: 16px;
          color: var(--typo-colours-primary-black);
        }
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .settings-actions {
      display: flex;
      justify-content: center;
      margin-top: 32px;

      .settings-save-button {
        min-width: 120px;
        height: 40px;
        font-weight: 600;
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .settings-card {
      .ant-card-body {
        padding: 16px;
      }
    }
  }
}