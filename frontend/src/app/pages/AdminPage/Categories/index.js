import React, { useState, useEffect } from 'react';
import './Categories.scss';
import { Layout, Typography, Card, Table, Button, Space, Modal, message, Input, Form, Switch, TreeSelect, InputNumber } from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined,
  FolderOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;

const Categories = () => {
  const [categories, setCategories] = useState([]);
  const [filteredCategories, setFilteredCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    filterCategories();
  }, [categories, searchTerm]);

  const fetchCategories = async () => {
    setLoading(true);
    // Mock data - sẽ thay thế bằng API calls thực tế
    setTimeout(() => {
      setCategories([
        {
          id: 1,
          name: 'Điện thoại',
          slug: 'dien-thoai',
          description: 'Các sản phẩm điện thoại thông minh',
          parentId: null,
          isActive: true,
          order: 1,
          productCount: 45,
          createdAt: '2024-01-15'
        },
        {
          id: 2,
          name: 'iPhone',
          slug: 'iphone',
          description: 'Điện thoại iPhone của Apple',
          parentId: 1,
          isActive: true,
          order: 1,
          productCount: 15,
          createdAt: '2024-01-15'
        },
        {
          id: 3,
          name: 'Samsung',
          slug: 'samsung',
          description: 'Điện thoại Samsung Galaxy',
          parentId: 1,
          isActive: true,
          order: 2,
          productCount: 18,
          createdAt: '2024-01-15'
        },
        {
          id: 4,
          name: 'Laptop',
          slug: 'laptop',
          description: 'Máy tính xách tay',
          parentId: null,
          isActive: true,
          order: 2,
          productCount: 32,
          createdAt: '2024-01-12'
        },
        {
          id: 5,
          name: 'MacBook',
          slug: 'macbook',
          description: 'Laptop MacBook của Apple',
          parentId: 4,
          isActive: true,
          order: 1,
          productCount: 12,
          createdAt: '2024-01-12'
        },
        {
          id: 6,
          name: 'Phụ kiện',
          slug: 'phu-kien',
          description: 'Phụ kiện công nghệ',
          parentId: null,
          isActive: true,
          order: 3,
          productCount: 67,
          createdAt: '2024-01-10'
        },
        {
          id: 7,
          name: 'Tai nghe',
          slug: 'tai-nghe',
          description: 'Tai nghe và âm thanh',
          parentId: 6,
          isActive: false,
          order: 1,
          productCount: 23,
          createdAt: '2024-01-10'
        }
      ]);
      setLoading(false);
    }, 1000);
  };

  const filterCategories = () => {
    let filtered = categories;

    if (searchTerm) {
      filtered = filtered.filter(category => 
        category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.slug.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredCategories(filtered);
  };

  const buildCategoryTree = (categories, parentId = null) => {
    return categories
      .filter(cat => cat.parentId === parentId)
      .map(cat => ({
        value: cat.id,
        title: cat.name,
        children: buildCategoryTree(categories, cat.id)
      }));
  };

  const getParentCategoryName = (parentId) => {
    if (!parentId) return 'Danh mục gốc';
    const parent = categories.find(cat => cat.id === parentId);
    return parent ? parent.name : 'Không xác định';
  };

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingCategory(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = (record) => {
    // Kiểm tra xem có danh mục con không
    const hasChildren = categories.some(cat => cat.parentId === record.id);
    if (hasChildren) {
      message.error('Không thể xóa danh mục có danh mục con. Vui lòng xóa danh mục con trước.');
      return;
    }

    // Kiểm tra xem có sản phẩm không
    if (record.productCount > 0) {
      message.error('Không thể xóa danh mục có sản phẩm. Vui lòng di chuyển sản phẩm trước.');
      return;
    }

    Modal.confirm({
      title: 'Xác nhận xóa danh mục',
      content: `Bạn có chắc chắn muốn xóa danh mục "${record.name}"?`,
      onOk: () => {
        setCategories(categories.filter(cat => cat.id !== record.id));
        message.success('Đã xóa danh mục thành công');
      }
    });
  };

  const handleSave = (values) => {
    const categoryData = {
      ...values,
      id: editingCategory ? editingCategory.id : Date.now(),
      slug: values.name.toLowerCase()
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
        .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
        .replace(/[ìíịỉĩ]/g, 'i')
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
        .replace(/[ùúụủũưừứựửữ]/g, 'u')
        .replace(/[ỳýỵỷỹ]/g, 'y')
        .replace(/đ/g, 'd')
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, ''),
      productCount: editingCategory ? editingCategory.productCount : 0,
      createdAt: editingCategory ? editingCategory.createdAt : new Date().toISOString().split('T')[0]
    };

    if (editingCategory) {
      setCategories(categories.map(cat => cat.id === editingCategory.id ? categoryData : cat));
      message.success('Đã cập nhật danh mục thành công');
    } else {
      setCategories([...categories, categoryData]);
      message.success('Đã thêm danh mục thành công');
    }

    setModalVisible(false);
    form.resetFields();
  };

  const handleToggleStatus = (record) => {
    setCategories(categories.map(cat => 
      cat.id === record.id 
        ? { ...cat, isActive: !cat.isActive }
        : cat
    ));
    message.success(`Đã ${record.isActive ? 'tắt' : 'bật'} danh mục`);
  };

  const columns = [
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {record.parentId ? <FolderOutlined /> : <FolderOpenOutlined />}
          <div>
            <Text strong style={{ color: record.parentId ? '#1890ff' : '#262626' }}>
              {text}
            </Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              /{record.slug}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Danh mục cha',
      dataIndex: 'parentId',
      key: 'parentId',
      render: (parentId) => (
        <Text type="secondary">{getParentCategoryName(parentId)}</Text>
      )
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      render: (text) => (
        <Text style={{ maxWidth: 200, display: 'block' }} ellipsis={{ tooltip: text }}>
          {text}
        </Text>
      )
    },
    {
      title: 'Số sản phẩm',
      dataIndex: 'productCount',
      key: 'productCount',
      render: (count) => (
        <Text style={{ color: '#1890ff', fontWeight: 600 }}>{count}</Text>
      ),
      sorter: (a, b) => a.productCount - b.productCount
    },
    {
      title: 'Thứ tự',
      dataIndex: 'order',
      key: 'order',
      width: 80,
      sorter: (a, b) => a.order - b.order
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="Bật"
          unCheckedChildren="Tắt"
        />
      )
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
    },
    {
      title: 'Hành động',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button 
            icon={<EditOutlined />} 
            size="small" 
            onClick={() => handleEdit(record)}
            title="Chỉnh sửa"
          />
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            onClick={() => handleDelete(record)}
            title="Xóa"
            disabled={record.productCount > 0}
          />
        </Space>
      )
    }
  ];

  const categoryTreeData = buildCategoryTree(categories.filter(cat => cat.id !== editingCategory?.id));

  return (
    <Layout className="admin-categories">
      <Content>
        <div className="categories-container">
          <div className="page-header">
            <Title level={2}>Quản lý danh mục</Title>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              Thêm danh mục
            </Button>
          </div>

          <Card className="filter-section">
            <Search
              placeholder="Tìm kiếm danh mục..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: 400 }}
              enterButton={<SearchOutlined />}
            />
          </Card>

          <Card className="categories-table">
            <Table
              dataSource={filteredCategories}
              columns={columns}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} của ${total} danh mục`
              }}
            />
          </Card>

          {/* Category Modal */}
          <Modal
            title={editingCategory ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới'}
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            onOk={() => form.submit()}
            width={600}
            className="category-modal"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
            >
              <Form.Item
                name="name"
                label="Tên danh mục"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên danh mục' },
                  { min: 2, message: 'Tên danh mục phải có ít nhất 2 ký tự' }
                ]}
              >
                <Input placeholder="Nhập tên danh mục" />
              </Form.Item>

              <Form.Item
                name="parentId"
                label="Danh mục cha"
              >
                <TreeSelect
                  treeData={categoryTreeData}
                  placeholder="Chọn danh mục cha (để trống nếu là danh mục gốc)"
                  allowClear
                  showSearch
                  treeDefaultExpandAll
                />
              </Form.Item>

              <Form.Item
                name="description"
                label="Mô tả"
                rules={[
                  { max: 255, message: 'Mô tả không được vượt quá 255 ký tự' }
                ]}
              >
                <Input.TextArea 
                  placeholder="Nhập mô tả cho danh mục"
                  rows={3}
                />
              </Form.Item>

              <div style={{ display: 'flex', gap: 16 }}>
                <Form.Item
                  name="order"
                  label="Thứ tự hiển thị"
                  style={{ flex: 1 }}
                  rules={[{ required: true, message: 'Vui lòng nhập thứ tự' }]}
                >
                  <InputNumber
                    placeholder="Thứ tự"
                    style={{ width: '100%' }}
                    min={1}
                  />
                </Form.Item>

                <Form.Item
                  name="isActive"
                  label="Trạng thái"
                  valuePropName="checked"
                  style={{ flex: 1 }}
                >
                  <Switch checkedChildren="Bật" unCheckedChildren="Tắt" />
                </Form.Item>
              </div>
            </Form>
          </Modal>
        </div>
      </Content>
    </Layout>
  );
};

export default Categories;
