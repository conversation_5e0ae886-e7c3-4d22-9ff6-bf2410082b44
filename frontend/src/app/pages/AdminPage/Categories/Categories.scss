.admin-categories {
  .categories-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        margin: 0;
        color: #1890ff;
      }

      .ant-btn {
        height: 40px;
        font-weight: 600;
      }
    }

    .filter-section {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-card-body {
        padding: 16px;
      }
    }

    .categories-table {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-table {
        .ant-table-thead th {
          background: #fafafa;
          font-weight: 600;
          border-bottom: 2px solid #f0f0f0;
        }

        .ant-table-tbody tr {
          transition: background-color 0.3s ease;

          &:hover {
            background: #f6ffed;
          }
        }
      }
    }
  }

  .category-modal {
    .ant-modal-body {
      padding: 24px;
    }

    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-tree-select {
      .ant-select-selector {
        border-radius: 6px;
      }
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover,
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .ant-switch {
      &.ant-switch-checked {
        background-color: #52c41a;
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .categories-container {
      padding: 16px;

      .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .ant-btn {
          width: 100%;
        }
      }

      .filter-section {
        .ant-input-search {
          width: 100% !important;
        }
      }

      .categories-table {
        .ant-table {
          font-size: 12px;

          .ant-table-thead th,
          .ant-table-tbody td {
            padding: 8px 4px;
          }

          .ant-table-tbody td {
            .ant-typography {
              font-size: 12px;
            }
          }
        }
      }
    }

    .category-modal {
      margin: 0;
      max-width: 100vw;
      width: 100vw !important;

      .ant-modal-content {
        border-radius: 0;
      }

      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  @media (max-width: 576px) {
    .categories-container {
      .categories-table {
        .ant-table-wrapper {
          overflow-x: auto;

          .ant-table {
            min-width: 800px;
          }
        }
      }
    }

    .category-modal {
      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }

        .ant-row {
          flex-direction: column;
          gap: 0;
        }
      }
    }
  }
}
