import React, { useState, useEffect } from 'react';
import './Orders.scss';
import { Layout, Typography, Card, Table, Button, Space, Tag, Modal, message, Input, Select, DatePicker, Descriptions } from 'antd';
import { 
  EyeOutlined, 
  EditOutlined, 
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  PrinterOutlined
} from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateRange, setDateRange] = useState([]);

  useEffect(() => {
    fetchOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [orders, searchTerm, statusFilter, dateRange]);

  const fetchOrders = async () => {
    setLoading(true);
    // Mock data - sẽ thay thế bằng API calls thực tế
    setTimeout(() => {
      setOrders([
        {
          id: 'DH001',
          customer: {
            name: 'Nguyễn Văn A',
            email: '<EMAIL>',
            phone: '0123456789'
          },
          items: [
            {
              id: 1,
              name: 'iPhone 15 Pro Max',
              price: 29990000,
              quantity: 1,
              variant: '256GB - Natural Titanium'
            },
            {
              id: 2,
              name: 'AirPods Pro 3',
              price: 6290000,
              quantity: 1,
              variant: 'Trắng'
            }
          ],
          total: 36280000,
          status: 'delivered',
          paymentMethod: 'cod',
          paymentStatus: 'paid',
          shippingAddress: 'Số 123 Đường ABC, Phường XYZ, Quận 1, TP.HCM',
          trackingNumber: 'TN123456789',
          orderDate: '2024-01-15 10:30',
          deliveredDate: '2024-01-18 14:20',
          notes: 'Giao hàng trong giờ hành chính'
        },
        {
          id: 'DH002',
          customer: {
            name: 'Trần Thị B',
            email: '<EMAIL>',
            phone: '**********'
          },
          items: [
            {
              id: 3,
              name: 'MacBook Air M3',
              price: ********,
              quantity: 1,
              variant: '13-inch - 8GB RAM - 256GB SSD'
            }
          ],
          total: ********,
          status: 'shipping',
          paymentMethod: 'bank_transfer',
          paymentStatus: 'paid',
          shippingAddress: 'Số 456 Đường DEF, Phường ABC, Quận 2, TP.HCM',
          trackingNumber: 'TN987654321',
          orderDate: '2024-01-20 09:15',
          notes: ''
        },
        {
          id: 'DH003',
          customer: {
            name: 'Lê Văn C',
            email: '<EMAIL>',
            phone: '**********'
          },
          items: [
            {
              id: 4,
              name: 'Samsung Galaxy S24 Ultra',
              price: ********,
              quantity: 1,
              variant: '512GB - Titanium Black'
            }
          ],
          total: ********,
          status: 'processing',
          paymentMethod: 'credit_card',
          paymentStatus: 'pending',
          shippingAddress: 'Số 789 Đường GHI, Phường DEF, Quận 3, TP.HCM',
          orderDate: '2024-01-22 16:45',
          notes: 'Khách hàng yêu cầu gọi trước khi giao'
        }
      ]);
      setLoading(false);
    }, 1000);
  };

  const filterOrders = () => {
    let filtered = orders;

    if (searchTerm) {
      filtered = filtered.filter(order => 
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    if (dateRange && dateRange.length === 2) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.orderDate);
        return orderDate >= dateRange[0].toDate() && orderDate <= dateRange[1].toDate();
      });
    }

    setFilteredOrders(filtered);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'orange',
      'processing': 'blue',
      'shipping': 'cyan',
      'delivered': 'green',
      'cancelled': 'red',
      'returned': 'purple'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': 'Chờ xử lý',
      'processing': 'Đang xử lý',
      'shipping': 'Đang giao hàng',
      'delivered': 'Đã giao hàng',
      'cancelled': 'Đã hủy',
      'returned': 'Đã trả hàng'
    };
    return texts[status] || status;
  };

  const getPaymentStatusColor = (status) => {
    return status === 'paid' ? 'green' : 'orange';
  };

  const getPaymentStatusText = (status) => {
    return status === 'paid' ? 'Đã thanh toán' : 'Chưa thanh toán';
  };

  const getPaymentMethodText = (method) => {
    const texts = {
      'cod': 'COD',
      'bank_transfer': 'Chuyển khoản',
      'credit_card': 'Thẻ tín dụng'
    };
    return texts[method] || method;
  };

  const handleViewDetail = (record) => {
    setSelectedOrder(record);
    setDetailModalVisible(true);
  };

  const handleUpdateStatus = (orderId, newStatus) => {
    setOrders(orders.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus }
        : order
    ));
    message.success('Đã cập nhật trạng thái đơn hàng');
  };

  const handleExport = () => {
    message.info('Tính năng xuất báo cáo đang được phát triển');
  };

  const handlePrint = (order) => {
    message.info('Tính năng in đơn hàng đang được phát triển');
  };

  const columns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <Text strong style={{ color: '#1890ff' }}>{text}</Text>,
      width: 120
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      render: (customer) => (
        <div>
          <Text strong>{customer.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>{customer.email}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>{customer.phone}</Text>
        </div>
      ),
      width: 200
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total',
      key: 'total',
      render: (total) => <Text strong style={{ color: '#52c41a' }}>{formatPrice(total)}</Text>,
      width: 120
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Select
          value={status}
          onChange={(value) => handleUpdateStatus(record.id, value)}
          style={{ width: '100%' }}
          size="small"
        >
          <Option value="pending">
            <Tag color="orange">Chờ xử lý</Tag>
          </Option>
          <Option value="processing">
            <Tag color="blue">Đang xử lý</Tag>
          </Option>
          <Option value="shipping">
            <Tag color="cyan">Đang giao hàng</Tag>
          </Option>
          <Option value="delivered">
            <Tag color="green">Đã giao hàng</Tag>
          </Option>
          <Option value="cancelled">
            <Tag color="red">Đã hủy</Tag>
          </Option>
        </Select>
      ),
      width: 140
    },
    {
      title: 'Thanh toán',
      key: 'payment',
      render: (_, record) => (
        <div>
          <Tag color={getPaymentStatusColor(record.paymentStatus)}>
            {getPaymentStatusText(record.paymentStatus)}
          </Tag>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {getPaymentMethodText(record.paymentMethod)}
          </Text>
        </div>
      ),
      width: 120
    },
    {
      title: 'Ngày đặt',
      dataIndex: 'orderDate',
      key: 'orderDate',
      render: (date) => formatDate(date),
      width: 140
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewDetail(record)}
            title="Xem chi tiết"
          />
          <Button 
            icon={<PrinterOutlined />} 
            size="small"
            onClick={() => handlePrint(record)}
            title="In đơn hàng"
          />
        </Space>
      ),
      width: 100
    }
  ];

  return (
    <Layout className="admin-orders">
      <Content>
        <div className="orders-container">
          <div className="page-header">
            <Title level={2}>Quản lý đơn hàng</Title>
            <Button 
              icon={<ExportOutlined />}
              onClick={handleExport}
            >
              Xuất báo cáo
            </Button>
          </div>

          <Card className="filter-section">
            <Space size="middle" wrap>
              <Search
                placeholder="Tìm kiếm đơn hàng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: 300 }}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="Lọc theo trạng thái"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 150 }}
                allowClear
              >
                <Option value="pending">Chờ xử lý</Option>
                <Option value="processing">Đang xử lý</Option>
                <Option value="shipping">Đang giao hàng</Option>
                <Option value="delivered">Đã giao hàng</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
              <RangePicker
                placeholder={['Từ ngày', 'Đến ngày']}
                value={dateRange}
                onChange={setDateRange}
                style={{ width: 250 }}
              />
            </Space>
          </Card>

          <Card className="orders-table">
            <Table
              dataSource={filteredOrders}
              columns={columns}
              rowKey="id"
              loading={loading}
              scroll={{ x: 1000 }}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} của ${total} đơn hàng`
              }}
            />
          </Card>

          {/* Order Detail Modal */}
          <Modal
            title={`Chi tiết đơn hàng ${selectedOrder?.id}`}
            open={detailModalVisible}
            onCancel={() => setDetailModalVisible(false)}
            footer={[
              <Button key="print" icon={<PrinterOutlined />} onClick={() => handlePrint(selectedOrder)}>
                In đơn hàng
              </Button>,
              <Button key="close" onClick={() => setDetailModalVisible(false)}>
                Đóng
              </Button>
            ]}
            width={800}
            className="order-detail-modal"
          >
            {selectedOrder && (
              <div className="order-detail-content">
                <Descriptions bordered column={2} size="small">
                  <Descriptions.Item label="Mã đơn hàng" span={1}>
                    <Text strong>{selectedOrder.id}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="Ngày đặt hàng" span={1}>
                    {formatDate(selectedOrder.orderDate)}
                  </Descriptions.Item>
                  <Descriptions.Item label="Khách hàng" span={1}>
                    <div>
                      <Text strong>{selectedOrder.customer.name}</Text>
                      <br />
                      <Text type="secondary">{selectedOrder.customer.email}</Text>
                      <br />
                      <Text type="secondary">{selectedOrder.customer.phone}</Text>
                    </div>
                  </Descriptions.Item>
                  <Descriptions.Item label="Trạng thái" span={1}>
                    <Tag color={getStatusColor(selectedOrder.status)}>
                      {getStatusText(selectedOrder.status)}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="Thanh toán" span={1}>
                    <div>
                      <Tag color={getPaymentStatusColor(selectedOrder.paymentStatus)}>
                        {getPaymentStatusText(selectedOrder.paymentStatus)}
                      </Tag>
                      <br />
                      <Text type="secondary">{getPaymentMethodText(selectedOrder.paymentMethod)}</Text>
                    </div>
                  </Descriptions.Item>
                  <Descriptions.Item label="Mã vận đơn" span={1}>
                    {selectedOrder.trackingNumber || 'Chưa có'}
                  </Descriptions.Item>
                  <Descriptions.Item label="Địa chỉ giao hàng" span={2}>
                    {selectedOrder.shippingAddress}
                  </Descriptions.Item>
                  {selectedOrder.notes && (
                    <Descriptions.Item label="Ghi chú" span={2}>
                      {selectedOrder.notes}
                    </Descriptions.Item>
                  )}
                </Descriptions>

                <div className="order-items-section">
                  <Title level={4}>Sản phẩm đã đặt</Title>
                  <Table
                    dataSource={selectedOrder.items}
                    pagination={false}
                    size="small"
                    columns={[
                      {
                        title: 'Sản phẩm',
                        dataIndex: 'name',
                        key: 'name',
                        render: (name, record) => (
                          <div>
                            <Text strong>{name}</Text>
                            {record.variant && (
                              <>
                                <br />
                                <Text type="secondary" style={{ fontSize: '12px' }}>{record.variant}</Text>
                              </>
                            )}
                          </div>
                        )
                      },
                      {
                        title: 'Đơn giá',
                        dataIndex: 'price',
                        key: 'price',
                        render: (price) => formatPrice(price)
                      },
                      {
                        title: 'Số lượng',
                        dataIndex: 'quantity',
                        key: 'quantity'
                      },
                      {
                        title: 'Thành tiền',
                        key: 'total',
                        render: (_, record) => (
                          <Text strong>{formatPrice(record.price * record.quantity)}</Text>
                        )
                      }
                    ]}
                  />
                  
                  <div className="order-summary">
                    <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
                      Tổng cộng: {formatPrice(selectedOrder.total)}
                    </Text>
                  </div>
                </div>
              </div>
            )}
          </Modal>
        </div>
      </Content>
    </Layout>
  );
};

export default Orders;
