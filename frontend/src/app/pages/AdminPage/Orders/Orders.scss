.admin-orders {
  .orders-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        margin: 0;
        color: #1890ff;
      }

      .ant-btn {
        height: 40px;
        font-weight: 600;
      }
    }

    .filter-section {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-card-body {
        padding: 16px;
      }
    }

    .orders-table {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .ant-table {
        .ant-table-thead th {
          background: #fafafa;
          font-weight: 600;
          border-bottom: 2px solid #f0f0f0;
        }

        .ant-table-tbody tr {
          transition: background-color 0.3s ease;

          &:hover {
            background: #f6ffed;
          }
        }

        .ant-select {
          .ant-select-selector {
            border: none;
            box-shadow: none;
          }

          &:hover .ant-select-selector {
            border: 1px solid #1890ff;
          }
        }
      }
    }
  }

  .order-detail-modal {
    .order-detail-content {
      .ant-descriptions {
        margin-bottom: 24px;
      }

      .order-items-section {
        h4 {
          margin-bottom: 16px;
          color: #262626;
        }

        .ant-table {
          margin-bottom: 16px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
        }

        .order-summary {
          text-align: right;
          padding: 16px;
          background: #f9f9f9;
          border-radius: 6px;
        }
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .orders-container {
      padding: 16px;

      .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .ant-btn {
          width: 100%;
        }
      }

      .filter-section {
        .ant-space {
          flex-direction: column;
          width: 100%;

          .ant-space-item {
            width: 100%;

            .ant-input-search,
            .ant-select,
            .ant-picker {
              width: 100% !important;
            }
          }
        }
      }

      .orders-table {
        .ant-table-wrapper {
          .ant-table {
            font-size: 12px;

            .ant-table-thead th,
            .ant-table-tbody td {
              padding: 8px 4px;
            }
          }
        }
      }
    }

    .order-detail-modal {
      margin: 0;
      max-width: 100vw;
      width: 100vw !important;

      .ant-modal-content {
        border-radius: 0;
        height: 100vh;
        
        .ant-modal-body {
          height: calc(100vh - 110px);
          overflow-y: auto;
        }
      }

      .order-detail-content {
        .ant-descriptions {
          .ant-descriptions-item-label,
          .ant-descriptions-item-content {
            padding: 8px;
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .orders-container {
      .orders-table {
        .ant-table-wrapper {
          overflow-x: auto;
        }
      }
    }

    .order-detail-modal {
      .order-detail-content {
        .order-items-section {
          .ant-table {
            .ant-table-thead th,
            .ant-table-tbody td {
              padding: 4px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
