.admin-dashboard {
  .dashboard-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;

    h2 {
      margin-bottom: 24px;
      color: #1890ff;
    }
  }

  .stats-section {
    margin-bottom: 24px;

    .stat-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
      }

      .ant-statistic-title {
        font-weight: 600;
        color: #666;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .ant-statistic-content-value {
          font-weight: 700;
          font-size: 1.5rem;
        }

        .ant-statistic-content-prefix {
          font-size: 1.2rem;
          margin-right: 8px;
        }

        .ant-statistic-content-suffix {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }

      &.orders {
        border-left: 4px solid #1890ff;
        
        .ant-statistic-content-prefix {
          color: #1890ff;
        }
      }

      &.revenue {
        border-left: 4px solid #52c41a;
        
        .ant-statistic-content-prefix {
          color: #52c41a;
        }
      }

      &.customers {
        border-left: 4px solid #722ed1;
        
        .ant-statistic-content-prefix {
          color: #722ed1;
        }
      }

      &.products {
        border-left: 4px solid #fa8c16;
        
        .ant-statistic-content-prefix {
          color: #fa8c16;
        }
      }
    }
  }

  .content-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      margin-bottom: 24px;

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-card-head-title {
          font-weight: 600;
          color: #262626;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .recent-orders,
    .top-products {
      .ant-table {
        .ant-table-thead th {
          background: #fafafa;
          font-weight: 600;
          border-bottom: 2px solid #f0f0f0;
        }

        .ant-table-tbody tr {
          transition: background-color 0.3s ease;

          &:hover {
            background: #f6ffed;
          }
        }
      }
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .dashboard-container {
      padding: 16px;
    }

    .stats-section {
      .stat-card {
        margin-bottom: 16px;

        .ant-statistic-content {
          flex-direction: column;
          align-items: flex-start;

          .ant-statistic-content-suffix {
            margin-top: 8px;
          }
        }
      }
    }

    .content-section {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }

  @media (max-width: 576px) {
    .stats-section {
      .ant-col {
        margin-bottom: 8px;
      }
    }

    .content-section {
      .ant-table {
        font-size: 12px;

        .ant-table-thead th,
        .ant-table-tbody td {
          padding: 8px 4px;
        }
      }
    }
  }
}
