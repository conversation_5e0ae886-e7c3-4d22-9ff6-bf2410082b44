import React, { useState, useEffect } from 'react';
import './Dashboard.scss';
import { Layout, Typography, Card, Row, Col, Statistic, Table, Progress, Space, Tag } from 'antd';
import { 
  ShoppingCartOutlined, 
  UserOutlined, 
  DollarCircleOutlined, 
  ProductOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined
} from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({});
  const [recentOrders, setRecentOrders] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    // Mock data - sẽ thay thế bằng API calls thực tế
    setTimeout(() => {
      setStats({
        totalOrders: { value: 1234, change: 12.5, isIncrease: true },
        totalRevenue: { value: 125000000, change: 8.3, isIncrease: true },
        totalCustomers: { value: 567, change: -2.1, isIncrease: false },
        totalProducts: { value: 89, change: 5.7, isIncrease: true }
      });

      setRecentOrders([
        {
          id: 'DH001',
          customer: 'Nguyễn Văn A',
          total: 2990000,
          status: 'delivered',
          orderDate: '2024-01-15 10:30'
        },
        {
          id: 'DH002',
          customer: 'Trần Thị B',
          total: 1500000,
          status: 'shipping',
          orderDate: '2024-01-15 09:15'
        },
        {
          id: 'DH003',
          customer: 'Lê Văn C',
          total: 890000,
          status: 'processing',
          orderDate: '2024-01-15 08:45'
        },
        {
          id: 'DH004',
          customer: 'Phạm Thị D',
          total: 3200000,
          status: 'pending',
          orderDate: '2024-01-15 07:20'
        }
      ]);

      setTopProducts([
        {
          id: 1,
          name: 'iPhone 15 Pro Max',
          sold: 45,
          revenue: 134550000,
          trend: 12.5
        },
        {
          id: 2,
          name: 'MacBook Air M3',
          sold: 23,
          revenue: 73570000,
          trend: 8.3
        },
        {
          id: 3,
          name: 'AirPods Pro 3',
          sold: 67,
          revenue: 42163000,
          trend: -3.2
        },
        {
          id: 4,
          name: 'Samsung Galaxy S24',
          sold: 31,
          revenue: 85190000,
          trend: 15.7
        }
      ]);

      setLoading(false);
    }, 1000);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'orange',
      'processing': 'blue',
      'shipping': 'cyan',
      'delivered': 'green',
      'cancelled': 'red'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': 'Chờ xử lý',
      'processing': 'Đang xử lý',
      'shipping': 'Đang giao hàng',
      'delivered': 'Đã giao hàng',
      'cancelled': 'Đã hủy'
    };
    return texts[status] || status;
  };

  const orderColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer'
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total',
      key: 'total',
      render: (total) => <Text strong style={{ color: '#1890ff' }}>{formatPrice(total)}</Text>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Thời gian',
      dataIndex: 'orderDate',
      key: 'orderDate'
    }
  ];

  const productColumns = [
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Đã bán',
      dataIndex: 'sold',
      key: 'sold',
      render: (sold) => <Text>{sold} sản phẩm</Text>
    },
    {
      title: 'Doanh thu',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue) => <Text strong style={{ color: '#52c41a' }}>{formatPrice(revenue)}</Text>
    },
    {
      title: 'Xu hướng',
      dataIndex: 'trend',
      key: 'trend',
      render: (trend) => (
        <Space>
          {trend > 0 ? (
            <TrendingUpOutlined style={{ color: '#52c41a' }} />
          ) : (
            <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
          )}
          <Text style={{ color: trend > 0 ? '#52c41a' : '#ff4d4f' }}>
            {Math.abs(trend)}%
          </Text>
        </Space>
      )
    }
  ];

  return (
    <Layout className="admin-dashboard">
      <Content>
        <div className="dashboard-container">
          <Title level={2}>Dashboard</Title>
          
          {/* Statistics Cards */}
          <Row gutter={[16, 16]} className="stats-section">
            <Col xs={24} sm={12} md={6}>
              <Card className="stat-card orders">
                <Statistic
                  title="Tổng đơn hàng"
                  value={stats.totalOrders?.value}
                  prefix={<ShoppingCartOutlined />}
                  suffix={
                    <Space>
                      {stats.totalOrders?.isIncrease ? (
                        <TrendingUpOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
                      )}
                      <Text style={{ 
                        color: stats.totalOrders?.isIncrease ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}>
                        {Math.abs(stats.totalOrders?.change || 0)}%
                      </Text>
                    </Space>
                  }
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={6}>
              <Card className="stat-card revenue">
                <Statistic
                  title="Doanh thu"
                  value={stats.totalRevenue?.value}
                  prefix={<DollarCircleOutlined />}
                  formatter={(value) => formatPrice(value)}
                  suffix={
                    <Space>
                      {stats.totalRevenue?.isIncrease ? (
                        <TrendingUpOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
                      )}
                      <Text style={{ 
                        color: stats.totalRevenue?.isIncrease ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}>
                        {Math.abs(stats.totalRevenue?.change || 0)}%
                      </Text>
                    </Space>
                  }
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={6}>
              <Card className="stat-card customers">
                <Statistic
                  title="Khách hàng"
                  value={stats.totalCustomers?.value}
                  prefix={<UserOutlined />}
                  suffix={
                    <Space>
                      {stats.totalCustomers?.isIncrease ? (
                        <TrendingUpOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
                      )}
                      <Text style={{ 
                        color: stats.totalCustomers?.isIncrease ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}>
                        {Math.abs(stats.totalCustomers?.change || 0)}%
                      </Text>
                    </Space>
                  }
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} md={6}>
              <Card className="stat-card products">
                <Statistic
                  title="Sản phẩm"
                  value={stats.totalProducts?.value}
                  prefix={<ProductOutlined />}
                  suffix={
                    <Space>
                      {stats.totalProducts?.isIncrease ? (
                        <TrendingUpOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
                      )}
                      <Text style={{ 
                        color: stats.totalProducts?.isIncrease ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}>
                        {Math.abs(stats.totalProducts?.change || 0)}%
                      </Text>
                    </Space>
                  }
                />
              </Card>
            </Col>
          </Row>

          {/* Charts and Tables */}
          <Row gutter={24} className="content-section">
            <Col xs={24} xl={14}>
              <Card title="Đơn hàng gần đây" className="recent-orders">
                <Table
                  dataSource={recentOrders}
                  columns={orderColumns}
                  rowKey="id"
                  loading={loading}
                  pagination={{ pageSize: 5 }}
                  size="small"
                />
              </Card>
            </Col>
            
            <Col xs={24} xl={10}>
              <Card title="Sản phẩm bán chạy" className="top-products">
                <Table
                  dataSource={topProducts}
                  columns={productColumns}
                  rowKey="id"
                  loading={loading}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  );
};

export default Dashboard;
