// E-commerce Welcome Page Styles
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

// Variables
$primary-color: #1890ff;
$primary-light: #40a9ff;
$primary-dark: #096dd9;
$secondary-color: #52c41a;
$accent-color: #722ed1;
$warning-color: #faad14;
$error-color: #ff4d4f;

$gray-50: #fafafa;
$gray-100: #f5f5f5;
$gray-200: #f0f0f0;
$gray-300: #d9d9d9;
$gray-400: #bfbfbf;
$gray-500: #8c8c8c;
$gray-600: #595959;
$gray-700: #434343;
$gray-800: #262626;
$gray-900: #1f1f1f;

$white: #ffffff;
$text-primary: #262626;
$text-secondary: #595959;
$text-light: #8c8c8c;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

@mixin card-shadow-hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

@mixin transition($property: all, $duration: 0.3s) {
  transition: $property $duration cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin gradient-bg($color1, $color2) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
}

// Base styles
.welcome-page {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: $gray-50;
  
  .ant-layout-content {
    padding: 0;
    overflow-x: hidden;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 16px;
    }
  }

  // Section titles
  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: $text-primary;
    text-align: center;
    margin-bottom: 16px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.125rem;
    color: $text-secondary;
    text-align: center;
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 1rem;
      margin-bottom: 32px;
    }
  }
}

// Hero Section
.hero-section {
  @include gradient-bg($primary-color, $primary-dark);
  color: $white;
  padding: 80px 0;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 60px 0;
  }

  .hero-content {
    margin-bottom: 60px;

    @media (max-width: 768px) {
      margin-bottom: 40px;
    }
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: $white;
    margin-bottom: 24px;
    line-height: 1.1;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }

    .highlight {
      background: linear-gradient(45deg, #ffd700, #ff6b6b);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 900;
    }
  }

  .hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: 1.125rem;
      margin-bottom: 32px;
    }
  }

  .hero-actions {
    margin-bottom: 0;

    .shop-now-btn {
      background: $secondary-color;
      border-color: $secondary-color;
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      @include transition();

      &:hover {
        background: lighten($secondary-color, 10%);
        border-color: lighten($secondary-color, 10%);
        transform: translateY(-2px);
        @include card-shadow-hover();
      }
    }

    .learn-more-btn {
      background: transparent;
      border: 2px solid $white;
      color: $white;
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      @include transition();

      &:hover {
        background: $white;
        color: $primary-color;
        transform: translateY(-2px);
      }
    }

    @media (max-width: 576px) {
      flex-direction: column;
      width: 100%;

      .ant-btn {
        width: 100%;
        margin-bottom: 16px;
      }
    }
  }

  .hero-stats {
    .ant-statistic {
      .ant-statistic-title {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        font-size: 24px;
        font-weight: 700;

        @media (max-width: 768px) {
          font-size: 20px;
        }
      }
    }

    @media (max-width: 576px) {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}

// Categories Section
.categories-section {
  padding: 80px 0;
  background: $white;

  @media (max-width: 768px) {
    padding: 60px 0;
  }

  .category-grid {
    .category-card {
      text-align: center;
      border-radius: 12px;
      @include card-shadow();
      @include transition();
      cursor: pointer;
      padding: 32px 16px;
      height: 120px;
      @include flex-center();
      flex-direction: column;

      &:hover {
        @include card-shadow-hover();
        transform: translateY(-4px);
      }

      .category-icon {
        font-size: 32px;
        margin-bottom: 12px;
        @include transition();

        @media (max-width: 768px) {
          font-size: 28px;
          margin-bottom: 8px;
        }
      }

      .category-text {
        font-size: 14px;
        font-weight: 600;
        color: $text-primary;

        @media (max-width: 768px) {
          font-size: 13px;
        }
      }

      &:hover .category-icon {
        transform: scale(1.1);
      }
    }
  }
}

// Benefits Section
.benefits-section {
  padding: 60px 0;
  background: $gray-50;

  @media (max-width: 768px) {
    padding: 40px 0;
  }

  .benefits-grid {
    .benefit-card {
      text-align: center;
      border-radius: 16px;
      padding: 32px 24px;
      height: 100%;
      @include transition();
      background: $white;
      border: none;

      &:hover {
        @include card-shadow-hover();
        transform: translateY(-4px);
      }

      .benefit-icon {
        font-size: 40px;
        margin-bottom: 20px;
        @include transition();

        @media (max-width: 768px) {
          font-size: 36px;
          margin-bottom: 16px;
        }
      }

      .benefit-title {
        font-size: 1.125rem;
        font-weight: 700;
        color: $text-primary;
        margin-bottom: 12px;

        @media (max-width: 768px) {
          font-size: 1rem;
        }
      }

      .benefit-description {
        font-size: 14px;
        color: $text-secondary;
        line-height: 1.6;
        margin-bottom: 0;
      }

      &:hover .benefit-icon {
        transform: scale(1.1);
      }
    }
  }
}

// Call to Action Section
.cta-section {
  padding: 80px 0;
  background: $white;

  @media (max-width: 768px) {
    padding: 60px 0;
  }

  .cta-card {
    @include gradient-bg($accent-color, darken($accent-color, 10%));
    border-radius: 20px;
    border: none;
    @include card-shadow();

    .ant-card-body {
      padding: 60px 40px;

      @media (max-width: 768px) {
        padding: 40px 24px;
      }
    }

    .cta-content {
      text-align: center;
      color: $white;

      .cta-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: $white;
        margin-bottom: 20px;

        @media (max-width: 768px) {
          font-size: 2rem;
        }
      }

      .cta-description {
        font-size: 1.125rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 40px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;

        @media (max-width: 768px) {
          font-size: 1rem;
          margin-bottom: 32px;
        }
      }

      .cta-actions {
        .ant-btn {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
          font-weight: 600;
          border-radius: 8px;
          @include transition();

          &.ant-btn-primary {
            background: $white;
            border-color: $white;
            color: $accent-color;

            &:hover {
              background: $gray-100;
              border-color: $gray-100;
              transform: translateY(-2px);
              @include card-shadow();
            }
          }

          &:not(.ant-btn-primary) {
            background: transparent;
            border: 2px solid $white;
            color: $white;

            &:hover {
              background: $white;
              color: $accent-color;
              transform: translateY(-2px);
            }
          }
        }

        @media (max-width: 576px) {
          flex-direction: column;
          width: 100%;

          .ant-btn {
            width: 100%;
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .welcome-page .container {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .welcome-page .container {
    max-width: 720px;
  }
}

@media (max-width: 768px) {
  .welcome-page .container {
    max-width: 100%;
    padding: 0 16px;
  }

  .hero-section .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 50px 0;

    .hero-title {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }
  }

  .categories-section,
  .benefits-section,
  .cta-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .categories-section .category-card {
    padding: 24px 12px;
    height: 100px;

    .category-icon {
      font-size: 24px;
    }

    .category-text {
      font-size: 12px;
    }
  }
}

// Animation keyframes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Animation classes
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.fade-in-down {
  animation: fadeInDown 0.6s ease-out forwards;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

// Override Ant Design styles
.welcome-page {
  .ant-card {
    border-radius: 12px;
  }

  .ant-btn {
    border-radius: 8px;
    font-weight: 500;
  }

  .ant-typography {
    margin-bottom: 0;
  }

  .ant-statistic-title {
    font-weight: 500;
  }

  .ant-statistic-content {
    font-weight: 700;
  }
}