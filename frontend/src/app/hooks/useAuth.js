import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { Modal, message } from 'antd';
import { CONSTANT } from '@constant';

export const useAuth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const user = useSelector(state => state.auth.user);
  
  const isAuthenticated = user && user !== CONSTANT.INITIAL;
  const isAdmin = isAuthenticated && user?.isSystemAdmin;

  const requireAuth = (callback, options = {}) => {
    const { 
      title = 'Yêu cầu đăng nhập',
      content = 'Bạn cần đăng nhập để sử dụng tính năng này.',
      showModal = true 
    } = options;

    if (isAuthenticated) {
      // Đã đăng nhập, thực hiện callback
      if (callback) callback();
      return true;
    } else {
      // Chưa đăng nhập
      if (showModal) {
        Modal.confirm({
          title,
          content,
          okText: 'Đăng nhập',
          cancelText: 'Hủy',
          onOk: () => {
            const redirectUrl = location.pathname + location.search;
            navigate(`/auth?redirect=${encodeURIComponent(redirectUrl)}`);
          }
        });
      } else {
        message.warning('Vui lòng đăng nhập để sử dụng tính năng này');
        const redirectUrl = location.pathname + location.search;
        navigate(`/auth?redirect=${encodeURIComponent(redirectUrl)}`);
      }
      return false;
    }
  };

  const requireAdmin = (callback, options = {}) => {
    const { 
      title = 'Yêu cầu quyền admin',
      content = 'Bạn cần quyền quản trị viên để truy cập tính năng này.' 
    } = options;

    if (!isAuthenticated) {
      return requireAuth(callback, {
        title: 'Yêu cầu đăng nhập',
        content: 'Bạn cần đăng nhập với tài khoản quản trị viên.'
      });
    }

    if (!isAdmin) {
      Modal.error({
        title,
        content
      });
      return false;
    }

    if (callback) callback();
    return true;
  };

  return {
    user,
    isAuthenticated,
    isAdmin,
    requireAuth,
    requireAdmin
  };
};
