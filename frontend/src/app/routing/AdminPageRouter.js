import React from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';

import { LINK } from '@link';
import { CONSTANT } from '@constant';

import NeedAccess from '../component/NeedAccess';

// Protected Route Component for Admin
const AdminProtectedRoute = ({ children, user }) => {
  const location = useLocation();
  const isAuthenticated = user && user !== CONSTANT.INITIAL;
  const isAdmin = isAuthenticated && user?.isSystemAdmin;

  if (!isAuthenticated) {
    const redirectUrl = location.pathname + location.search;
    return <Navigate to={`/auth?redirect=${encodeURIComponent(redirectUrl)}`} replace />;
  }

  if (!isAdmin) {
    return <Navigate to={LINK.ERROR_404} replace />;
  }

  return children;
};

// E-commerce Admin Pages
import Dashboard from '@pages/AdminPage/Dashboard';
import Products from '@pages/AdminPage/Products';
import Categories from '@pages/AdminPage/Categories';
import Orders from '@pages/AdminPage/Orders';
import Setting from '@pages/AdminPage/Setting';

const AdminPageRouter = ({ user }) => {

  function linkToAdmin(adminUrl) {
    return adminUrl.replace(LINK.ADMIN_PAGE, '');
  }

  return (
    <AdminProtectedRoute user={user}>
      <Routes>
        <Route>
          {/* Dashboard */}
          <Route path={linkToAdmin(LINK.ADMIN.DASHBOARD)} element={<Dashboard/>}/>
          
          {/* Products Management */}
          <Route path={linkToAdmin(LINK.ADMIN.PRODUCTS)} element={<Products/>}/>
          <Route path={linkToAdmin(LINK.ADMIN.PRODUCT_CREATE)} element={<Products/>}/>
          <Route path={linkToAdmin(LINK.ADMIN.PRODUCT_DETAIL)} element={<Products/>}/>
          
          {/* Categories Management */}
          <Route path={linkToAdmin(LINK.ADMIN.CATEGORIES)} element={<Categories/>}/>
          
          {/* Orders Management */}
          <Route path={linkToAdmin(LINK.ADMIN.ORDERS)} element={<Orders/>}/>
          
          {/* Settings */}
          <Route path={linkToAdmin(LINK.ADMIN.SETTING)} element={<Setting/>}/>

          {/* Default redirect to dashboard */}
          <Route path="" element={<Navigate to={LINK.ADMIN.DASHBOARD} replace/>}/>
          <Route path="*" element={<Navigate to={LINK.ERROR_404} replace/>}/>

        </Route>
      </Routes>
    </AdminProtectedRoute>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminPageRouter);
