import React, { useEffect } from 'react';
import { BrowserRouter, Navigate, Route, Routes, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';

import Layout from '@app/layout';
import App from '@app/App';
import Error404 from '@app/pages/Error/404';
import Welcome from '@app/pages/Welcome';

// Auth Pages
import Logout from '@app/auth/Logout';
import ResetPassword from '@app/auth/components/ResetPassword';
import AuthPage, { AuthLayout } from '@app/auth/AuthPage';
import LoginGoogle from '@app/auth/components/LoginGoogle';
import ActivateAccount from '@src/app/auth/components/ActivateAccount';

// Shop Pages
import HomePage from '@pages/ShopPage/HomePage';
import ProductPage from '@pages/ShopPage/ProductPage';
import CartPage from '@pages/ShopPage/CartPage';
import CheckoutPage from '@pages/ShopPage/CheckoutPage';
import OrderPage from '@pages/ShopPage/OrderPage';

// Admin Router
import AdminPageRouter from './AdminPageRouter';

import { LINK } from '@link';
import { CONSTANT } from '@constant';
import { message } from 'antd';

// Protected Route Component
const ProtectedRoute = ({ children, user, requiredAuth = true, requiredAdmin = false }) => {
  const location = useLocation();
  const isAuthenticated = user && user !== CONSTANT.INITIAL;
  const isAdmin = isAuthenticated && user?.isSystemAdmin;

  if (requiredAuth && !isAuthenticated) {
    const redirectUrl = location.pathname + location.search;
    return <Navigate to={`/auth?redirect=${encodeURIComponent(redirectUrl)}`} replace />;
  }

  if (requiredAdmin && !isAdmin) {
    return <Navigate to={LINK.ERROR_404} replace />;
  }

  return children;
};

// Auth Checker Component
const AuthChecker = ({ children, user }) => {
  const location = useLocation();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const redirectUrl = urlParams.get('redirect');
    
    if (redirectUrl && user && user !== CONSTANT.INITIAL) {
      window.location.href = decodeURIComponent(redirectUrl);
      message.success('Đăng nhập thành công!');
    }
  }, [user, location.search]);

  useEffect(() => {
    const jsContent = document.getElementById('js-layout-content');
    if (jsContent) {
      jsContent.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [location]);

  return children;
};

const AppRoutes = ({ user }) => {

  return (
    <BrowserRouter basename="/">
      <AuthChecker user={user}>
        <Routes>
          <Route element={<App/>}>
            {/* Auth Routes */}
            <Route path="auth/login-google" element={<LoginGoogle/>}/>
            <Route path="auth/*" element={<AuthPage/>}/>
            <Route path="logout" element={<Logout/>}/>
            <Route path="reset-password" element={<AuthLayout/>}>
              <Route path="" element={<ResetPassword/>}/>
            </Route>
            <Route path={LINK.ACTIVATE_ACCOUNT} element={<ActivateAccount/>}/>
            
            {/* Main Layout Routes */}
            <Route element={<Layout/>}>
              {/* Welcome Page */}
              <Route path={LINK.WELCOME} element={<Welcome/>}/>

              {/* Shop Routes */}
              <Route path={LINK.SHOP.HOME} element={<HomePage/>}/>
              <Route path={LINK.SHOP.PRODUCTS} element={<ProductPage/>}/>
              
              {/* Protected Shop Routes */}
              <Route 
                path={LINK.SHOP.CART} 
                element={
                  <ProtectedRoute user={user} requiredAuth={true}>
                    <CartPage/>
                  </ProtectedRoute>
                }
              />
              <Route 
                path={LINK.SHOP.CHECKOUT} 
                element={
                  <ProtectedRoute user={user} requiredAuth={true}>
                    <CheckoutPage/>
                  </ProtectedRoute>
                }
              />
              <Route 
                path={LINK.SHOP.ORDERS} 
                element={
                  <ProtectedRoute user={user} requiredAuth={true}>
                    <OrderPage/>
                  </ProtectedRoute>
                }
              />

              {/* Admin Routes */}
              <Route path={`${LINK.ADMIN_PAGE}/*`} element={<AdminPageRouter/>}/>

              {/* Error Pages */}
              <Route path={LINK.ERROR_404} element={<Error404/>}/>
              
              {/* Default Routes */}
              <Route path="/" element={<Navigate to={LINK.SHOP.HOME} replace/>}/>
              <Route path="*" element={<Navigate to={LINK.ERROR_404} replace/>}/>
            </Route>
          </Route>
        </Routes>
      </AuthChecker>
    </BrowserRouter>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AppRoutes);
