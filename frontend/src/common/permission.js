import { CONSTANT } from '@src/constants/constant';

export function checkPermission(permissionRequest, permissionGranted) {
  if (permissionGranted?.[CONSTANT.ALL_LOWER]?.includes(CONSTANT.ALL_LOWER)) return true;

  const [serviceRequest, actionRequest] = permissionRequest.split('.');
  if (!serviceRequest || !actionRequest) return false;

  return !!permissionGranted?.[serviceRequest]?.includes(CONSTANT.ALL_LOWER)
    || !!permissionGranted?.[serviceRequest]?.includes(actionRequest);
}

export function hideActionCell(actionPermission) {
  return !(actionPermission.detail || actionPermission.update) && !actionPermission.remove;
}

export const PERMISSION_INIT = {
  list: false,
  detail: false,
  create: false,
  remove: false,
  update: false,
};

export const PERMISSIONS = {
  USER: {
    LIST: 'users.list',
    CREATE: 'users.create',
    REMOVE: 'users.remove',
    UPDATE: 'users.update',
  },
  ORGANIZATION: {
    LIST: 'organizations.list',
    CREATE: 'organizations.create',
    REMOVE: 'organizations.remove',
    UPDATE: 'organizations.update',
  },
  INSPECTION: {
    MY_LIST: 'inspections.list',
    LIST: 'inspections.getAllByOrgID',
    DETAIL: 'inspections.get',
    CREATE: 'inspections.create',
    REMOVE: 'inspections.remove',
    UPDATE: 'inspections.update',
  },
  CATEGORY: {
    LIST: 'categories.list',
    DETAIL: 'categories.get',
    CREATE: 'categories.create',
    REMOVE: 'categories.remove',
    UPDATE: 'categories.update',
  },
  ROLE: {
    LIST: 'roles.list',
    DETAIL: 'roles.get',
    CREATE: 'roles.create',
    REMOVE: 'roles.remove',
    UPDATE: 'roles.update',
  },
  PERMISSION: {
    LIST: 'permissions.list',
    DETAIL: 'permissions.get',
    CREATE: 'permissions.create',
    REMOVE: 'permissions.remove',
    UPDATE: 'permissions.update',
  },
  AI_MODEL: {
    LIST: 'aimodels.list',
    CREATE: 'aimodels.create',
    REMOVE: 'aimodels.remove',
    UPDATE: 'aimodels.update',
    DETAIL: 'aimodels.get',
  },
  ASSET: {
    LIST: 'assets.list',
    CREATE: 'assets.create',
    REMOVE: 'assets.remove',
    UPDATE: 'assets.update',
    DETAIL: 'assets.getDetailAsset',
  },
  ASSET_TYPE: {
    LIST: 'assettypes.list',
    CREATE: 'assettypes.create',
    REMOVE: 'assettypes.remove',
    UPDATE: 'assettypes.update',
    DETAIL: 'assettypes.get',
  },
};
