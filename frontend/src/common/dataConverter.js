import { camelCase, snakeCase } from 'lodash';
import moment from 'moment';
import { cloneObj } from '@src/common/functionCommons';
import { PAGINATION_INIT } from '@src/constants/constant';

export function extractIds(listData) {
  return listData?.map?.(element => element?._id)?.filter(x => !!x);
}

export function extractKeys(listData, key) {
  return listData?.map?.(element => element[key]);
}

export function groupBy(list, key) {
  return list.reduce(function(grouped, element) {
    grouped[element[key]] ||= [];
    grouped[element[key]].push(element);
    return grouped;
  }, {});
}

export function convertArrayToObject(list, key) {
  return list.reduce(function(grouped, element) {
    grouped[element[key]] = element;
    return grouped;
  }, {});
}

//--------------------------------------------------------------------
export function convertSnakeCaseToCamelCase(dataInput) {
  if (typeof dataInput === 'object') {
    if (Array.isArray(dataInput)) {
      let objOutput = [];
      dataInput.forEach(item => {
        objOutput = [...objOutput, convertSnakeCaseToCamelCase(item)];
      });
      return objOutput;
    } else {
      return convertObjectToCamelCase(dataInput);
    }
  }
  return dataInput;
}

export function convertObjectToCamelCase(objInput) {
  if (!objInput) return objInput;
  const objOutput = {};
  Object.entries(objInput).forEach(([key, value]) => {
    if (key === 'extra') {
      objOutput[key] = value;
    } else {
      if (typeof value === 'object') {
        if (Array.isArray(value)) {
          // array
          objOutput[camelCase(key)] = convertSnakeCaseToCamelCase(value);
        } else {
          // object
          objOutput[camelCase(key)] = convertObjectToCamelCase(value);
        }
      } else {
        if (key === '_id') {
          objOutput._id = value;
          objOutput.key = value;
        } else {
          objOutput[camelCase(key)] = value;
        }
      }
    }
  });
  return objOutput;
}

//--------------------------------------------------------------------
export function convertCamelCaseToSnakeCase(dataInput) {
  dataInput = cloneObj(dataInput);
  if (typeof dataInput === 'object') {
    if (Array.isArray(dataInput)) {
      let objOutput = [];
      dataInput.forEach(item => {
        objOutput = [...objOutput, convertCamelCaseToSnakeCase(item)];
      });
      return objOutput;
    } else {
      return convertObjectToSnakeCase(dataInput);
    }
  }
  return dataInput;
}

export function convertObjectToSnakeCase(objInput) {
  if (!objInput) return objInput;
  objInput = cloneObj(objInput);
  const objOutput = {};
  Object.entries(objInput).forEach(([key, value]) => {
    if (key === 'extra' || key === '_id') {
      objOutput[key] = value;
    } else {
      if (typeof value === 'object') {
        if (moment.isMoment(value)) {
          objOutput[snakeCase(key)] = value;
        } else if (Array.isArray(value)) {
          // array
          objOutput[snakeCase(key)] = convertCamelCaseToSnakeCase(value);
        } else {
          // object
          objOutput[snakeCase(key)] = convertObjectToSnakeCase(value);
        }
      } else {
        if (key === '_id') {
          objOutput._id = value;
        } else {
          objOutput[snakeCase(key)] = value !== undefined ? value : null;
        }
      }
    }
  });
  return objOutput;
}

//--------------------------------------------------------------------

export function handlePagingData(pagingData, query) {
  try {
    pagingData = cloneObj(pagingData);
    const rows = pagingData.rows;
    delete pagingData.rows;

    return { rows, paging: pagingData, query };
  } catch (_) {
    return Object.assign({}, PAGINATION_INIT, { query });
  }
}

export const parseTextToJson = (text = '') => {
  if (!text) return null;
  try {
    const jsonString = text.replaceAll('\n', '').replaceAll('"', '"');
    return JSON.parse(jsonString);
  } catch (error) {
    return null;
  }
};

export const parseJsonToText = (json = {}) => {
  if (!json) return undefined;
  return JSON.stringify(json, null, 2);
};

export const upperCaseFirstLetter = (text = '') => {
  if (!text) return text;
  return text.charAt(0).toUpperCase() + text.slice(1);
};

//------------------------------------------------------------------------

export function convertPascalCaseToCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => convertObjectToCamelCase(item)); // Nếu là array, xử lý từng phần tử
  } else if (obj !== null && typeof obj === 'object') {
    const result = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const camelCaseKey = key.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());

        // Nếu giá trị là object hoặc array, gọi đệ quy
        result[camelCaseKey] = convertObjectToCamelCase(obj[key]);
      }
    }
    return result;
  }
  return obj; // Nếu không phải object hoặc array, trả về giá trị gốc
}

//------------------------------------------------------------------------

export function toConstantFormat(text) {
  if (typeof text !== 'string' || !text) return '';

  return text
    .trim() // Loại bỏ khoảng trắng ở đầu và cuối
    .replace(/[^a-zA-Z0-9\s]/g, '') // Loại bỏ ký tự đặc biệt
    .replace(/\s+/g, '_') // Thay khoảng trắng bằng gạch dưới
    .toUpperCase(); // Chuyển thành chữ in hoa
}