import _ from 'lodash';

if (!String.prototype.format) { // define format of string
  String.prototype.format = function() {
    var args = arguments;
    return this.replace(/{(\d+)}/g, function(match, number) {
      return typeof args[number] !== 'undefined' ? args[number] : '';
    });
  };
}
if (!Number.prototype.fixedFloat) { // define format of string
  Number.prototype.fixedFloat = function(fractionDigits) {
    return parseFloat(this).toLocaleString(
      undefined,
      { minimumFractionDigits: 0, maximumFractionDigits: fractionDigits });

  };
}

if (!String.prototype.toSentenceCase) { // define format of string
  String.prototype.toSentenceCase = function() {
    if (!this) return '';
    return _.upperFirst(_.toLower(this));
  };
}

if (!String.prototype.toUpperSnakeCase) { // define format of string
  String.prototype.toUpperSnakeCase = function() {
    if (!this) return '';
    return _.snakeCase(this).toUpperCase();
  };
}

if (!String.prototype.truncate) { // define format of string
  String.prototype.truncate = function(size) {
    if (this.length <= size) return this;
    return this.substring(0, Number(size)) + '…';
  };
}

if (!String.prototype.toDate) { // define format of string
  String.prototype.toDate = function() {
    let date = new Date(this);
    return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;
  };
}

if (!Array.prototype.last) { // get last item of array
  Array.prototype.last = function() {
    return this[this.length - 1];
  };
}

if (!Array.prototype.first) { // get last item of array
  Array.prototype.first = function() {
    return this[0];
  };
}

if (!Array.prototype.max) {
  Array.prototype.max = function() {
    return Math.max.apply(null, this);
  };
}
if (!Array.prototype.min) {
  Array.prototype.min = function() {
    return Math.min.apply(null, this);
  };
}
if (!Array.prototype.insert) {
  Array.prototype.insert = function(index, item) {
    this.splice(index, 0, item);
  };
}
Object.defineProperties(Array.prototype, {
  count: {
    value: function(value) {
      return this.filter(x => x == value).length;
    },
  },
});
