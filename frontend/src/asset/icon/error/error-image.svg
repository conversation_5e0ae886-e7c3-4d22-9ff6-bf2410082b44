<svg width="42" height="41" viewBox="0 0 42 41" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2604_3126)">
    <g filter="url(#filter0_bd_2604_3126)">
      <path
        d="M34.075 23.7696L34.0378 12.9021H22.1228V1.77148H1.59082V31.6296L25.0646 32.0999C25.0646 32.0999 26.132 25.1246 34.076 23.7706L34.075 23.7696Z"
        fill="#FFB7B5"/>
    </g>
    <g filter="url(#filter1_d_2604_3126)">
      <path
        d="M35.4905 23.7981H33.3959V14.0952C33.166 14.0844 32.9791 14.0687 32.7923 14.0687C29.5961 14.0667 26.4009 14.0687 23.2047 14.0677C21.5553 14.0677 20.7609 13.2615 20.7609 11.5884C20.7609 8.80576 20.7609 6.0231 20.7609 3.24044C20.7609 3.06174 20.7609 2.88205 20.7609 2.65622H2.44478V30.8019H25.2387C25.1487 31.5531 25.0675 32.2257 24.9814 32.9434C24.7906 32.9434 24.6135 32.9434 24.4374 32.9434C16.8065 32.9434 9.17562 32.9434 1.54472 32.9434C0.551725 32.9434 0.29834 32.6911 0.29834 31.6994C0.29834 21.6979 0.29834 11.6974 0.29834 1.69692C0.29834 0.763145 0.56053 0.5 1.48798 0.5C7.99381 0.5 14.4996 0.5 21.0055 0.5C22.2881 0.5 22.8897 1.10779 22.8897 2.39995C22.8907 5.18261 22.8887 7.96527 22.8897 10.7479C22.8897 11.6198 23.1999 11.9301 24.0676 11.9311C27.2305 11.9331 30.3944 11.9311 33.5573 11.9321C34.8468 11.9321 35.4885 12.5674 35.4895 13.8507C35.4924 16.9603 35.4895 20.0709 35.4895 23.1805C35.4895 23.3887 35.4895 23.5959 35.4895 23.7981H35.4905Z"
        fill="url(#paint0_linear_2604_3126)"/>
    </g>
    <path
      d="M28.0985 25.8523C27.3794 26.8656 26.7249 27.8131 26.0362 28.7361C25.9393 28.8657 25.6791 28.9246 25.4932 28.9256C23.3096 28.9383 21.126 28.9344 18.9424 28.9344C14.9821 28.9344 11.0219 28.9344 7.06263 28.9344C6.93252 28.9344 6.80142 28.9373 6.67131 28.9324C6.06279 28.9079 5.85636 28.5799 6.10486 28.0203C7.1047 25.7659 8.10846 23.5124 9.11124 21.259C9.76867 19.7813 10.4271 18.3045 11.0865 16.8278C11.3858 16.1581 11.8075 16.109 12.2311 16.708C12.8259 17.5475 13.4041 18.3988 13.995 19.2412C14.3658 19.7695 14.4881 19.8039 15.0966 19.5535C15.9252 19.2118 16.7509 18.8652 17.5757 18.5156C18.3123 18.2034 18.5237 18.2967 18.7575 19.0517C19.1009 20.1603 19.4423 21.2688 19.7886 22.3754C19.9941 23.0343 20.2974 23.1452 20.8658 22.7652C22.0182 21.9954 23.1658 21.2178 24.3202 20.4499C24.8417 20.1033 25.0784 20.1662 25.367 20.7082C26.3023 22.4667 27.2346 24.2263 28.0985 25.8542V25.8523Z"
      fill="url(#paint1_linear_2604_3126)"/>
    <path
      d="M34.1463 40.5C29.9855 40.4922 26.626 37.1145 26.6289 32.9424C26.6318 28.7557 30.0237 25.3584 34.1933 25.3662C38.359 25.3741 41.7136 28.7793 41.7019 32.9896C41.6902 37.1626 38.3238 40.5088 34.1473 40.501L34.1463 40.5ZM32.9029 31.0778C32.9029 31.8467 32.8882 32.6155 32.9068 33.3843C32.9254 34.1178 33.4997 34.6843 34.1737 34.6735C34.8693 34.6617 35.4309 34.1099 35.4387 33.3745C35.4543 31.8702 35.4543 30.365 35.4387 28.8598C35.4309 28.0929 34.8566 27.5166 34.16 27.5224C33.441 27.5283 32.9166 28.0811 32.9039 28.8696C32.8921 29.605 32.9009 30.3414 32.9019 31.0778H32.9029ZM34.1718 38.3458C34.9016 38.3448 35.4808 37.7674 35.4798 37.0428C35.4778 36.3329 34.8791 35.731 34.1718 35.729C33.4527 35.7271 32.8725 36.3142 32.8735 37.0438C32.8735 37.7861 33.4341 38.3467 34.1727 38.3458H34.1718Z"
      fill="url(#paint2_linear_2604_3126)"/>
    <path d="M24.1685 2.15063C27.5016 4.99123 30.7623 7.77094 34.118 10.6322H24.1685V2.15063Z"
          fill="url(#paint3_linear_2604_3126)"/>
    <path
      d="M17.0883 12.6587C18.1332 12.6528 18.951 13.455 18.9657 14.5017C18.9804 15.5268 18.1498 16.3791 17.1216 16.3928C16.1041 16.4066 15.2579 15.5661 15.251 14.5341C15.2432 13.4757 16.0386 12.6646 17.0883 12.6587Z"
      fill="url(#paint4_linear_2604_3126)"/>
    <path
      d="M32.9039 31.0779C32.9039 30.3415 32.8941 29.6061 32.9059 28.8697C32.9186 28.0802 33.443 27.5284 34.1621 27.5225C34.8586 27.5166 35.4329 28.093 35.4407 28.8598C35.4564 30.3641 35.4564 31.8693 35.4407 33.3745C35.4329 34.11 34.8713 34.6608 34.1757 34.6736C33.5017 34.6854 32.9264 34.1188 32.9088 33.3844C32.8893 32.6165 32.9049 31.8467 32.9039 31.0779Z"
      fill="white"/>
    <path
      d="M34.1727 38.3457C33.4341 38.3467 32.8745 37.7861 32.8735 37.0438C32.8735 36.3142 33.4527 35.727 34.1718 35.729C34.8791 35.731 35.4788 36.3329 35.4798 37.0428C35.4817 37.7674 34.9016 38.3447 34.1718 38.3457H34.1727Z"
      fill="white"/>
  </g>
  <defs>
    <filter id="filter0_bd_2604_3126" x="-7.40918" y="-7.22852" width="50.4854" height="48.3284"
            filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="4.5"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2604_3126"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                     result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.156863 0 0 0 0 0.156863 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="effect1_backgroundBlur_2604_3126" result="effect2_dropShadow_2604_3126"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2604_3126" result="shape"/>
    </filter>
    <filter id="filter1_d_2604_3126" x="-3.70166" y="0.5" width="43.1924" height="40.4434" filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                     result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.156863 0 0 0 0 0.156863 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2604_3126"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2604_3126" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_2604_3126" x1="17.8946" y1="0.5" x2="17.8946" y2="32.9434"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFA0A0"/>
      <stop offset="1" stop-color="#FF8484"/>
    </linearGradient>
    <linearGradient id="paint1_linear_2604_3126" x1="17.0502" y1="16.29" x2="17.0502" y2="28.9353"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFAFA"/>
      <stop offset="1" stop-color="white" stop-opacity="0.4"/>
    </linearGradient>
    <linearGradient id="paint2_linear_2604_3126" x1="34.1654" y1="25.3662" x2="34.1654" y2="40.501"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF6161"/>
      <stop offset="1" stop-color="#F52828"/>
    </linearGradient>
    <linearGradient id="paint3_linear_2604_3126" x1="29.1432" y1="2.15063" x2="29.1432" y2="10.6322"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF6161"/>
      <stop offset="1" stop-color="#FF6161"/>
    </linearGradient>
    <linearGradient id="paint4_linear_2604_3126" x1="17.1084" y1="12.6587" x2="17.1084" y2="16.393"
                    gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFAFA"/>
      <stop offset="1" stop-color="white" stop-opacity="0.4"/>
    </linearGradient>
    <clipPath id="clip0_2604_3126">
      <rect width="41.4035" height="40" fill="white" transform="translate(0.29834 0.5)"/>
    </clipPath>
  </defs>
</svg>
