import { all } from 'redux-saga/effects';
import { combineReducers } from 'redux';

import { DUCKS } from '@src/ducks';

const reducers = {}, sagas = [];

Object.entries(DUCKS).forEach(([key, value]) => {
  reducers[key] = value?.reducer;
  sagas.push(value.saga());
});

export const combinedReducer = combineReducers(reducers);

export const rootReducer = (state, action) => {
  if (action.type === 'Auth/Logout') {
    Object.keys(state).forEach(key => {
      if (key !== 'app') {
        delete state[key];
      }
    });

    // state = {app:state.app};
  }
  return combinedReducer(state, action);
};

export function * rootSaga() {
  yield all(sagas);
}
