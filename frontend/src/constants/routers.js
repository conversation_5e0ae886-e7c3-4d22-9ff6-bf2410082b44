import '@src/common/prototype';

// import Welcome from '@app/pages/Welcome';
// import HomePage from '@app/pages/HomePage';
// import Setting from '@app/pages/Setting';
// import Folder from '@app/pages/Folder';
// import { Workspace } from '@app/pages/Workspace';
// import { Project } from '@app/pages/Project';
//
// import User from '@app/pages/User';
// import UserStudent from '@src/app/pages/Student/UserStudent';
// import ShareWithMe from '@app/pages/ShareWithMe';
// import ToolPage from '@app/pages/ToolPage';
// import StarredPage from '@src/app/pages/Starred/StarredPage';
// import TemplatePage from '@src/app/pages/Template';
// import PackagePage from '@src/app/pages//Package';
// import { Organization } from '@app/pages/Organization';
// import Payment from '@app/pages/Payment';
// import PaymentHistory from '@src/app/pages/PaymentHistory';
// import DashboardPage from '@app/pages/Dashboard';
// import { Resource } from '@app/pages/Resource';
// import CreateExam from '@app/pages/Exam/CreateExam';
// import MarkExam from '@src/app/pages/Exam/MarkExam';
// import { CreateWriting } from '@src/app/pages/Writing/CreateWriting';
// import ResultWriting from '@src/app/pages/Writing/ResultWriting';
// import { Speaking } from '@src/app/pages/Student/Speaking';
// import SpeakingSpeeches from '@app/pages/Student/SpeakingSpeeches';
// import MyEssay from '@src/app/pages/Writing/MyEssay';
// import Pricing from '@src/app/pages/Pricing';
// import Exchange from '@src/app/pages/Student/Exchange';
// import { TotalTopicScreen } from '@src/app/pages/Student/DictationShadowing/TotalTopicScreen';
// import { DictationScreen } from '@src/app/pages/Student/DictationShadowing/DictationScreen/DictationScreen';
// import { ShadowingScreen } from '@src/app/pages/Student/DictationShadowing/ShadowingScreen/ShadowingScreen';

const ROUTERS = [
  // {
  //   menuLang: 'WELCOME',
  //   path: LINK.WELCOME,
  //   element: <Welcome/>,
  //   permissionRequired: null,
  // },
  // {
  //   menuLang: 'HOMEPAGE',
  //   path: LINK.HOMEPAGE,
  //   element: <HomePage/>,
  //   permissionRequired: null,
  // },
  // {
  //   menuLang: 'Setting',
  //   path: LINK.SETTING,
  //   element: <Setting/>,
  //   permissionRequired: null,
  // },
  // {
  //   menuLang: 'Workspace',
  //   path: LINK.MY_WORKSPACE,
  //   element: <Workspace/>,
  //   permissionRequired: null,
  // },
  // {
  //   menuLang: 'Workspace',
  //   path: LINK.ORGANIZATION_WORKSPACE,
  //   element: <Workspace/>,
  //   permissionRequired: null,
  // },
  //{
  //  menuLang: 'Workspace',
  //  path: LINK.WORKSPACE_DETAIL.format(':id'),
  //  element: <Workspace />,
  //  permissionRequired: null,
  //},
  // {
  //   menuLang: 'Project',
  //   path: LINK.PROJECT_DETAIL.format(':id'),
  //   element: <Project/>,
  //   permissionRequired: null,
  // },
  // {
  //   menuLang: 'Folder',
  //   path: LINK.FOLDER_DETAIL.format(':id'),
  //   // icon: HOMEPAGE,
  //   // activeIcon: DASHBOARD_ACTIVE,
  //   element: <Folder/>,
  //   permissionRequired: null,
  // },
  //
  // {
  //   menuLang: 'USERS',
  //   path: LINK.ACCOUNT,
  //   permissionRequired: null,
  //   element: <User/>,
  // },
  // {
  //   menuLang: 'USERS',
  //   path: LINK.ACCOUNT,
  //   permissionRequired: null,
  //   element: <UserStudent/>,
  //   forStudent: true,
  // },

  // {
  //   menuLang: 'Share with me',
  //   path: LINK.SHARE_WITH_ME,
  //   permissionRequired: null,
  //   element: <ShareWithMe/>,
  // },
  // {
  //   menuLang: 'Tools',
  //   path: LINK.TOOLS,
  //   permissionRequired: null,
  //   element: <ToolPage/>,
  // },
  // {
  //   menuLang: 'Starred',
  //   path: LINK.STARRED,
  //   permissionRequired: null,
  //   element: <StarredPage/>,
  // },
  // {
  //   menuLang: 'Template',
  //   path: LINK.TEMPLATE,
  //   permissionRequired: null,
  //   element: <TemplatePage/>,
  // },
  // {
  //   menuLang: 'Package',
  //   path: LINK.SUBSCRIPTION,
  //   permissionRequired: null,
  //   element: <PackagePage/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'Organization',
  //   path: LINK.ORGANIZATION,
  //   permissionRequired: null,
  //   element: <Organization/>,
  // },
  // {
  //   menuLang: 'PAYMENT',
  //   path: LINK.PAYMENT_ID.format(':id'),
  //   permissionRequired: null,
  //   element: <Payment/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'PAYMENT',
  //   path: LINK.PAYMENT_VNPAY,
  //   permissionRequired: null,
  //   element: <Payment/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'PAYMENT_HISTORY',
  //   path: LINK.PAYMENT_HISTORY,
  //   permissionRequired: null,
  //   element: <PaymentHistory/>,
  // },
  // {
  //   menuLang: 'EXCHANGE',
  //   path: LINK.EXCHANGE,
  //   permissionRequired: null,
  //   element: <Exchange/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'MY_DASHBOARD',
  //   path: LINK.MY_DASHBOARD,
  //   permissionRequired: null,
  //   element: <DashboardPage/>,
  // },
  // {
  //   menuLang: 'DASHBOARD',
  //   path: LINK.ORG_DASHBOARD,
  //   permissionRequired: null,
  //   element: <DashboardPage/>,
  // },
  // {
  //   menuLang: 'RESOURCE',
  //   path: LINK.RESOURCE,
  //   permissionRequired: null,
  //   element: <Resource/>,
  // },
  // {
  //   menuLang: 'CREATE_EXAM',
  //   path: LINK.CREATE_EXAM,
  //   permissionRequired: null,
  //   element: <CreateExam/>,
  // },
  // {
  //   menuLang: 'MARK_EXAM',
  //   path: LINK.MARK_EXAM,
  //   permissionRequired: null,
  //   element: <MarkExam/>,
  // },
  // {
  //   menuLang: 'Speaking',
  //   path: LINK.SPEAKING,
  //   permissionRequired: null,
  //   element: <Speaking/>,
  //   forStudent: true,
  // },
  // {
  //   path: LINK.SPEAKING_ID.format(':id'),
  //   element: <Speaking/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'Speaking Speeches',
  //   path: LINK.SPEAKING_SPEECHES,
  //   permissionRequired: null,
  //   element: <SpeakingSpeeches/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'Writing',
  //   path: LINK.WRITING,
  //   permissionRequired: null,
  //   element: <CreateWriting/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'Speaking Speeches',
  //   path: LINK.WRITING_ESSAYS,
  //   permissionRequired: null,
  //   element: <MyEssay/>,
  //   forStudent: true,
  // },
  // {
  //   path: LINK.WRITING_RESULT.format(':id'),
  //   permissionRequired: null,
  //   element: <ResultWriting/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'Pricing',
  //   path: LINK.PRICING,
  //   permissionRequired: null,
  //   element: <Pricing/>,
  //   forStudent: true,
  // },
  // {
  //   menuLang: 'DICTATION_SHADOWING',
  //   path: LINK.DICTATION_SHADOWING,
  //   permissionRequired: null,
  //   element: <TotalTopicScreen/>,
  //   forStudent: true,
  //   isShowInAside: true,
  // },
  // {
  //   path: LINK.DICTATION.format(':id'),
  //   permissionRequired: null,
  //   element: <DictationScreen/>,
  //   forStudent: true,
  //   isShowInAside: true,
  // },
  // {
  //   path: LINK.SHADOWING.format(':id'),
  //   permissionRequired: null,
  //   element: <ShadowingScreen/>,
  //   forStudent: true,
  //   isShowInAside: true,
  // },
];

export { ROUTERS };
