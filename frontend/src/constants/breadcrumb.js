import { <PERSON>IN<PERSON> } from '@link';
import { CONSTANT } from '@constant';

import '@src/common/prototype';

const BREADCRUMB = [
  // Welcome & Home
  {
    path: LINK.WELCOME,
    items: [{ lang: 'WELCOME', url: LINK.WELCOME }],
  },

  // Shop Pages
  {
    path: LINK.SHOP.HOME,
    items: [{ lang: 'HOME', url: LINK.SHOP.HOME }],
  },
  {
    path: LINK.SHOP.PRODUCTS,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'PRODUCTS', url: LINK.SHOP.PRODUCTS }
    ],
  },
  {
    path: LINK.SHOP.PRODUCT_DETAIL.format(':id'),
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'PRODUCTS', url: LINK.SHOP.PRODUCTS },
      { lang: 'PRODUCT_DETAIL', url: LINK.SHOP.PRODUCT_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.SHOP.CATEGORY.format(':slug'),
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'CATEGORY', url: LINK.SHOP.CATEGORY.format(':slug') }
    ],
  },
  {
    path: LINK.SHOP.CART,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'CART', url: LINK.SHOP.CART }
    ],
  },
  {
    path: LINK.SHOP.CHECKOUT,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'CART', url: LINK.SHOP.CART },
      { lang: 'CHECKOUT', url: LINK.SHOP.CHECKOUT }
    ],
  },
  {
    path: LINK.SHOP.ORDERS,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'ORDERS', url: LINK.SHOP.ORDERS }
    ],
  },
  {
    path: LINK.SHOP.ORDER_DETAIL.format(':id'),
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'ORDERS', url: LINK.SHOP.ORDERS },
      { lang: 'ORDER_DETAIL', url: LINK.SHOP.ORDER_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.SHOP.PROFILE,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'PROFILE', url: LINK.SHOP.PROFILE }
    ],
  },
  {
    path: LINK.SHOP.WISHLIST,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'WISHLIST', url: LINK.SHOP.WISHLIST }
    ],
  },
  {
    path: LINK.SHOP.SEARCH,
    items: [
      { lang: 'HOME', url: LINK.SHOP.HOME },
      { lang: 'SEARCH', url: LINK.SHOP.SEARCH }
    ],
  },

  // Account & Settings
  {
    path: LINK.ACCOUNT,
    items: [{ lang: 'ACCOUNT_SETTINGS', url: LINK.ACCOUNT }],
  },
  {
    path: LINK.SETTING,
    items: [{ lang: 'SETTINGS', url: LINK.SETTING }],
  },

  // Information Pages
  {
    path: LINK.ABOUT,
    items: [{ lang: 'ABOUT_US', url: LINK.ABOUT }],
  },
  {
    path: LINK.CONTACT,
    items: [{ lang: 'CONTACT', url: LINK.CONTACT }],
  },
  {
    path: LINK.HELP,
    items: [{ lang: 'HELP', url: LINK.HELP }],
  },
  {
    path: LINK.WARRANTY_POLICY,
    items: [{ lang: 'WARRANTY_POLICY', url: LINK.WARRANTY_POLICY }],
  },
  {
    path: LINK.RETURN_POLICY,
    items: [{ lang: 'RETURN_POLICY', url: LINK.RETURN_POLICY }],
  },
  {
    path: LINK.SHIPPING_POLICY,
    items: [{ lang: 'SHIPPING_POLICY', url: LINK.SHIPPING_POLICY }],
  },
  {
    path: LINK.PAYMENT_POLICY,
    items: [{ lang: 'PAYMENT_POLICY', url: LINK.PAYMENT_POLICY }],
  },
  {
    path: LINK.TERMS_OF_SERVICE,
    items: [{ lang: 'TERMS_OF_SERVICE', url: LINK.TERMS_OF_SERVICE }],
  },
  {
    path: LINK.PRIVACY_POLICY,
    items: [{ lang: 'PRIVACY_POLICY', url: LINK.PRIVACY_POLICY }],
  },

  // Admin Pages
  {
    path: LINK.ADMIN.DASHBOARD,
    items: [{ lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD }],
  },
  {
    path: LINK.ADMIN.PRODUCTS,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'PRODUCTS_MANAGEMENT', url: LINK.ADMIN.PRODUCTS }
    ],
  },
  {
    path: LINK.ADMIN.PRODUCT_CREATE,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'PRODUCTS_MANAGEMENT', url: LINK.ADMIN.PRODUCTS },
      { lang: 'CREATE_PRODUCT', url: LINK.ADMIN.PRODUCT_CREATE }
    ],
  },
  {
    path: LINK.ADMIN.PRODUCT_DETAIL.format(':id'),
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'PRODUCTS_MANAGEMENT', url: LINK.ADMIN.PRODUCTS },
      { lang: 'PRODUCT_DETAIL', url: LINK.ADMIN.PRODUCT_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.ADMIN.CATEGORIES,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'CATEGORIES_MANAGEMENT', url: LINK.ADMIN.CATEGORIES }
    ],
  },
  {
    path: LINK.ADMIN.CATEGORY_CREATE,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'CATEGORIES_MANAGEMENT', url: LINK.ADMIN.CATEGORIES },
      { lang: 'CREATE_CATEGORY', url: LINK.ADMIN.CATEGORY_CREATE }
    ],
  },
  {
    path: LINK.ADMIN.CATEGORY_DETAIL.format(':id'),
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'CATEGORIES_MANAGEMENT', url: LINK.ADMIN.CATEGORIES },
      { lang: 'CATEGORY_DETAIL', url: LINK.ADMIN.CATEGORY_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.ADMIN.ORDERS,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'ORDERS_MANAGEMENT', url: LINK.ADMIN.ORDERS }
    ],
  },
  {
    path: LINK.ADMIN.ORDER_DETAIL.format(':id'),
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'ORDERS_MANAGEMENT', url: LINK.ADMIN.ORDERS },
      { lang: 'ORDER_DETAIL', url: LINK.ADMIN.ORDER_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.ADMIN.CUSTOMERS,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'CUSTOMERS_MANAGEMENT', url: LINK.ADMIN.CUSTOMERS }
    ],
  },
  {
    path: LINK.ADMIN.CUSTOMER_DETAIL.format(':id'),
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'CUSTOMERS_MANAGEMENT', url: LINK.ADMIN.CUSTOMERS },
      { lang: 'CUSTOMER_DETAIL', url: LINK.ADMIN.CUSTOMER_DETAIL.format(':id') }
    ],
  },
  {
    path: LINK.ADMIN.SETTING,
    items: [
      { lang: 'DASHBOARD', url: LINK.ADMIN.DASHBOARD },
      { lang: 'ADMIN_SETTINGS', url: LINK.ADMIN.SETTING }
    ],
  },
];

export default BREADCRUMB;

