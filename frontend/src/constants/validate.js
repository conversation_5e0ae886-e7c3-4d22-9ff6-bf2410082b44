export const Validate = {
  REQUIRED: validateRequired,
  EMAIL: validateEmail,
  EMAIL_REQUIRED: validateEmailRequired,
};

function validateRequired(value) {
  return !value ? 'Required' : '';
}

function validateEmail(value) {
  let error;
  if (value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value)) {
    error = 'Invalid email address';
  }
  return error;
}

function validateEmailRequired(value) {
  let error;
  if (!value) {
    error = 'Required';
  } else {
    error = validateEmail(value);
  }
  return error;
}
