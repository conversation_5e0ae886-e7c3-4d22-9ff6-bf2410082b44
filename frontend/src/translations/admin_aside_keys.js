export const adminAsideKeysEN = {
  // Group titles
  'CONTENT_MANAGEMENT': 'Content Management',
  'USER_MANAGEMENT': 'User Management',
  'BILLING_AND_PROMOTION': 'Billing & Promotion',
  'ANALYTICS_AND_REPORTING': 'Analytics & Reporting',
  'SYSTEM_CONFIGURATION': 'System Configuration',

  // Content Management items
  'INSTRUCTION': 'Instruction',
  'EXPLAIN': 'Explain',
  'GROUP_TOOL': 'Group Tool',
  'KNOWLEDGE': 'Knowledge',
  'OPTIONS': 'Options',
  'OUTPUT_TYPE': 'Output Type',
  'DICTATION_AND_SHADOWING': 'Dictation & Shadowing',
  'DICTATION_SHADOWING_DETAIL': 'Dictation & Shadowing Detail',
  'SPEAKING_EXERCISE': 'Speaking Exercise',
  'SPEAKING_EXERCISE_DETAIL': 'Speaking Exercise Detail',
  'SPEAKING_EXERCISE_DETAIL_DESCRIPTION': 'Create and manage IELTS speaking exercises with multiple parts',

  // Speaking Exercise Part 2
  'SPEAKING_PART2_TITLE': 'Part 2: Cue Card',
  'SPEAKING_PART2_SUBTITLE': 'The candidate speaks for 1-2 minutes on a given topic',
  'SPEAKING_PART2_CUE_CARD_TITLE': 'Cue Cards',
  'SPEAKING_PART2_CUE_CARD_SUBTITLE': 'Add multiple cue cards for this topic',
  'SPEAKING_PART2_CUE_CARD_PLACEHOLDER': 'Enter cue card text here...',
  'RECREATE_AUDIO_TOOLTIP': 'Recreate audio for this cue card',
  'CREATE_AUDIO_TOOLTIP': 'Create audio for this cue card',
  'RECREATE_AUDIO': 'Recreate Audio',
  'CREATE_AUDIO': 'Create Audio',
  'DELETE_CUE_CARD_CONFIRM': 'Delete Cue Card',
  'DELETE_CUE_CARD_DESCRIPTION': 'Are you sure you want to delete this cue card?',
  'DELETE_CUE_CARD_TOOLTIP': 'Delete this cue card',
  'IELTS_CUE_CARD': 'IELTS Cue Card',
  'ADD_CUE_CARD_TOOLTIP': 'Add a new cue card',
  'ADD_CUE_CARD': 'Add Cue Card',

  // User Management items
  'USER': 'User',
  'USER_TRACKING': 'User Tracking',
  'WAITING_LIST': 'Waiting List',
  'ORGANIZATION': 'Organization',
  'PERSONA': 'Persona',

  // Billing & Promotion items
  'DISCOUNT': 'Discount',
  'PROMOTION': 'Promotion',

  // Analytics & Reporting items
  'AVERAGE_TOKEN': 'Average Token',
  'OPENAI_COST': 'OpenAI Cost',
  'OPENAI_COST_ANALYSIS': 'OpenAI Cost Analysis',
  'OPENAI_COST_ANALYSIS_DESCRIPTION': 'Monitor and analyze OpenAI API usage costs across different tools and services',
  'TOTAL_TOKEN_BY_USER': 'Total Token By User',
  'DAILY_ACTIVE_USERS': 'Daily Active Users',
  'MONTHLY_ACTIVE_USERS': 'Monthly Active Users',

  // OpenAI Cost page
  'FILTER_OPTIONS': 'Filter Options',
  'FILTER_OPTIONS_DESCRIPTION': 'Filter cost data by time period and other parameters',
  'SELECT_TIME_PERIOD': 'Select time period',
  'FROM_DATE': 'From date',
  'TO_DATE': 'To date',
  'COST_ANALYSIS_VIEWS': 'Cost Analysis Views',
  'COST_ANALYSIS_VIEWS_DESCRIPTION': 'Different perspectives to analyze OpenAI API costs',
  'TOTAL_COST': 'Total Cost',
  'COST_BY_TOOL': 'Cost By Tool',
  'COST_BY_INSTRUCTION': 'Cost By Instruction',
  'STUDENT_TOOLS_COST': 'Student Tools Cost',
  'STUDENT_TOOLS_COST_SUMMARY': 'Student Tools Cost Summary',
  'STUDENT_TOOLS_COST_BREAKDOWN': 'Student Tools Cost Breakdown',
  'TOP_TOOLS_BY_COST': 'Top Tools by Cost',
  'TOTAL_TOOLS': 'Total Tools',
  'TOTAL_TOOLS_DESCRIPTION': 'Number of different tools used by students',
  'TOTAL_SUBMITS': 'Total Submissions',
  'TOTAL_SUBMITS_DESCRIPTION': 'Total number of submissions across all tools',
  'TOTAL_COST_DESCRIPTION': 'Total cost across all student tools',
  'SHOWING_TOP_10_OF': 'Showing top 10 of',
  'NO_STUDENT_TOOL_COST_DATA': 'No student tool cost data available',
  'NO_COST_DATA_FOUND': 'No cost data found',
  'COST_OVERVIEW': 'Cost Overview',
  'COST_OVERVIEW_TOOLTIP': 'Summary of all OpenAI API costs',
  'COST_BREAKDOWN_BY_TYPE': 'Cost Breakdown by Type',
  'COST_BY_TOOL_TYPE': 'Cost by Tool Type',
  'AVERAGE_COST_PER_SUBMIT': 'Average Cost Per Submit',
  'AVERAGE_COST_PER_SUBMIT_DESCRIPTION': 'Average cost per submission across all tools',
  'MEDIA_TOOLS_COST': 'Media Tools Cost',
  'TEXT_TOOLS_COST': 'Text Tools Cost',
  'IMAGE_TOOLS_COST': 'Image Tools Cost',
  'MEDIA_TOOLS_COST_DESCRIPTION': 'Total cost for audio and video processing tools',
  'TEXT_TOOLS_COST_DESCRIPTION': 'Total cost for text processing and generation tools',
  'IMAGE_TOOLS_COST_DESCRIPTION': 'Total cost for image generation and processing tools',
  'MEDIA_TOOLS_SUBMITS': 'Media Tools Submissions',
  'TEXT_TOOLS_SUBMITS': 'Text Tools Submissions',
  'IMAGE_TOOLS_SUBMITS': 'Image Tools Submissions',
  'OPENAI_COST_DISTRIBUTION': 'OpenAI Cost Distribution',
  'OPENAI_COST_DISTRIBUTION_TOOLTIP': 'Distribution of costs across different tool types',
  'USAGE_SUMMARY': 'Usage Summary',
  'USAGE_SUMMARY_TOOLTIP': 'Summary of tool usage and submissions',
  'SUBMITS': 'Submissions',
  'COST_BY_TOOL_DETAIL': 'Cost By Tool Detail',
  'COST_BY_TOOL_DETAIL_TOOLTIP': 'Detailed breakdown of costs by individual tools',
  'ORDER': 'Order',
  'TOOL': 'Tool',
  'NUMBER_OF_SUBMITS': 'Number of Submissions',
  'TOTAL_TOKENS': 'Total Tokens',
  'OUTPUT_AVERAGE_TOKENS': 'Output Average Tokens',
  'INPUT_AVERAGE_TOKENS': 'Input Average Tokens',
  'AVERAGE_COST': 'Average Cost',
  'INPUT_TOKEN': 'Input Token',
  'OUTPUT_TOKEN': 'Output Token',
  'TOTAL_TOKEN': 'Total Token',
  'COST': 'Cost',
  'ERROR_FETCHING_DATA': 'Error fetching data. Please try again.',

  // System Configuration items
  'GPT_MODEL': 'GPT Model',
  'SETTINGS': 'Settings',
  'VOICE_OPTIONS': 'Voice Options',

  // UserTracking Filter
  'USER_FULL_NAME': 'Full Name',
  'USER_EMAIL': 'Email',
  'USER_TYPE': 'User Type',
  'CREATED_DATE': 'Created Date',
  'ACTIVITY_PERIOD': 'Activity Period',
  'ENTER_FULL_NAME': 'Enter full name',
  'SELECT_USER_TYPE': 'Select user type',
  'USER_THIS_WEEK': 'This Week',
  'USER_THIS_MONTH': 'This Month',
  'USER_CUSTOM': 'Custom',
  'USER_SELECT_FROM_DATE': 'Select from date',
  'USER_SELECT_TO_DATE': 'Select to date',
  'USER_CAN_NOT_BE_BLANK': 'Can not be blank',
  'ONLY_ACTIVE_USER': 'Only Active User',
  'CANNOT_USE_BOTH_FILTERS': 'Cannot use both activity period and created date filters simultaneously',
  'CLEAR': 'Clear',
  'SEARCH': 'Search',

  // UserMetrics
  'AVERAGE_DAILY_ACTIVE_USERS_DESC': 'Average number of active users per day in the current month',
  'AVERAGE_USERS_PER_DAY': 'Average users per day',
  'MONTHLY_ACTIVE_USERS_DESC': 'Total number of users who have been active in the current month',
  'TOTAL_USERS_THIS_MONTH': 'Total users this month',
  'USER_RETENTION_RATE': 'User Retention Rate',
  'USER_RETENTION_RATE_DESC': 'Percentage of daily active users compared to monthly active users',
  'DAILY_TO_MONTHLY_RATIO': 'Daily to monthly user ratio',
  'USER_TRACKING_DESCRIPTION': 'Monitor user activity and engagement metrics',
  'USER_ACTIVITY_OVERVIEW': 'User Activity Overview',
  'NO_USER_DATA_FOUND': 'No user data found',
  'USER_STATUS': 'User Status',
  'ACTIVE': 'Active',
  'ALL': 'All',
  'ALL_USERS': 'All Users',
  'ONLY_ACTIVE': 'Only Active',

  // DocumentTemplate Detail
  'DOCUMENT_TEMPLATE_DETAIL': 'Document Template Detail',
  'TEMPLATE_FILE': 'Template File',
  'NO_FILE_SELECTED': 'No file selected',
  'VISIBILITY_SETTINGS': 'Visibility Settings',
  'DOCUMENT_OPTION': 'Document Option',
  'ADD_DOCUMENT_OPTION': 'Add Document Option',
  'UPDATE_DOCUMENT_OPTION': 'Update Document Option',
  'ENTER_FIELD_NAME': 'Enter field name',
  'ENTER_FIELD_LABEL': 'Enter field label',
  'DOC_TEMPLATE_YES': 'Yes',
  'DOC_TEMPLATE_NO': 'No',
  'HEADER_IMAGE': 'Header Image',
  'UPLOAD_IMAGE': 'Upload Image',
  'FIELD_VALUES': 'Field Values',
  'ENTER_VALUE': 'Enter value',
  'ADD': 'Add',
  'UPDATE': 'Update',
  'CHANGE': 'Change',
  'REMOVE': 'Remove',

  // Settings Page
  'SYSTEM_SETTINGS': 'System Settings',
  'SYSTEM_SETTINGS_DESCRIPTION': 'Configure system-wide settings and integrations',
  'API_INTEGRATION': 'API Integration',
  'USER_LIMITS': 'User Limits',
  'TOOLS_SETTINGS': 'Tools Settings',
  'NOTIFICATION_SETTINGS': 'Notification Settings',
  'DISPLAY_SETTINGS': 'Display Settings',
  'API_KEY_OPENAI': 'API Key OpenAI',
  'API_KEY_OPENAI_DESC': 'API key for OpenAI services integration',
  'API_KEY_OPENAI_REQUIRED': 'API key OpenAI can\'t be blank!',
  'ENTER_API_KEY_OPENAI': 'Enter API key OpenAI',
  'SPEECH_KEY': 'Speech Key',
  'SPEECH_KEY_DESC': 'Key for speech recognition services',
  'ENTER_SPEECH_KEY': 'Enter speech key',
  'SERVICE_REGION': 'Service Region',
  'SERVICE_REGION_DESC': 'Region for speech services',
  'ENTER_SERVICE_REGION': 'Enter service region',
  'AZURE_ENDPOINT': 'Azure Endpoint',
  'AZURE_ENDPOINT_DESC': 'Endpoint URL for Azure services',
  'ENTER_AZURE_ENDPOINT': 'Enter Azure endpoint',
  'USER_LIMIT': 'User Limit',
  'USER_LIMIT_DESC': 'Maximum number of users allowed in the system',
  'USER_LIMIT_REQUIRED': 'User limit can\'t be blank!',
  'ENTER_USER_LIMIT': 'Enter user limit',
  'FREE_SUBMISSION': 'Free Submission',
  'FREE_SUBMISSION_DESC': 'Number of free submissions allowed per user',
  'ENTER_FREE_SUBMISSION': 'Enter number of free submissions',
  'PAID_SUBMISSION': 'Paid Submission',
  'PAID_SUBMISSION_DESC': 'Number of paid submissions allowed per user',
  'ENTER_PAID_SUBMISSION': 'Enter number of paid submissions',
  'STUDENT_TOOLS': 'Student Tools',
  'STUDENT_TOOLS_DESC': 'Tools available for students to use',
  'STUDENT_TOOLS_REQUIRED': 'Student tools can\'t be blank!',
  'SELECT_STUDENT_TOOLS': 'Select student tools',
  'TELEGRAM_BOT_TOKEN': 'Telegram Bot Token',
  'TELEGRAM_BOT_TOKEN_DESC': 'Token for Telegram bot integration',
  'ENTER_TELEGRAM_BOT_TOKEN': 'Enter Telegram bot token',
  'TELEGRAM_CHAT_ID': 'Telegram Chat ID',
  'TELEGRAM_CHAT_ID_DESC': 'Chat ID for Telegram notifications',
  'ENTER_TELEGRAM_CHAT_ID': 'Enter Telegram chat ID',
  'AUTO_SHOW_FEEDBACK': 'Auto Show Feedback',
  'AUTO_SHOW_FEEDBACK_DESC': 'Automatically show feedback to users',
  'SHOW_CHAT_BOT': 'Show Chat Bot',
  'SHOW_CHAT_BOT_DESC': 'Display chat bot on the interface',
  'CHAT_BOT_ID': 'Chat Bot ID',
  'CHAT_BOT_ID_DESC': 'ID for the chat bot integration',
  'ENTER_CHAT_BOT_ID': 'Enter chat bot ID',
  'CONFIRM_UPDATE_SETTINGS': 'Confirm Update Settings',
  'CONFIRM_UPDATE_SENSITIVE_SETTINGS': 'You are about to update sensitive API keys. Are you sure you want to continue?',
  'UPDATE_SETTING_SUCCESS': 'Settings updated successfully',
  'UPDATE_SETTING_ERROR': 'Failed to update settings',

  // Persona Management
  'PERSONA_MANAGEMENT': 'Persona Management',
  'PERSONA_MANAGEMENT_DESCRIPTION': 'Manage user personas and their associated templates and tools',
  'SEARCH_PERSONA_PLACEHOLDER': 'Search persona by name',
  'NO_PERSONA_FOUND': 'No personas found',
  'CREATE_PERSONA': 'Create Persona',
  'EDIT_PERSONA': 'Edit Persona',
  'DELETE_PERSONA': 'Delete Persona',
  'DELETE_PERSONA_CONFIRM': 'Are you sure you want to delete the persona \'{{persona}}\'? This action cannot be undone.',
  'DELETE_PERSONA_SUCCESSFULLY': 'Persona deleted successfully',
  'DELETE_PERSONA_ERROR': 'Failed to delete persona',
  'PERSONA_DETAIL': 'Persona Detail',
  'PERSONA_DETAIL_DESCRIPTION': 'View and edit persona details, templates, and tool options',
  'CREATE_PERSONA_DESCRIPTION': 'Create a new persona with templates and tool options',
  'BASIC_INFORMATION': 'Basic Information',
  'PERSONA_NAME': 'Persona Name',
  'PERSONA_NAME_DESC': 'The name of the persona that will be displayed to users',
  'PERSONA_NAME_REQUIRED': 'Persona name is required',
  'SELECT_PERSONA_NAME': 'Select persona name',
  'TEMPLATE': 'Template',
  'TEMPLATE_DESC': 'Templates associated with this persona',
  'TEMPLATE_REQUIRED': 'Template is required',
  'SELECT_TEMPLATE': 'Select template',
  'TOOL_OPTIONS': 'Tool Options',
  'TOOL_OPTIONS_DESC': 'Configure the tools available for this persona',
  'OPTION_TYPE_DESC': 'The type of tool option',
  'OPTION_TYPE_REQUIRED': 'Option type is required',
  'SELECT_OPTION_TYPE': 'Select option type',
  'TOOLS': 'Tools',
  'TOOLS_DESC': 'The tools available for this option type',
  'TOOLS_REQUIRED': 'Tools are required',
  'SELECT_TOOLS': 'Select tools',
  'ADD_OPTION': 'Add Option',
  'REMOVE_OPTION': 'Remove Option',
  'CONFIRM_LEAVE': 'Confirm Leave',
  'CONFIRM_LEAVE_DESCRIPTION': 'You have unsaved changes. Are you sure you want to leave?',
  'LEAVE': 'Leave',
  'STAY': 'Stay',
  'CREATE_PERSONA_SUCCESSFULLY': 'Persona created successfully',
  'CREATE_PERSONA_ERROR': 'Failed to create persona',
  'UPDATE_PERSONA_SUCCESSFULLY': 'Persona updated successfully',
  'UPDATE_PERSONA_ERROR': 'Failed to update persona',

  // API Key Management
  'API_KEY_MANAGEMENT': 'API Key Management',
  'API_KEY_MANAGEMENT_DESCRIPTION': 'Manage API keys for external service integrations',
  'API_KEY': 'API Key',
  'MODEL_INTERFACE': 'Model Interface',
  'URL': 'URL',
  'CREATE_API_KEY': 'Create API Key',
  'EDIT_API_KEY': 'Edit API Key',
  'DELETE_API_KEY': 'Delete API Key',
  'CONFIRM_DELETE_API_KEY': 'Are you sure you want to delete this API key? This action cannot be undone.',
  'DELETE_API_KEY_SUCCESS': 'API key deleted successfully',
  'DELETE_API_KEY_ERROR': 'Failed to delete API key',
  'CREATE_API_KEY_SUCCESS': 'API key created successfully',
  'CREATE_API_KEY_ERROR': 'Failed to create API key',
  'UPDATE_API_KEY_SUCCESS': 'API key updated successfully',
  'UPDATE_API_KEY_ERROR': 'Failed to update API key',
  'SEARCH_MODEL_INTERFACE_PLACEHOLDER': 'Search model interface',
  'NO_API_KEY_FOUND': 'No API keys found',
  'ENDPOINT': 'Endpoint',
  'REGION': 'Region',
  'AWS_ACCESS_KEY': 'AWS Access Key',
  'AWS_SECRET_KEY': 'AWS Secret Key',
  'AWS_REGION': 'AWS Region',

  // Modal AI Management
  'MODAL_AI': 'Modal AI',
  'MODAL_AI_MANAGEMENT': 'Modal AI Management',
  'MODAL_AI_MANAGEMENT_DESCRIPTION': 'Manage models and their pricing configurations',
  'MODAL_NAME': 'Modal Name',
  // 'API_KEY': 'API Key',
  'TOKEN_UNIT': 'Token Unit',
  'UNIT': 'Unit',
  'INPUT_PRICE': 'Input Price',
  'OUTPUT_PRICE': 'Output Price',
  'MAX_TOKENS': 'Max Tokens',
  'CREATE_MODAL_AI': 'Create Modal AI',
  'EDIT_MODAL_AI': 'Edit Modal AI',
  'DELETE_MODAL_AI': 'Delete Modal AI',
  'CONFIRM_DELETE_MODAL_AI': 'Are you sure you want to delete the modal \'{{modal}}\'? This action cannot be undone.',
  'DELETE_MODAL_AI_SUCCESS': 'Modal AI deleted successfully',
  'DELETE_MODAL_AI_ERROR': 'Failed to delete modal AI',
  'CREATE_MODAL_AI_SUCCESS': 'Modal AI created successfully',
  'CREATE_MODAL_AI_ERROR': 'Failed to create modal AI',
  'UPDATE_MODAL_AI_SUCCESS': 'Modal AI updated successfully',
  'UPDATE_MODA_AI_ERROR': 'Failed to update modal AI',
  'SEARCH_MODAL_NAME_PLACEHOLDER': 'Search modal name',
  'NO_MODAL_AI_FOUND': 'No modal AI found',

  // Support Business Management
  'SUPPORT_BUSINESS': 'Support Business',
  'SUPPORT_BUSINESS_DESCRIPTION': 'Manage business support requests and their status',
  'COMPANY_NAME': 'Company Name',
  'CONTACT_NAME': 'Contact Name',
  'EMAIL': 'Email',
  'PHONE_NUMBER': 'Phone Number',
  'SUPPORT_CREATED_AT': 'Created At',
  'HAS_SUPPORT': 'Has Support',
  'ADD_SUPPORT': 'Add Support',
  'REMOVE_SUPPORT': 'Remove Support',
  'UPDATE_SUPPORT_BUSINESS_SUCCESS': 'Support status updated successfully',
  'UPDATE_SUPPORT_BUSINESS_ERROR': 'Failed to update support status',
  'SEARCH_CONTACT_NAME_PLACEHOLDER': 'Search by contact name',
  'SEARCH_COMPANY_NAME_PLACEHOLDER': 'Search by company name',
  'FILTER_BY_SUPPORT_STATUS': 'Filter by support status',
  'NO_SUPPORT_BUSINESS_FOUND': 'No business support requests found',

  // Feedback Management
  'FEEDBACK_ANALYSIS': 'Feedback Analysis',
  'FEEDBACK_ANALYSIS_DESCRIPTION': 'Analyze user feedback and ratings for different features',
  'FEEDBACK_STATISTICS': 'Feedback Statistics',
  'FEEDBACK_STATISTICS_DESCRIPTION': 'View detailed statistics and individual feedback from users',
  'FEATURE': 'Feature',
  'SEARCH_FEATURE_PLACEHOLDER': 'Search by feature name',
  'SELECT_GROUP_FEEDBACK': 'Select group feedback',
  'SELECT_TIME': 'Select time period',
  'FEEDBACK_THIS_WEEK': 'This week',
  'FEEDBACK_THIS_MONTH': 'This month',
  'FEEDBACK_CUSTOM': 'Custom',
  'FEEDBACK_SELECT_FROM_DATE': 'Select from date',
  'FEEDBACK_SELECT_TO_DATE': 'Select to date',
  'FEEDBACK_CAN_NOT_BE_BLANK': 'Cannot be blank',
  'SELECT_SATISFACTION_LEVEL': 'Select satisfaction level',
  'SELECT_USEFULNESS_LEVEL': 'Select usefulness level',
  'UPGRADE_INTEREST': 'Upgrade interest',
  'RECOMMEND_TO_OTHERS': 'Recommend to others',
  'VERY_BAD': 'Very bad',
  'BAD': 'Bad',
  'AVERAGE': 'Average',
  'GOOD': 'Good',
  'EXCELLENT': 'Excellent',
  'FEEDBACK_YES': 'Yes',
  'FEEDBACK_NO': 'No',
  'NO_FEEDBACK_ANALYSIS_FOUND': 'No feedback analysis found',
  'NO_FEEDBACK_FOUND': 'No feedback found',

  // Package Management
  'PACKAGE_SETTING': 'Package Setting',
  'PACKAGE_SETTING_DESCRIPTION': 'Manage subscription packages and pricing options',
  'PACKAGE_NAME': 'Package Name',
  'PACKAGE_DESCRIPTION': 'Description',
  'PRICE': 'Price',
  'PACKAGE_TYPE': 'Type',
  'CUSTOMER': 'Customer',
  'LAST_MODIFIED': 'Last Modified',
  'CREATE_PACKAGE': 'Create Package',
  'EDIT_PACKAGE': 'Edit Package',
  'DELETE_PACKAGE': 'Delete Package',
  'DELETE_PACKAGE_CONFIRM': 'Are you sure you want to delete the package \'{{name}}\'? This action cannot be undone.',
  'DELETE_PACKAGE_SUCCESS': 'Package deleted successfully',
  'DELETE_PACKAGE_ERROR': 'Failed to delete package',
  'SEARCH_PACKAGE_PLACEHOLDER': 'Search by name, description or type',
  'NO_PACKAGES_FOUND': 'No packages found',
  'MONTH': 'Month',
  'YEAR': 'Year',

  // Promotion Management
  'PROMOTION_MANAGEMENT': 'Promotion Management',
  'PROMOTION_MANAGEMENT_DESCRIPTION': 'Manage promotional codes and discounts for packages',
  'DISPLAY_TEXT': 'Display Text',
  'SEARCH_DISPLAY_TEXT_PLACEHOLDER': 'Search by display text',
  'SELECT_PACKAGE': 'Select Package',
  'IS_ACTIVATE': 'Is Active',
  'START_DATE': 'Start Date',
  'END_DATE': 'End Date',
  'CREATE_PROMOTION': 'Create Promotion',
  'EDIT_PROMOTION': 'Edit Promotion',
  'DELETE_PROMOTION': 'Delete Promotion',
  'DELETE_PROMOTION_CONFIRM': 'Are you sure you want to delete the promotion \'{{name}}\'? This action cannot be undone.',
  'DELETE_PROMOTION_SUCCESS': 'Promotion deleted successfully',
  'DELETE_PROMOTION_ERROR': 'Failed to delete promotion',
  'UPDATE_PROMOTION_SUCCESS': 'Promotion updated successfully',
  'UPDATE_PROMOTION_ERROR': 'Failed to update promotion',
  'CREATE_PROMOTION_SUCCESS': 'Promotion created successfully',
  'CREATE_PROMOTION_ERROR': 'Failed to create promotion',
  'NO_PROMOTIONS_FOUND': 'No promotions found',

  // Discount Management
  'DISCOUNT_MANAGEMENT': 'Discount Management',
  'DISCOUNT_MANAGEMENT_DESCRIPTION': 'Manage discount codes and special offers',
  'DISCOUNT_CODE': 'Discount Code',
  'SEARCH_DISCOUNT_CODE_PLACEHOLDER': 'Search by discount code',
  'FILTER_BY_DISCOUNT_STATUS': 'Filter by status',
  'DISCOUNT_ACTIVE': 'Active',
  'INACTIVE': 'Inactive',
  'DISCOUNT_LEVEL': 'Discount Level',
  'DISCOUNT_TYPE': 'Type',
  'NUMBER_OF_USED': 'Number of Used',
  'LIMIT': 'Limit',
  'SELECT_TYPE': 'Select Type',
  'SELECT_START_DATE': 'Select Start Date',
  'SELECT_END_DATE': 'Select End Date',
  'CREATE_DISCOUNT': 'Create Discount',
  'UPDATE_DISCOUNT': 'Update Discount',
  'EDIT_DISCOUNT': 'Edit Discount',
  'DELETE_DISCOUNT': 'Delete Discount',
  'DELETE_DISCOUNT_CONFIRM': 'Are you sure you want to delete the discount code \'{{code}}\'? This action cannot be undone.',
  'DELETE_DISCOUNT_SUCCESS': 'Discount deleted successfully',
  'DELETE_DISCOUNT_ERROR': 'Failed to delete discount',
  'UPDATE_DISCOUNT_SUCCESS': 'Discount updated successfully',
  'UPDATE_DISCOUNT_ERROR': 'Failed to update discount',
  'CREATE_DISCOUNT_SUCCESS': 'Discount created successfully',
  'CREATE_DISCOUNT_ERROR': 'Failed to create discount',
  'NO_DISCOUNTS_FOUND': 'No discounts found',

  // Wait List Management
  'WAIT_LIST': 'Wait List',
  'WAIT_LIST_DESCRIPTION': 'Manage users in the waiting list for access to the platform',
  'WAITLIST_EMAIL': 'Email',
  'WAITLIST_FULL_NAME': 'Full Name',
  'SEARCH_EMAIL_PLACEHOLDER': 'Search by email',
  'SEARCH_FULL_NAME_PLACEHOLDER': 'Search by full name',
  'MOVE_TO_WHITELIST': 'Move to Whitelist',
  'MOVE_TO_WHITELIST_SUCCESS': 'User moved to whitelist successfully',
  'MOVE_TO_WHITELIST_ERROR': 'Failed to move user to whitelist',
  'NO_WAIT_LIST_FOUND': 'No users found in wait list',

  // Whitelist Management
  'WHITELIST': 'Whitelist',
  'WHITELIST_DESCRIPTION': 'Manage users with approved access to the platform',
  'ADD_WHITELIST': 'Add to Whitelist',
  'DELETE_WHITELIST': 'Delete from Whitelist',
  'DELETE_WHITELIST_CONFIRM': 'Are you sure you want to remove \'{{email}}\' from the whitelist? This action cannot be undone.',
  'DELETE_WHITELIST_SUCCESS': 'User removed from whitelist successfully',
  'DELETE_WHITELIST_ERROR': 'Failed to remove user from whitelist',
  'ADD_TO_WHITELIST_SUCCESS': 'User added to whitelist successfully',
  'ADD_TO_WHITELIST_ERROR': 'Failed to add user to whitelist',
  'EMAIL_CAN_NOT_BE_BLANK': 'Email cannot be blank',
  'INVALID_EMAIL': 'Invalid email format',
  'ENTER_EMAIL': 'Enter email address',
  'NO_WHITELIST_FOUND': 'No users found in whitelist',

  // Organization Management
  'ORGANIZATION_MANAGEMENT': 'Organization Management',
  'ORGANIZATION_MANAGEMENT_DESCRIPTION': 'Manage organizations and their access to the platform',
  'ORGANIZATION_NAME': 'Organization Name',
  'SEARCH_ORGANIZATION_NAME_PLACEHOLDER': 'Search by organization name',
  'CREATE_ORGANIZATION': 'Create Organization',
  'EDIT_ORGANIZATION': 'Edit Organization',
  'DELETE_ORGANIZATION': 'Delete Organization',
  'CONFIRM_DELETE_ORGANIZATION': 'Are you sure you want to delete this organization?',
  'CONFIRM_DELETE_ORGANIZATION_WITH_NAME': 'Are you sure you want to delete the organization \'{{name}}\'? This action cannot be undone.',
  'DELETE_ORGANIZATION_SUCCESS': 'Organization deleted successfully',
  'DELETE_ORGANIZATION_ERROR': 'Failed to delete organization',
  'LOCK': 'Lock',
  'UNLOCK': 'Unlock',
  'CONFIRM_LOCK_ORGANIZATION': 'Are you sure you want to lock this organization?',
  'CONFIRM_UNLOCK_ORGANIZATION': 'Are you sure you want to unlock this organization?',
  'LOCKED': 'Locked',
  'ORGANIZATION_STATUS': 'Status',
  'FILTER_BY_ORGANIZATION_STATUS': 'Filter by status',
  'ORGANIZATION_ACTIVE': 'Active',
  'NO_ORGANIZATIONS_FOUND': 'No organizations found',

  // Document Template Management
  'DOCUMENT_TEMPLATE': 'Document Template',
  'DOCUMENT_TEMPLATE_DESCRIPTION': 'Manage document templates for the application',
  'DOCUMENT_NAME': 'Name',
  'DESCRIPTION': 'Description',
  'DOCUMENT_STATUS': 'Status',
  'PUBLIC': 'Public',
  'PRIVATE': 'Private',
  'SEARCH_TEMPLATE_NAME_PLACEHOLDER': 'Search by template name',
  'FILTER_BY_DOCUMENT_STATUS': 'Filter by status',
  'CREATE_DOCUMENT_TEMPLATE': 'Create Document Template',
  'EDIT_DOCUMENT_TEMPLATE': 'Edit Document Template',
  'DELETE_DOCUMENT_TEMPLATE': 'Delete Document Template',
  'DELETE_DOCUMENT_TEMPLATE_CONFIRM': 'Are you sure you want to delete the document template \'{{name}}\'? This action cannot be undone.',
  'DELETE_DOCUMENT_TEMPLATE_SUCCESS': 'Document template deleted successfully',
  'DELETE_DOCUMENT_TEMPLATE_ERROR': 'Failed to delete document template',
  'CREATE_DOCUMENT_TEMPLATE_SUCCESS': 'Document template created successfully',
  'CREATE_DOCUMENT_TEMPLATE_ERROR': 'Failed to create document template',
  'NO_DOCUMENT_TEMPLATES_FOUND': 'No document templates found',

  // Speaking Exercise Management
  'SPEAKING_EXERCISE_MANAGEMENT': 'Speaking Exercise Management',
  'SPEAKING_EXERCISE_MANAGEMENT_DESCRIPTION': 'Manage speaking exercises for IELTS practice',
  'TITLE': 'Title',
  'TOPIC': 'Topic',
  'EXERCISE_TITLE_TOOLTIP': 'The title of the speaking exercise',
  'EXERCISE_TOPIC_TOOLTIP': 'The main topic or theme of the speaking exercise',
  'EXERCISE_STATUS': 'Status',
  'CREATED_BY': 'Created By',
  'SEARCH_TITLE_PLACEHOLDER': 'Search by title',
  'SEARCH_TOPIC_PLACEHOLDER': 'Search by topic',
  'ENTER_TOPIC': 'Enter topic',
  'FILTER_BY_STATUS': 'Filter by status',
  'FILTER_BY_EXERCISE_STATUS': 'Filter by status',
  'CREATE_EXERCISE': 'Create Exercise',
  'EDIT_EXERCISE': 'Edit Exercise',
  'DELETE_EXERCISE': 'Delete Exercise',
  'DELETE_EXERCISE_CONFIRM': 'Are you sure you want to delete the exercise \'{{title}}\'? This action cannot be undone.',
  'DELETE_EXERCISE_SUCCESS': 'Exercise deleted successfully',
  'DELETE_EXERCISE_ERROR': 'Failed to delete exercise',
  'CREATE_EXERCISE_SUCCESS': 'Exercise created successfully',
  'CREATE_EXERCISE_ERROR': 'Failed to create exercise',
  'NO_EXERCISES_FOUND': 'No exercises found',

  // Dictation and Shadowing Management
  'DICTATION_SHADOWING_MANAGEMENT': 'Dictation & Shadowing Management',
  'DICTATION_SHADOWING_MANAGEMENT_DESCRIPTION': 'Manage dictation and shadowing exercises for language practice',
  'DIFFICULTY': 'Difficulty',
  'TYPE': 'Type',
  'TIME_LIMIT': 'Time Limit',
  'SEARCH_EXERCISE_NAME_PLACEHOLDER': 'Search by name',
  'FILTER_BY_DIFFICULTY': 'Filter by difficulty',
  'FILTER_BY_TYPE': 'Filter by type',

  // Output Type Management
  'OUTPUT_TYPE_MANAGEMENT': 'Output Type Management',
  'OUTPUT_TYPE_MANAGEMENT_DESCRIPTION': 'Manage output types and response formats for the application',
  'OUTPUT_TYPE_CODE': 'Code',
  'SCHEMA': 'Schema',
  'OUTPUT_RESPONSE_FORMAT': 'Response Format',
  'SEARCH_OUTPUT_TYPE_PLACEHOLDER': 'Search by name',
  'SEARCH_RESPONSE_FORMAT_PLACEHOLDER': 'Search by response format',
  'CREATE_OUTPUT_TYPE': 'Create Output Type',
  'EDIT_OUTPUT_TYPE': 'Edit Output Type',
  'DELETE_OUTPUT_TYPE': 'Delete Output Type',
  'DELETE_OUTPUT_TYPE_CONFIRM': 'Are you sure you want to delete the output type \'{{name}}\'? This action cannot be undone.',
  'DELETE_OUTPUT_TYPE_SUCCESS': 'Output type deleted successfully',
  'DELETE_OUTPUT_TYPE_ERROR': 'Failed to delete output type',
  'CREATE_OUTPUT_TYPE_SUCCESS': 'Output type created successfully',
  'CREATE_OUTPUT_TYPE_ERROR': 'Failed to create output type',
  'UPDATE_OUTPUT_TYPE_SUCCESS': 'Output type updated successfully',
  'UPDATE_OUTPUT_TYPE_ERROR': 'Failed to update output type',
  'NO_OUTPUT_TYPES_FOUND': 'No output types found',

  // Options Management
  'OPTIONS_MANAGEMENT': 'Options Management',
  'OPTIONS_MANAGEMENT_DESCRIPTION': 'Manage options and input types for the application',
  'PLACEHOLDER': 'Placeholder',
  'DEFAULT_VALUE': 'Default Value',
  'OPTION_TYPE': 'Type',
  'OPTION_INSTRUCTION': 'Instruction',
  'SEARCH_OPTION_NAME_PLACEHOLDER': 'Search by name',
  'SEARCH_OPTION_CODE_PLACEHOLDER': 'Search by code',
  'CREATE_OPTION': 'Create Option',
  'EDIT_OPTION': 'Edit Option',
  'DELETE_OPTION': 'Delete Option',
  'DELETE_OPTION_CONFIRM': 'Are you sure you want to delete the option \'{{name}}\'? This action cannot be undone.',
  'DELETE_OPTION_SUCCESS': 'Option deleted successfully',
  'DELETE_OPTION_ERROR': 'Failed to delete option',
  'NO_OPTIONS_FOUND': 'No options found',

  // Group Tool Management
  'GROUP_TOOL_MANAGEMENT': 'Group Tool Management',
  'GROUP_TOOL_MANAGEMENT_DESCRIPTION': 'Manage tool groups for the application',
  'GROUP_NAME': 'Group Name',
  'GROUP_TOOL_DESCRIPTION': 'Description',
  'GROUP_TOOL_CODE': 'Code',
  'SEARCH_GROUP_NAME_PLACEHOLDER': 'Search by group name',
  'SEARCH_GROUP_CODE_PLACEHOLDER': 'Search by code',
  'CREATE_GROUP_TOOL': 'Create Group Tool',
  'EDIT_GROUP_TOOL': 'Edit Group Tool',
  'DELETE_GROUP_TOOL': 'Delete Group Tool',
  'DELETE_GROUP_TOOL_CONFIRM': 'Are you sure you want to delete the group tool \'{{name}}\'? This action cannot be undone.',
  'DELETE_GROUP_TOOL_SUCCESS': 'Group tool deleted successfully',
  'DELETE_GROUP_TOOL_ERROR': 'Failed to delete group tool',
  'NO_GROUP_TOOLS_FOUND': 'No group tools found',

  // Tools Management
  'TOOLS_MANAGEMENT': 'Tools Management',
  'TOOLS_MANAGEMENT_DESCRIPTION': 'Manage tools for the application',
  'TOOL_NAME': 'Name',
  'GROUPS': 'Groups',
  'ORGANIZATIONS': 'Organizations',
  'CATEGORIES': 'Categories',
  'INPUT_TYPE': 'Input Type',
  'VISIBLE': 'Visible',
  'SEARCH_TOOL_NAME_PLACEHOLDER': 'Search by name',
  'FILTER_BY_INPUT_TYPE': 'Filter by input type',
  'FILTER_BY_VISIBILITY': 'Filter by visibility',
  'FILTER_BY_INSTRUCTION': 'Filter by instruction',
  'CREATE_TOOL': 'Create Tool',
  'COPY_TOOL': 'Copy Tool',
  'EDIT_TOOL': 'Edit Tool',
  'DELETE_TOOL': 'Delete Tool',
  'DELETE_TOOL_CONFIRM': 'Are you sure you want to delete the tool \'{{name}}\'? This action cannot be undone.',
  'DELETE_TOOL_SUCCESS': 'Tool deleted successfully',
  'DELETE_TOOL_ERROR': 'Failed to delete tool',
  'COPY_TOOL_SUCCESS': 'Tool \'{{name}}\' copied successfully',
  'COPY_TOOL_ERROR': 'Failed to copy tool',
  'NO_TOOLS_FOUND': 'No tools found',

  // Explain Management
  'EXPLAIN_MANAGEMENT': 'Explain Management',
  'EXPLAIN_MANAGEMENT_DESCRIPTION': 'Manage explain templates for the application',
  'EXPLAIN_TYPE': 'Explain Type',
  'TASK_TYPE': 'Task Type',
  'EXPLAIN_RESPONSE_FORMAT': 'Response Format',
  'EXPLAIN_CREATED_AT': 'Created At',
  'SEARCH_EXPLAIN_NAME_PLACEHOLDER': 'Search by name',
  'FILTER_BY_GPT_MODEL': 'Filter by GPT model',
  'FILTER_BY_EXPLAIN_TYPE': 'Filter by explain type',
  'FILTER_BY_TASK_TYPE': 'Filter by task type',
  'CREATE_EXPLAIN': 'Create Explain',
  'EDIT_EXPLAIN': 'Edit Explain',
  'DELETE_EXPLAIN': 'Delete Explain',
  'DELETE_EXPLAIN_CONFIRM': 'Are you sure you want to delete the explain \'{{name}}\'? This action cannot be undone.',
  'DELETE_EXPLAIN_SUCCESS': 'Explain deleted successfully',
  'DELETE_EXPLAIN_ERROR': 'Failed to delete explain',
  'NO_EXPLAINS_FOUND': 'No explains found',

  // Instruction Management
  'INSTRUCTION_MANAGEMENT': 'Instruction Management',
  'INSTRUCTION_MANAGEMENT_DESCRIPTION': 'Manage instructions for the application',
  'CHAT_TYPE': 'Chat Type',
  'IS_SUB_INSTRUCTION': 'Is Sub Instruction',
  'YES': 'Yes',
  'NO': 'No',
  'SEARCH_INSTRUCTION_NAME_PLACEHOLDER': 'Search by name',
  'FILTER_BY_OUTPUT_TYPE': 'Filter by output type',
  'CREATE_INSTRUCTION': 'Create Instruction',
  'COPY_INSTRUCTION': 'Copy Instruction',
  'EDIT_INSTRUCTION': 'Edit Instruction',
  'DELETE_INSTRUCTION': 'Delete Instruction',
  'DELETE_INSTRUCTION_CONFIRM': 'Are you sure you want to delete the instruction \'{{name}}\'? This action cannot be undone.',
  'DELETE_INSTRUCTION_SUCCESS': 'Instruction deleted successfully',
  'DELETE_INSTRUCTION_ERROR': 'Failed to delete instruction',
  'COPY_INSTRUCTION_SUCCESS': 'Instruction \'{{name}}\' copied successfully',
  'COPY_INSTRUCTION_ERROR': 'Failed to copy instruction',
  'NO_INSTRUCTIONS_FOUND': 'No instructions found',

  // Voice Options
  'VOICE_OPTIONS_MANAGEMENT': 'Voice Options Management',
  'VOICE_OPTIONS_DESCRIPTION': 'Manage voice options for the application',
  'EDIT_VOICE_OPTION': 'Edit Voice Option',
  'DELETE_VOICE_OPTION': 'Delete Voice Option',
  'CREATE_VOICE_OPTION': 'Create Voice Option',
  'VOICE_OPTION_DESCRIPTION': 'Create or edit voice options for the application',
  'VOICE_FILE': 'Voice File',
  'VOICE_OPTION_CODE': 'Code',
  'IS_PUBLIC': 'Is Public',
  'NAME_REQUIRED': 'Name can\'t be blank!',
  'CODE_REQUIRED': 'Code can\'t be blank!',
  'IS_PUBLIC_REQUIRED': 'Is public can\'t be blank!',
  'ENTER_CODE': 'Enter code',
  'SELECT_IS_PUBLIC': 'Select is public',
  'DRAG_DROP_AUDIO': 'Drag and drop audio file here',
  'OR': 'or',
  'BROWSE_FILES': 'Browse Files',
  'SUPPORTED_FORMATS': 'Supported formats',
  'DELETE_FILE': 'Delete file',
  'LISTEN_PREVIEW': 'Listen preview',
  'NO_AUDIO': 'No audio',
  'AUDIO_PREVIEW': 'Audio Preview',
  'PLAY_AUDIO': 'Play',
  'PAUSE_AUDIO': 'Pause',
  'PLAYING': 'Playing...',
  'TOTAL_ITEMS': 'Total: {{total}} items',
  'DELETE_VOICE_OPTION_CONFIRM': 'Are you sure you want to delete this voice option?',
  'DELETE_VOICE_OPTION_CONFIRM_WITH_NAME': 'Are you sure you want to delete the voice option \'{{name}}\'? This action cannot be undone.',
  'DELETE_VOICE_OPTION_SUCCESS': 'Voice option deleted successfully',
  'DELETE_VOICE_OPTION_ERROR': 'Failed to delete voice option',
  'UPDATE_VOICE_OPTION_SUCCESS': 'Voice option updated successfully',
  'UPDATE_VOICE_OPTION_ERROR': 'Failed to update voice option',
  'CREATE_VOICE_OPTION_SUCCESS': 'Voice option created successfully',
  'CREATE_VOICE_OPTION_ERROR': 'Failed to create voice option',
  'UPLOADING_FILE': 'Uploading file...',
  'UPLOAD_FILE_SUCCESS': 'File uploaded successfully',
  'UPLOAD_FILE_ERROR': 'Failed to upload file',
  'SAVE_BEFORE_UPLOAD': 'Please save the voice option before uploading a file',
  'SEARCH_VOICE_OPTION_PLACEHOLDER': 'Search by name or code',
  'NO_VOICE_OPTIONS_FOUND': 'No voice options found'
};

// Vietnamese translations
export const adminAsideKeysVI = {
  // Group titles
  'CONTENT_MANAGEMENT': 'Quản lý nội dung',
  'USER_MANAGEMENT': 'Quản lý người dùng',
  'BILLING_AND_PROMOTION': 'Thanh toán & Khuyến mãi',
  'ANALYTICS_AND_REPORTING': 'Phân tích & Báo cáo',
  'SYSTEM_CONFIGURATION': 'Cấu hình hệ thống',

  // Content Management items
  'EXPLAIN': 'Giải thích',
  'GROUP_TOOL': 'Nhóm công cụ',
  'KNOWLEDGE': 'Kiến thức',
  'OPTIONS': 'Tùy chọn',
  'DICTATION_AND_SHADOWING': 'Dictation & Shadowing',
  'DICTATION_SHADOWING_DETAIL': 'Chi tiết Dictation & Shadowing',
  'SPEAKING_EXERCISE': 'Bài tập nói',
  'SPEAKING_EXERCISE_DETAIL': 'Chi tiết bài tập nói',
  'SPEAKING_EXERCISE_DETAIL_DESCRIPTION': 'Tạo và quản lý các bài tập nói IELTS với nhiều phần',

  // Speaking Exercise Part 2
  'SPEAKING_PART2_TITLE': 'Phần 2: Thẻ gợi ý',
  'SPEAKING_PART2_SUBTITLE': 'Thí sinh nói trong 1-2 phút về một chủ đề cho trước',
  'SPEAKING_PART2_CUE_CARD_TITLE': 'Thẻ gợi ý',
  'SPEAKING_PART2_CUE_CARD_SUBTITLE': 'Thêm nhiều thẻ gợi ý cho chủ đề này',
  'SPEAKING_PART2_CUE_CARD_PLACEHOLDER': 'Nhập nội dung thẻ gợi ý tại đây...',
  'RECREATE_AUDIO_TOOLTIP': 'Tạo lại âm thanh cho thẻ gợi ý này',
  'CREATE_AUDIO_TOOLTIP': 'Tạo âm thanh cho thẻ gợi ý này',
  'RECREATE_AUDIO': 'Tạo lại âm thanh',
  'CREATE_AUDIO': 'Tạo âm thanh',
  'DELETE_CUE_CARD_CONFIRM': 'Xóa thẻ gợi ý',
  'DELETE_CUE_CARD_DESCRIPTION': 'Bạn có chắc chắn muốn xóa thẻ gợi ý này không?',
  'DELETE_CUE_CARD_TOOLTIP': 'Xóa thẻ gợi ý này',
  'IELTS_CUE_CARD': 'Thẻ gợi ý IELTS',
  'ADD_CUE_CARD_TOOLTIP': 'Thêm thẻ gợi ý mới',
  'ADD_CUE_CARD': 'Thêm thẻ gợi ý',

  // User Management items
  'USER': 'Người dùng',
  'USER_TRACKING': 'Theo dõi người dùng',
  'WAITING_LIST': 'Danh sách chờ',
  'ORGANIZATION': 'Tổ chức',
  'PERSONA': 'Nhân vật',

  // Billing & Promotion items
  'DISCOUNT': 'Giảm giá',
  'PROMOTION': 'Khuyến mãi',

  // Analytics & Reporting items
  'AVERAGE_TOKEN': 'Token trung bình',
  'OPENAI_COST': 'Chi phí OpenAI',
  'OPENAI_COST_ANALYSIS': 'Phân tích chi phí OpenAI',
  'OPENAI_COST_ANALYSIS_DESCRIPTION': 'Theo dõi và phân tích chi phí sử dụng API OpenAI trên các công cụ và dịch vụ khác nhau',
  'TOTAL_TOKEN_BY_USER': 'Tổng token theo người dùng',
  'DAILY_ACTIVE_USERS': 'Người dùng hoạt động hàng ngày',
  'MONTHLY_ACTIVE_USERS': 'Người dùng hoạt động hàng tháng',

  // OpenAI Cost page
  'FILTER_OPTIONS': 'Tùy chọn lọc',
  'FILTER_OPTIONS_DESCRIPTION': 'Lọc dữ liệu chi phí theo khoảng thời gian và các tham số khác',
  'COST_ANALYSIS_VIEWS': 'Góc nhìn phân tích chi phí',
  'COST_ANALYSIS_VIEWS_DESCRIPTION': 'Các góc nhìn khác nhau để phân tích chi phí API OpenAI',
  'TOTAL_COST': 'Tổng chi phí',
  'COST_BY_TOOL': 'Chi phí theo công cụ',
  'COST_BY_INSTRUCTION': 'Chi phí theo hướng dẫn',
  'STUDENT_TOOLS_COST': 'Chi phí công cụ học viên',
  'STUDENT_TOOLS_COST_SUMMARY': 'Tổng quan chi phí công cụ học viên',
  'STUDENT_TOOLS_COST_BREAKDOWN': 'Phân tích chi phí công cụ học viên',
  'TOP_TOOLS_BY_COST': 'Công cụ có chi phí cao nhất',
  'TOTAL_TOOLS': 'Tổng số công cụ',
  'TOTAL_TOOLS_DESCRIPTION': 'Số lượng công cụ khác nhau được học viên sử dụng',
  'TOTAL_SUBMITS': 'Tổng số lần gửi',
  'TOTAL_SUBMITS_DESCRIPTION': 'Tổng số lần gửi trên tất cả các công cụ',
  'TOTAL_COST_DESCRIPTION': 'Tổng chi phí trên tất cả công cụ học viên',
  'SHOWING_TOP_10_OF': 'Hiển thị 10 trong số',
  'NO_STUDENT_TOOL_COST_DATA': 'Không có dữ liệu chi phí công cụ học viên',
  'NO_COST_DATA_FOUND': 'Không tìm thấy dữ liệu chi phí',
  'COST_OVERVIEW': 'Tổng quan chi phí',
  'COST_OVERVIEW_TOOLTIP': 'Tóm tắt tất cả chi phí API OpenAI',
  'COST_BREAKDOWN_BY_TYPE': 'Phân tích chi phí theo loại',
  'COST_BY_TOOL_TYPE': 'Chi phí theo loại công cụ',
  'AVERAGE_COST_PER_SUBMIT': 'Chi phí trung bình mỗi lần gửi',
  'AVERAGE_COST_PER_SUBMIT_DESCRIPTION': 'Chi phí trung bình cho mỗi lần gửi trên tất cả công cụ',
  'MEDIA_TOOLS_COST': 'Chi phí công cụ đa phương tiện',
  'TEXT_TOOLS_COST': 'Chi phí công cụ văn bản',
  'IMAGE_TOOLS_COST': 'Chi phí công cụ hình ảnh',
  'MEDIA_TOOLS_COST_DESCRIPTION': 'Tổng chi phí cho các công cụ xử lý âm thanh và video',
  'TEXT_TOOLS_COST_DESCRIPTION': 'Tổng chi phí cho các công cụ xử lý và tạo văn bản',
  'IMAGE_TOOLS_COST_DESCRIPTION': 'Tổng chi phí cho các công cụ tạo và xử lý hình ảnh',
  'MEDIA_TOOLS_SUBMITS': 'Số lần gửi công cụ đa phương tiện',
  'TEXT_TOOLS_SUBMITS': 'Số lần gửi công cụ văn bản',
  'IMAGE_TOOLS_SUBMITS': 'Số lần gửi công cụ hình ảnh',
  'OPENAI_COST_DISTRIBUTION': 'Phân bổ chi phí OpenAI',
  'OPENAI_COST_DISTRIBUTION_TOOLTIP': 'Phân bổ chi phí giữa các loại công cụ khác nhau',
  'USAGE_SUMMARY': 'Tổng quan sử dụng',
  'USAGE_SUMMARY_TOOLTIP': 'Tổng quan về việc sử dụng công cụ và số lần gửi',
  'SUBMITS': 'Lần gửi',
  'INPUT_TOKEN': 'Token đầu vào',
  'OUTPUT_TOKEN': 'Token đầu ra',
  'TOTAL_TOKEN': 'Tổng token',
  'COST': 'Chi phí',
  'ERROR_FETCHING_DATA': 'Lỗi khi tải dữ liệu. Vui lòng thử lại.',

  'COST_BY_TOOL_DETAIL': 'Chi tiết chi phí theo công cụ',
  'COST_BY_TOOL_DETAIL_TOOLTIP': 'Phân tích chi tiết chi phí theo từng công cụ',
  'TOOL': 'Công cụ',
  'NUMBER_OF_SUBMITS': 'Số lần gửi',
  'TOTAL_TOKENS': 'Tổng token',
  'OUTPUT_AVERAGE_TOKENS': 'Token đầu ra trung bình',
  'INPUT_AVERAGE_TOKENS': 'Token đầu vào trung bình',
  'AVERAGE_COST': 'Chi phí trung bình',

  // System Configuration items
  'SETTINGS': 'Cài đặt',
  'VOICE_OPTIONS': 'Tùy chọn giọng nói',

  // UserTracking Filter
  'USER_TYPE': 'Loại người dùng',
  'CREATED_DATE': 'Ngày tạo',
  'ACTIVITY_PERIOD': 'Thời gian hoạt động',
  'ENTER_FULL_NAME': 'Nhập họ tên',
  'SELECT_USER_TYPE': 'Chọn loại người dùng',
  'SELECT_TIME_PERIOD': 'Chọn khoảng thời gian',
  'FROM_DATE': 'Từ ngày',
  'TO_DATE': 'Đến ngày',
  'ONLY_ACTIVE_USER': 'Chỉ người dùng đang hoạt động',
  'CANNOT_USE_BOTH_FILTERS': 'Không thể sử dụng đồng thời bộ lọc thời gian hoạt động và ngày tạo',
  'CLEAR': 'Xóa',
  'SEARCH': 'Tìm kiếm',

  // UserMetrics
  'AVERAGE_DAILY_ACTIVE_USERS_DESC': 'Số lượng người dùng hoạt động trung bình mỗi ngày trong tháng hiện tại',
  'AVERAGE_USERS_PER_DAY': 'Người dùng trung bình mỗi ngày',
  'MONTHLY_ACTIVE_USERS_DESC': 'Tổng số người dùng đã hoạt động trong tháng hiện tại',
  'TOTAL_USERS_THIS_MONTH': 'Tổng người dùng trong tháng này',
  'USER_RETENTION_RATE': 'Tỷ lệ giữ chân người dùng',
  'USER_RETENTION_RATE_DESC': 'Phần trăm người dùng hoạt động hàng ngày so với người dùng hàng tháng',
  'DAILY_TO_MONTHLY_RATIO': 'Tỷ lệ người dùng hàng ngày so với hàng tháng',
  'USER_TRACKING_DESCRIPTION': 'Theo dõi hoạt động và chỉ số tương tác của người dùng',
  'USER_ACTIVITY_OVERVIEW': 'Tổng quan hoạt động người dùng',
  'NO_USER_DATA_FOUND': 'Không tìm thấy dữ liệu người dùng',
  'USER_STATUS': 'Trạng thái người dùng',
  'ACTIVE': 'Đang hoạt động',
  'ALL': 'Tất cả',
  'ALL_USERS': 'Tất cả người dùng',
  'ONLY_ACTIVE': 'Chỉ đang hoạt động',

  // DocumentTemplate Detail
  'DOCUMENT_TEMPLATE_DETAIL': 'Chi tiết mẫu tài liệu',
  'TEMPLATE_FILE': 'Tệp mẫu',
  'NO_FILE_SELECTED': 'Chưa chọn tệp',
  'VISIBILITY_SETTINGS': 'Cài đặt hiển thị',
  'DOCUMENT_OPTION': 'Tùy chọn tài liệu',
  'ADD_DOCUMENT_OPTION': 'Thêm tùy chọn tài liệu',
  'UPDATE_DOCUMENT_OPTION': 'Cập nhật tùy chọn tài liệu',
  'ENTER_FIELD_NAME': 'Nhập tên trường',
  'ENTER_FIELD_LABEL': 'Nhập nhãn trường',
  'HEADER_IMAGE': 'Hình ảnh tiêu đề',
  'UPLOAD_IMAGE': 'Tải lên hình ảnh',
  'FIELD_VALUES': 'Giá trị trường',
  'ENTER_VALUE': 'Nhập giá trị',
  'ADD': 'Thêm',
  'UPDATE': 'Cập nhật',
  'CHANGE': 'Thay đổi',
  'REMOVE': 'Xóa',

  // Settings Page
  'SYSTEM_SETTINGS': 'Cài đặt hệ thống',
  'SYSTEM_SETTINGS_DESCRIPTION': 'Cấu hình các thiết lập và tích hợp toàn hệ thống',
  'API_INTEGRATION': 'Tích hợp API',
  'USER_LIMITS': 'Giới hạn người dùng',
  'TOOLS_SETTINGS': 'Cài đặt công cụ',
  'NOTIFICATION_SETTINGS': 'Cài đặt thông báo',
  'DISPLAY_SETTINGS': 'Cài đặt hiển thị',
  'API_KEY_OPENAI': 'Khóa API OpenAI',
  'API_KEY_OPENAI_DESC': 'Khóa API cho tích hợp dịch vụ OpenAI',
  'API_KEY_OPENAI_REQUIRED': 'Khóa API OpenAI không được để trống!',
  'ENTER_API_KEY_OPENAI': 'Nhập khóa API OpenAI',
  'SPEECH_KEY': 'Khóa nhận dạng giọng nói',
  'SPEECH_KEY_DESC': 'Khóa cho dịch vụ nhận dạng giọng nói',
  'ENTER_SPEECH_KEY': 'Nhập khóa nhận dạng giọng nói',
  'SERVICE_REGION': 'Khu vực dịch vụ',
  'SERVICE_REGION_DESC': 'Khu vực cho dịch vụ giọng nói',
  'ENTER_SERVICE_REGION': 'Nhập khu vực dịch vụ',
  'AZURE_ENDPOINT': 'Điểm cuối Azure',
  'AZURE_ENDPOINT_DESC': 'URL điểm cuối cho dịch vụ Azure',
  'ENTER_AZURE_ENDPOINT': 'Nhập điểm cuối Azure',
  'USER_LIMIT': 'Giới hạn người dùng',
  'USER_LIMIT_DESC': 'Số lượng người dùng tối đa được phép trong hệ thống',
  'USER_LIMIT_REQUIRED': 'Giới hạn người dùng không được để trống!',
  'ENTER_USER_LIMIT': 'Nhập giới hạn người dùng',
  'FREE_SUBMISSION': 'Lượt gửi miễn phí',
  'FREE_SUBMISSION_DESC': 'Số lượt gửi miễn phí cho mỗi người dùng',
  'ENTER_FREE_SUBMISSION': 'Nhập số lượt gửi miễn phí',
  'PAID_SUBMISSION': 'Lượt gửi trả phí',
  'PAID_SUBMISSION_DESC': 'Số lượt gửi trả phí cho mỗi người dùng',
  'ENTER_PAID_SUBMISSION': 'Nhập số lượt gửi trả phí',
  'STUDENT_TOOLS': 'Công cụ học viên',
  'STUDENT_TOOLS_DESC': 'Các công cụ có sẵn cho học viên sử dụng',
  'STUDENT_TOOLS_REQUIRED': 'Công cụ học viên không được để trống!',
  'SELECT_STUDENT_TOOLS': 'Chọn công cụ học viên',
  'TELEGRAM_BOT_TOKEN': 'Token Bot Telegram',
  'TELEGRAM_BOT_TOKEN_DESC': 'Token cho tích hợp bot Telegram',
  'ENTER_TELEGRAM_BOT_TOKEN': 'Nhập token bot Telegram',
  'TELEGRAM_CHAT_ID': 'ID Chat Telegram',
  'TELEGRAM_CHAT_ID_DESC': 'ID chat cho thông báo Telegram',
  'ENTER_TELEGRAM_CHAT_ID': 'Nhập ID chat Telegram',
  'AUTO_SHOW_FEEDBACK': 'Tự động hiển thị phản hồi',
  'AUTO_SHOW_FEEDBACK_DESC': 'Tự động hiển thị phản hồi cho người dùng',
  'SHOW_CHAT_BOT': 'Hiển thị Chat Bot',
  'SHOW_CHAT_BOT_DESC': 'Hiển thị chat bot trên giao diện',
  'CHAT_BOT_ID': 'ID Chat Bot',
  'CHAT_BOT_ID_DESC': 'ID cho tích hợp chat bot',
  'ENTER_CHAT_BOT_ID': 'Nhập ID chat bot',
  'CONFIRM_UPDATE_SETTINGS': 'Xác nhận cập nhật cài đặt',
  'CONFIRM_UPDATE_SENSITIVE_SETTINGS': 'Bạn sắp cập nhật các khóa API nhạy cảm. Bạn có chắc chắn muốn tiếp tục không?',
  'UPDATE_SETTING_SUCCESS': 'Cập nhật cài đặt thành công',
  'UPDATE_SETTING_ERROR': 'Cập nhật cài đặt thất bại',

  // Persona Management
  'PERSONA_MANAGEMENT': 'Quản lý Persona',
  'PERSONA_MANAGEMENT_DESCRIPTION': 'Quản lý persona người dùng và các mẫu, công cụ liên kết',
  'SEARCH_PERSONA_PLACEHOLDER': 'Tìm kiếm persona theo tên',
  'NO_PERSONA_FOUND': 'Không tìm thấy persona nào',
  'CREATE_PERSONA': 'Tạo Persona',
  'EDIT_PERSONA': 'Chỉnh sửa Persona',
  'DELETE_PERSONA': 'Xóa Persona',
  'DELETE_PERSONA_CONFIRM': 'Bạn có chắc chắn muốn xóa persona \'{{persona}}\'? Hành động này không thể hoàn tác.',
  'DELETE_PERSONA_SUCCESSFULLY': 'Xóa persona thành công',
  'DELETE_PERSONA_ERROR': 'Xóa persona thất bại',
  'PERSONA_DETAIL': 'Chi tiết Persona',
  'PERSONA_DETAIL_DESCRIPTION': 'Xem và chỉnh sửa chi tiết persona, mẫu và tùy chọn công cụ',
  'CREATE_PERSONA_DESCRIPTION': 'Tạo một persona mới với mẫu và tùy chọn công cụ',
  'BASIC_INFORMATION': 'Thông tin cơ bản',
  'PERSONA_NAME': 'Tên Persona',
  'PERSONA_NAME_DESC': 'Tên của persona sẽ được hiển thị cho người dùng',
  'PERSONA_NAME_REQUIRED': 'Tên persona là bắt buộc',
  'SELECT_PERSONA_NAME': 'Chọn tên persona',
  'TEMPLATE': 'Mẫu',
  'TEMPLATE_DESC': 'Các mẫu liên kết với persona này',
  'TEMPLATE_REQUIRED': 'Mẫu là bắt buộc',
  'SELECT_TEMPLATE': 'Chọn mẫu',
  'TOOL_OPTIONS': 'Tùy chọn công cụ',
  'TOOL_OPTIONS_DESC': 'Cấu hình các công cụ có sẵn cho persona này',
  'OPTION_TYPE': 'Loại tùy chọn',
  'OPTION_TYPE_DESC': 'Loại tùy chọn công cụ',
  'OPTION_TYPE_REQUIRED': 'Loại tùy chọn là bắt buộc',
  'SELECT_OPTION_TYPE': 'Chọn loại tùy chọn',
  'TOOLS': 'Công cụ',
  'TOOLS_DESC': 'Các công cụ có sẵn cho loại tùy chọn này',
  'TOOLS_REQUIRED': 'Công cụ là bắt buộc',
  'SELECT_TOOLS': 'Chọn công cụ',
  'ADD_OPTION': 'Thêm tùy chọn',
  'REMOVE_OPTION': 'Xóa tùy chọn',
  'CONFIRM_LEAVE': 'Xác nhận rời đi',
  'CONFIRM_LEAVE_DESCRIPTION': 'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời đi không?',
  'LEAVE': 'Rời đi',
  'STAY': 'Ở lại',
  'CREATE_PERSONA_SUCCESSFULLY': 'Tạo persona thành công',
  'CREATE_PERSONA_ERROR': 'Tạo persona thất bại',
  'UPDATE_PERSONA_SUCCESSFULLY': 'Cập nhật persona thành công',
  'UPDATE_PERSONA_ERROR': 'Cập nhật persona thất bại',

  // API Key Management
  'API_KEY_MANAGEMENT': 'Quản lý API Key',
  'API_KEY_MANAGEMENT_DESCRIPTION': 'Quản lý các khóa API cho tích hợp dịch vụ bên ngoài',
  'API_KEY': 'Khóa API',
  'MODEL_INTERFACE': 'Giao diện mô hình',
  'URL': 'Đường dẫn',
  'CREATE_API_KEY': 'Tạo khóa API',
  'EDIT_API_KEY': 'Chỉnh sửa khóa API',
  'DELETE_API_KEY': 'Xóa khóa API',
  'CONFIRM_DELETE_API_KEY': 'Bạn có chắc chắn muốn xóa khóa API này? Hành động này không thể hoàn tác.',
  'DELETE_API_KEY_SUCCESS': 'Xóa khóa API thành công',
  'DELETE_API_KEY_ERROR': 'Xóa khóa API thất bại',
  'CREATE_API_KEY_SUCCESS': 'Tạo khóa API thành công',
  'CREATE_API_KEY_ERROR': 'Tạo khóa API thất bại',
  'UPDATE_API_KEY_SUCCESS': 'Cập nhật khóa API thành công',
  'UPDATE_API_KEY_ERROR': 'Cập nhật khóa API thất bại',
  'SEARCH_MODEL_INTERFACE_PLACEHOLDER': 'Tìm kiếm giao diện mô hình',
  'NO_API_KEY_FOUND': 'Không tìm thấy khóa API nào',
  'ENDPOINT': 'Endpoint',
  'REGION': 'Region',
  'AWS_ACCESS_KEY': 'AWS Access Key',
  'AWS_SECRET_KEY': 'AWS Secret Key',
  'AWS_REGION': 'AWS Region',

  // Modal AI Management
  'MODAL_AI': 'Mô hình AI',
  'MODAL_AI_MANAGEMENT': 'Quản lý mô hình AI',
  'MODAL_AI_MANAGEMENT_DESCRIPTION': 'Quản lý các mô hình AI và cấu hình giá của chúng',
  'MODAL_NAME': 'Tên mô hình',
  'API_KEYS': 'Khóa API',
  'TOKEN_UNIT': 'Đơn vị token',
  'UNIT': 'Đơn vị',
  'INPUT_PRICE': 'Giá đầu vào',
  'OUTPUT_PRICE': 'Giá đầu ra',
  'MAX_TOKENS': 'Token tối đa',
  'CREATE_MODAL_AI': 'Tạo mô hình AI',
  'EDIT_MODAL_AI': 'Chỉnh sửa mô hình AI',
  'DELETE_MODAL_AI': 'Xóa mô hình AI',
  'CONFIRM_DELETE_MODAL_AI': 'Bạn có chắc chắn muốn xóa mô hình \'{{modal}}\'? Hành động này không thể hoàn tác.',
  'DELETE_MODAL_AI_SUCCESS': 'Xóa mô hình AI thành công',
  'DELETE_MODAL_AI_ERROR': 'Xóa mô hình AI thất bại',
  'CREATE_MODAL_AI_SUCCESS': 'Tạo mô hình AI thành công',
  'CREATE_MODAL_ERROR': 'Tạo mô hình AI thất bại',
  'UPDATE_MODAL_SUCCESS': 'Cập nhật mô hình AI thành công',
  'UPDATE_MODAL_ERROR': 'Cập nhật mô hình AI thất bại',
  'SEARCH_MODAL_NAME_PLACEHOLDER': 'Tìm kiếm theo tên mô hình',
  'NO_MODAL_AI_FOUND': 'Không tìm thấy mô hình AI nào',

  // Support Business Management
  'SUPPORT_BUSINESS': 'Hỗ trợ doanh nghiệp',
  'SUPPORT_BUSINESS_DESCRIPTION': 'Quản lý các yêu cầu hỗ trợ doanh nghiệp và trạng thái của chúng',
  'COMPANY_NAME': 'Tên công ty',
  'CONTACT_NAME': 'Tên liên hệ',
  'PHONE_NUMBER': 'Số điện thoại',
  'HAS_SUPPORT': 'Đã hỗ trợ',
  'ADD_SUPPORT': 'Thêm hỗ trợ',
  'REMOVE_SUPPORT': 'Hủy hỗ trợ',
  'UPDATE_SUPPORT_BUSINESS_SUCCESS': 'Cập nhật trạng thái hỗ trợ thành công',
  'UPDATE_SUPPORT_BUSINESS_ERROR': 'Cập nhật trạng thái hỗ trợ thất bại',
  'SEARCH_CONTACT_NAME_PLACEHOLDER': 'Tìm kiếm theo tên liên hệ',
  'SEARCH_COMPANY_NAME_PLACEHOLDER': 'Tìm kiếm theo tên công ty',
  'FILTER_BY_SUPPORT_STATUS': 'Lọc theo trạng thái hỗ trợ',
  'NO_SUPPORT_BUSINESS_FOUND': 'Không tìm thấy yêu cầu hỗ trợ doanh nghiệp nào',

  // Feedback Management
  'FEEDBACK_ANALYSIS': 'Phân tích phản hồi',
  'FEEDBACK_ANALYSIS_DESCRIPTION': 'Phân tích phản hồi và đánh giá của người dùng cho các tính năng khác nhau',
  'FEEDBACK_STATISTICS': 'Thống kê phản hồi',
  'FEEDBACK_STATISTICS_DESCRIPTION': 'Xem thống kê chi tiết và phản hồi cá nhân từ người dùng',
  'FEATURE': 'Tính năng',
  'SEARCH_FEATURE_PLACEHOLDER': 'Tìm kiếm theo tên tính năng',
  'SELECT_GROUP_FEEDBACK': 'Chọn nhóm phản hồi',
  'SELECT_TIME': 'Chọn khoảng thời gian',
  'THIS_WEEK': 'Tuần này',
  'THIS_MONTH': 'Tháng này',
  'CUSTOM': 'Tùy chỉnh',
  'SELECT_FROM_DATE': 'Chọn từ ngày',
  'SELECT_TO_DATE': 'Chọn đến ngày',
  'CAN_NOT_BE_BLANK': 'Không được để trống',
  'SELECT_SATISFACTION_LEVEL': 'Chọn mức độ hài lòng',
  'SELECT_USEFULNESS_LEVEL': 'Chọn mức độ hữu ích',
  'UPGRADE_INTEREST': 'Quan tâm nâng cấp',
  'RECOMMEND_TO_OTHERS': 'Giới thiệu cho người khác',
  'VERY_BAD': 'Rất tệ',
  'BAD': 'Tệ',
  'AVERAGE': 'Trung bình',
  'GOOD': 'Tốt',
  'EXCELLENT': 'Xuất sắc',
  'NO_FEEDBACK_ANALYSIS_FOUND': 'Không tìm thấy phân tích phản hồi nào',
  'NO_FEEDBACK_FOUND': 'Không tìm thấy phản hồi nào',

  // Package Management
  'PACKAGE_SETTING': 'Cài đặt gói dịch vụ',
  'PACKAGE_SETTING_DESCRIPTION': 'Quản lý các gói đăng ký và tùy chọn giá',
  'PACKAGE_NAME': 'Tên gói',
  'PRICE': 'Giá',
  'CUSTOMER': 'Khách hàng',
  'ORDER': 'Thứ tự',
  'LAST_MODIFIED': 'Chỉnh sửa lần cuối',
  'CREATE_PACKAGE': 'Tạo gói',
  'EDIT_PACKAGE': 'Chỉnh sửa gói',
  'DELETE_PACKAGE': 'Xóa gói',
  'DELETE_PACKAGE_CONFIRM': 'Bạn có chắc chắn muốn xóa gói \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_PACKAGE_SUCCESS': 'Xóa gói thành công',
  'DELETE_PACKAGE_ERROR': 'Xóa gói thất bại',
  'SEARCH_PACKAGE_PLACEHOLDER': 'Tìm kiếm theo tên, mô tả hoặc loại',
  'NO_PACKAGES_FOUND': 'Không tìm thấy gói nào',
  'MONTH': 'Tháng',
  'YEAR': 'Năm',

  // Promotion Management
  'PROMOTION_MANAGEMENT': 'Quản lý khuyến mãi',
  'PROMOTION_MANAGEMENT_DESCRIPTION': 'Quản lý mã khuyến mãi và giảm giá cho các gói',
  'DISPLAY_TEXT': 'Văn bản hiển thị',
  'SEARCH_DISPLAY_TEXT_PLACEHOLDER': 'Tìm kiếm theo văn bản hiển thị',
  'SELECT_PACKAGE': 'Chọn gói',
  'IS_ACTIVATE': 'Kích hoạt',
  'START_DATE': 'Ngày bắt đầu',
  'END_DATE': 'Ngày kết thúc',
  'CREATE_PROMOTION': 'Tạo khuyến mãi',
  'EDIT_PROMOTION': 'Chỉnh sửa khuyến mãi',
  'DELETE_PROMOTION': 'Xóa khuyến mãi',
  'DELETE_PROMOTION_CONFIRM': 'Bạn có chắc chắn muốn xóa khuyến mãi \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_PROMOTION_SUCCESS': 'Xóa khuyến mãi thành công',
  'DELETE_PROMOTION_ERROR': 'Xóa khuyến mãi thất bại',
  'UPDATE_PROMOTION_SUCCESS': 'Cập nhật khuyến mãi thành công',
  'UPDATE_PROMOTION_ERROR': 'Cập nhật khuyến mãi thất bại',
  'CREATE_PROMOTION_SUCCESS': 'Tạo khuyến mãi thành công',
  'CREATE_PROMOTION_ERROR': 'Tạo khuyến mãi thất bại',
  'NO_PROMOTIONS_FOUND': 'Không tìm thấy khuyến mãi nào',

  // Discount Management
  'DISCOUNT_MANAGEMENT': 'Quản lý mã giảm giá',
  'DISCOUNT_MANAGEMENT_DESCRIPTION': 'Quản lý mã giảm giá và ưu đãi đặc biệt',
  'DISCOUNT_CODE': 'Mã giảm giá',
  'SEARCH_DISCOUNT_CODE_PLACEHOLDER': 'Tìm kiếm theo mã giảm giá',
  'INACTIVE': 'Không hoạt động',
  'DISCOUNT_LEVEL': 'Mức giảm giá',
  'NUMBER_OF_USED': 'Số lần sử dụng',
  'LIMIT': 'Giới hạn',
  'SELECT_TYPE': 'Chọn loại',
  'SELECT_START_DATE': 'Chọn ngày bắt đầu',
  'SELECT_END_DATE': 'Chọn ngày kết thúc',
  'CREATE_DISCOUNT': 'Tạo mã giảm giá',
  'UPDATE_DISCOUNT': 'Cập nhật mã giảm giá',
  'EDIT_DISCOUNT': 'Chỉnh sửa mã giảm giá',
  'DELETE_DISCOUNT': 'Xóa mã giảm giá',
  'DELETE_DISCOUNT_CONFIRM': 'Bạn có chắc chắn muốn xóa mã giảm giá \'{{code}}\'? Hành động này không thể hoàn tác.',
  'DELETE_DISCOUNT_SUCCESS': 'Xóa mã giảm giá thành công',
  'DELETE_DISCOUNT_ERROR': 'Xóa mã giảm giá thất bại',
  'UPDATE_DISCOUNT_SUCCESS': 'Cập nhật mã giảm giá thành công',
  'UPDATE_DISCOUNT_ERROR': 'Cập nhật mã giảm giá thất bại',
  'CREATE_DISCOUNT_SUCCESS': 'Tạo mã giảm giá thành công',
  'CREATE_DISCOUNT_ERROR': 'Tạo mã giảm giá thất bại',
  'NO_DISCOUNTS_FOUND': 'Không tìm thấy mã giảm giá nào',

  // Wait List Management
  'WAIT_LIST': 'Danh sách chờ',
  'WAIT_LIST_DESCRIPTION': 'Quản lý người dùng trong danh sách chờ truy cập vào nền tảng',
  'EMAIL': 'Email',
  'FULL_NAME': 'Họ tên',
  'SEARCH_EMAIL_PLACEHOLDER': 'Tìm kiếm theo email',
  'SEARCH_FULL_NAME_PLACEHOLDER': 'Tìm kiếm theo họ tên',
  'MOVE_TO_WHITELIST': 'Chuyển vào danh sách trắng',
  'MOVE_TO_WHITELIST_SUCCESS': 'Chuyển người dùng vào danh sách trắng thành công',
  'MOVE_TO_WHITELIST_ERROR': 'Chuyển người dùng vào danh sách trắng thất bại',
  'NO_WAIT_LIST_FOUND': 'Không tìm thấy người dùng nào trong danh sách chờ',

  // Whitelist Management
  'WHITELIST': 'Danh sách trắng',
  'WHITELIST_DESCRIPTION': 'Quản lý người dùng được phê duyệt truy cập vào nền tảng',
  'ADD_WHITELIST': 'Thêm vào danh sách trắng',
  'DELETE_WHITELIST': 'Xóa khỏi danh sách trắng',
  'DELETE_WHITELIST_CONFIRM': 'Bạn có chắc chắn muốn xóa \'{{email}}\' khỏi danh sách trắng? Hành động này không thể hoàn tác.',
  'DELETE_WHITELIST_SUCCESS': 'Xóa người dùng khỏi danh sách trắng thành công',
  'DELETE_WHITELIST_ERROR': 'Xóa người dùng khỏi danh sách trắng thất bại',
  'ADD_TO_WHITELIST_SUCCESS': 'Thêm người dùng vào danh sách trắng thành công',
  'ADD_TO_WHITELIST_ERROR': 'Thêm người dùng vào danh sách trắng thất bại',
  'EMAIL_CAN_NOT_BE_BLANK': 'Email không được để trống',
  'INVALID_EMAIL': 'Định dạng email không hợp lệ',
  'ENTER_EMAIL': 'Nhập địa chỉ email',
  'NO_WHITELIST_FOUND': 'Không tìm thấy người dùng nào trong danh sách trắng',

  // Organization Management
  'ORGANIZATION_MANAGEMENT': 'Quản lý tổ chức',
  'ORGANIZATION_MANAGEMENT_DESCRIPTION': 'Quản lý các tổ chức và quyền truy cập của họ vào nền tảng',
  'ORGANIZATION_NAME': 'Tên tổ chức',
  'SEARCH_ORGANIZATION_NAME_PLACEHOLDER': 'Tìm kiếm theo tên tổ chức',
  'CREATE_ORGANIZATION': 'Tạo tổ chức',
  'EDIT_ORGANIZATION': 'Chỉnh sửa tổ chức',
  'DELETE_ORGANIZATION': 'Xóa tổ chức',
  'CONFIRM_DELETE_ORGANIZATION': 'Bạn có chắc chắn muốn xóa tổ chức này?',
  'CONFIRM_DELETE_ORGANIZATION_WITH_NAME': 'Bạn có chắc chắn muốn xóa tổ chức \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_ORGANIZATION_SUCCESS': 'Xóa tổ chức thành công',
  'DELETE_ORGANIZATION_ERROR': 'Xóa tổ chức thất bại',
  'LOCK': 'Khóa',
  'UNLOCK': 'Mở khóa',
  'CONFIRM_LOCK_ORGANIZATION': 'Bạn có chắc chắn muốn khóa tổ chức này?',
  'CONFIRM_UNLOCK_ORGANIZATION': 'Bạn có chắc chắn muốn mở khóa tổ chức này?',
  'LOCKED': 'Đã khóa',
  'ORGANIZATION_ACTIVE': 'Hoạt động',
  'NO_ORGANIZATIONS_FOUND': 'Không tìm thấy tổ chức nào',

  // Document Template Management
  'DOCUMENT_TEMPLATE': 'Mẫu tài liệu',
  'DOCUMENT_TEMPLATE_DESCRIPTION': 'Quản lý các mẫu tài liệu cho ứng dụng',
  'PUBLIC': 'Công khai',
  'PRIVATE': 'Riêng tư',
  'SEARCH_TEMPLATE_NAME_PLACEHOLDER': 'Tìm kiếm theo tên mẫu',
  'CREATE_DOCUMENT_TEMPLATE': 'Tạo mẫu tài liệu',
  'EDIT_DOCUMENT_TEMPLATE': 'Chỉnh sửa mẫu tài liệu',
  'DELETE_DOCUMENT_TEMPLATE': 'Xóa mẫu tài liệu',
  'DELETE_DOCUMENT_TEMPLATE_CONFIRM': 'Bạn có chắc chắn muốn xóa mẫu tài liệu \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_DOCUMENT_TEMPLATE_SUCCESS': 'Xóa mẫu tài liệu thành công',
  'DELETE_DOCUMENT_TEMPLATE_ERROR': 'Xóa mẫu tài liệu thất bại',
  'CREATE_DOCUMENT_TEMPLATE_SUCCESS': 'Tạo mẫu tài liệu thành công',
  'CREATE_DOCUMENT_TEMPLATE_ERROR': 'Tạo mẫu tài liệu thất bại',
  'NO_DOCUMENT_TEMPLATES_FOUND': 'Không tìm thấy mẫu tài liệu nào',

  // Speaking Exercise Management
  'SPEAKING_EXERCISE_MANAGEMENT': 'Quản lý bài tập nói',
  'SPEAKING_EXERCISE_MANAGEMENT_DESCRIPTION': 'Quản lý các bài tập nói cho luyện thi IELTS',
  'TITLE': 'Tiêu đề',
  'TOPIC': 'Chủ đề',
  'EXERCISE_TITLE_TOOLTIP': 'Tiêu đề của bài tập nói',
  'EXERCISE_TOPIC_TOOLTIP': 'Chủ đề chính hoặc nội dung của bài tập nói',
  'STATUS': 'Trạng thái',
  'CREATED_BY': 'Tạo bởi',
  'SEARCH_TITLE_PLACEHOLDER': 'Tìm kiếm theo tiêu đề',
  'SEARCH_TOPIC_PLACEHOLDER': 'Tìm kiếm theo chủ đề',
  'ENTER_TOPIC': 'Nhập chủ đề',
  'FILTER_BY_STATUS': 'Lọc theo trạng thái',
  'CREATE_EXERCISE': 'Tạo bài tập',
  'EDIT_EXERCISE': 'Chỉnh sửa bài tập',
  'DELETE_EXERCISE': 'Xóa bài tập',
  'DELETE_EXERCISE_CONFIRM': 'Bạn có chắc chắn muốn xóa bài tập \'{{title}}\'? Hành động này không thể hoàn tác.',
  'DELETE_EXERCISE_SUCCESS': 'Xóa bài tập thành công',
  'DELETE_EXERCISE_ERROR': 'Xóa bài tập thất bại',
  'CREATE_EXERCISE_SUCCESS': 'Tạo bài tập thành công',
  'CREATE_EXERCISE_ERROR': 'Tạo bài tập thất bại',
  'NO_EXERCISES_FOUND': 'Không tìm thấy bài tập nào',

  // Dictation and Shadowing Management
  'DICTATION_SHADOWING_MANAGEMENT': 'Quản lý Dictation & Shadowing',
  'DICTATION_SHADOWING_MANAGEMENT_DESCRIPTION': 'Quản lý các bài tập Dictation và Shadowing cho luyện tập ngôn ngữ',
  'DIFFICULTY': 'Độ khó',
  'TIME_LIMIT': 'Giới hạn thời gian',
  'SEARCH_EXERCISE_NAME_PLACEHOLDER': 'Tìm kiếm theo tên',
  'FILTER_BY_DIFFICULTY': 'Lọc theo độ khó',
  'FILTER_BY_TYPE': 'Lọc theo loại',

  // Output Type Management
  'OUTPUT_TYPE_MANAGEMENT': 'Quản lý Loại Đầu Ra',
  'OUTPUT_TYPE_MANAGEMENT_DESCRIPTION': 'Quản lý các loại đầu ra và định dạng phản hồi cho ứng dụng',
  'SCHEMA': 'Cấu trúc',
  'SEARCH_OUTPUT_TYPE_PLACEHOLDER': 'Tìm kiếm theo tên',
  'SEARCH_RESPONSE_FORMAT_PLACEHOLDER': 'Tìm kiếm theo định dạng phản hồi',
  'CREATE_OUTPUT_TYPE': 'Tạo loại đầu ra',
  'EDIT_OUTPUT_TYPE': 'Chỉnh sửa loại đầu ra',
  'DELETE_OUTPUT_TYPE': 'Xóa loại đầu ra',
  'DELETE_OUTPUT_TYPE_CONFIRM': 'Bạn có chắc chắn muốn xóa loại đầu ra \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_OUTPUT_TYPE_SUCCESS': 'Xóa loại đầu ra thành công',
  'DELETE_OUTPUT_TYPE_ERROR': 'Xóa loại đầu ra thất bại',
  'CREATE_OUTPUT_TYPE_SUCCESS': 'Tạo loại đầu ra thành công',
  'CREATE_OUTPUT_TYPE_ERROR': 'Tạo loại đầu ra thất bại',
  'UPDATE_OUTPUT_TYPE_SUCCESS': 'Cập nhật loại đầu ra thành công',
  'UPDATE_OUTPUT_TYPE_ERROR': 'Cập nhật loại đầu ra thất bại',
  'NO_OUTPUT_TYPES_FOUND': 'Không tìm thấy loại đầu ra nào',

  // Options Management
  'OPTIONS_MANAGEMENT': 'Quản lý Tùy chọn',
  'OPTIONS_MANAGEMENT_DESCRIPTION': 'Quản lý các tùy chọn và loại đầu vào cho ứng dụng',
  'PLACEHOLDER': 'Placeholder',
  'DEFAULT_VALUE': 'Giá trị mặc định',
  'TYPE': 'Loại',
  'INSTRUCTION': 'Hướng dẫn',
  'SEARCH_OPTION_NAME_PLACEHOLDER': 'Tìm kiếm theo tên',
  'SEARCH_OPTION_CODE_PLACEHOLDER': 'Tìm kiếm theo mã',
  'CREATE_OPTION': 'Tạo tùy chọn',
  'EDIT_OPTION': 'Chỉnh sửa tùy chọn',
  'DELETE_OPTION': 'Xóa tùy chọn',
  'DELETE_OPTION_CONFIRM': 'Bạn có chắc chắn muốn xóa tùy chọn \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_OPTION_SUCCESS': 'Xóa tùy chọn thành công',
  'DELETE_OPTION_ERROR': 'Xóa tùy chọn thất bại',
  'NO_OPTIONS_FOUND': 'Không tìm thấy tùy chọn nào',

  // Group Tool Management
  'GROUP_TOOL_MANAGEMENT': 'Quản lý Nhóm Công cụ',
  'GROUP_TOOL_MANAGEMENT_DESCRIPTION': 'Quản lý các nhóm công cụ cho ứng dụng',
  'GROUP_NAME': 'Tên nhóm',
  'DESCRIPTION': 'Mô tả',
  'CODE': 'Mã',
  'SEARCH_GROUP_NAME_PLACEHOLDER': 'Tìm kiếm theo tên nhóm',
  'SEARCH_GROUP_CODE_PLACEHOLDER': 'Tìm kiếm theo mã',
  'CREATE_GROUP_TOOL': 'Tạo nhóm công cụ',
  'EDIT_GROUP_TOOL': 'Chỉnh sửa nhóm công cụ',
  'DELETE_GROUP_TOOL': 'Xóa nhóm công cụ',
  'DELETE_GROUP_TOOL_CONFIRM': 'Bạn có chắc chắn muốn xóa nhóm công cụ \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_GROUP_TOOL_SUCCESS': 'Xóa nhóm công cụ thành công',
  'DELETE_GROUP_TOOL_ERROR': 'Xóa nhóm công cụ thất bại',
  'NO_GROUP_TOOLS_FOUND': 'Không tìm thấy nhóm công cụ nào',

  // Tools Management
  'TOOLS_MANAGEMENT': 'Quản lý Công cụ',
  'TOOLS_MANAGEMENT_DESCRIPTION': 'Quản lý các công cụ cho ứng dụng',
  'NAME': 'Tên',
  'GROUPS': 'Nhóm',
  'ORGANIZATIONS': 'Tổ chức',
  'CATEGORIES': 'Danh mục',
  'INPUT_TYPE': 'Loại đầu vào',
  'TOOL_INSTRUCTION': 'Hướng dẫn',
  'VISIBLE': 'Hiển thị',
  'SEARCH_TOOL_NAME_PLACEHOLDER': 'Tìm kiếm theo tên',
  'FILTER_BY_INPUT_TYPE': 'Lọc theo loại đầu vào',
  'FILTER_BY_VISIBILITY': 'Lọc theo trạng thái hiển thị',
  'FILTER_BY_INSTRUCTION': 'Lọc theo hướng dẫn',
  'CREATE_TOOL': 'Tạo công cụ',
  'COPY_TOOL': 'Sao chép công cụ',
  'EDIT_TOOL': 'Chỉnh sửa công cụ',
  'DELETE_TOOL': 'Xóa công cụ',
  'DELETE_TOOL_CONFIRM': 'Bạn có chắc chắn muốn xóa công cụ \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_TOOL_SUCCESS': 'Xóa công cụ thành công',
  'DELETE_TOOL_ERROR': 'Xóa công cụ thất bại',
  'COPY_TOOL_SUCCESS': 'Sao chép công cụ \'{{name}}\' thành công',
  'COPY_TOOL_ERROR': 'Sao chép công cụ thất bại',
  'NO_TOOLS_FOUND': 'Không tìm thấy công cụ nào',

  // Explain Management
  'EXPLAIN_MANAGEMENT': 'Quản lý Giải thích',
  'EXPLAIN_MANAGEMENT_DESCRIPTION': 'Quản lý các mẫu giải thích cho ứng dụng',
  'GPT_MODEL': 'Mô hình GPT',
  'EXPLAIN_TYPE': 'Loại giải thích',
  'TASK_TYPE': 'Loại nhiệm vụ',
  'RESPONSE_FORMAT': 'Định dạng phản hồi',
  'CREATED_AT': 'Ngày tạo',
  'SEARCH_EXPLAIN_NAME_PLACEHOLDER': 'Tìm kiếm theo tên',
  'FILTER_BY_GPT_MODEL': 'Lọc theo mô hình GPT',
  'FILTER_BY_EXPLAIN_TYPE': 'Lọc theo loại giải thích',
  'FILTER_BY_TASK_TYPE': 'Lọc theo loại nhiệm vụ',
  'CREATE_EXPLAIN': 'Tạo giải thích',
  'EDIT_EXPLAIN': 'Chỉnh sửa giải thích',
  'DELETE_EXPLAIN': 'Xóa giải thích',
  'DELETE_EXPLAIN_CONFIRM': 'Bạn có chắc chắn muốn xóa giải thích \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_EXPLAIN_SUCCESS': 'Xóa giải thích thành công',
  'DELETE_EXPLAIN_ERROR': 'Xóa giải thích thất bại',
  'NO_EXPLAINS_FOUND': 'Không tìm thấy giải thích nào',

  // Instruction Management
  'INSTRUCTION_MANAGEMENT': 'Quản lý Hướng dẫn',
  'INSTRUCTION_MANAGEMENT_DESCRIPTION': 'Quản lý các hướng dẫn cho ứng dụng',
  'CHAT_TYPE': 'Loại trò chuyện',
  'OUTPUT_TYPE': 'Loại đầu ra',
  'IS_SUB_INSTRUCTION': 'Là hướng dẫn con',
  'YES': 'Có',
  'NO': 'Không',
  'SEARCH_INSTRUCTION_NAME_PLACEHOLDER': 'Tìm kiếm theo tên',
  'FILTER_BY_OUTPUT_TYPE': 'Lọc theo loại đầu ra',
  'CREATE_INSTRUCTION': 'Tạo hướng dẫn',
  'COPY_INSTRUCTION': 'Sao chép hướng dẫn',
  'EDIT_INSTRUCTION': 'Chỉnh sửa hướng dẫn',
  'DELETE_INSTRUCTION': 'Xóa hướng dẫn',
  'DELETE_INSTRUCTION_CONFIRM': 'Bạn có chắc chắn muốn xóa hướng dẫn \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_INSTRUCTION_SUCCESS': 'Xóa hướng dẫn thành công',
  'DELETE_INSTRUCTION_ERROR': 'Xóa hướng dẫn thất bại',
  'COPY_INSTRUCTION_SUCCESS': 'Sao chép hướng dẫn \'{{name}}\' thành công',
  'COPY_INSTRUCTION_ERROR': 'Sao chép hướng dẫn thất bại',
  'NO_INSTRUCTIONS_FOUND': 'Không tìm thấy hướng dẫn nào',

  // Voice Options
  'VOICE_OPTIONS_MANAGEMENT': 'Quản lý tùy chọn giọng nói',
  'VOICE_OPTIONS_DESCRIPTION': 'Quản lý các tùy chọn giọng nói cho ứng dụng',
  'EDIT_VOICE_OPTION': 'Chỉnh sửa tùy chọn giọng nói',
  'DELETE_VOICE_OPTION': 'Xóa tùy chọn giọng nói',
  'CREATE_VOICE_OPTION': 'Tạo tùy chọn giọng nói',
  'VOICE_OPTION_DESCRIPTION': 'Tạo hoặc chỉnh sửa tùy chọn giọng nói cho ứng dụng',
  'VOICE_FILE': 'Tệp âm thanh',
  'IS_PUBLIC': 'Công khai',
  'NAME_REQUIRED': 'Tên không được để trống!',
  'CODE_REQUIRED': 'Mã không được để trống!',
  'IS_PUBLIC_REQUIRED': 'Trạng thái công khai không được để trống!',
  'ENTER_CODE': 'Nhập mã',
  'SELECT_IS_PUBLIC': 'Chọn trạng thái công khai',
  'DRAG_DROP_AUDIO': 'Kéo và thả tệp âm thanh vào đây',
  'OR': 'hoặc',
  'BROWSE_FILES': 'Chọn tệp',
  'SUPPORTED_FORMATS': 'Định dạng hỗ trợ',
  'DELETE_FILE': 'Xóa tệp',
  'LISTEN_PREVIEW': 'Nghe thử',
  'NO_AUDIO': 'Không có âm thanh',
  'AUDIO_PREVIEW': 'Nghe thử âm thanh',
  'PLAY_AUDIO': 'Phát',
  'PAUSE_AUDIO': 'Tạm dừng',
  'PLAYING': 'Đang phát...',
  'TOTAL_ITEMS': 'Tổng: {{total}} mục',
  'DELETE_VOICE_OPTION_CONFIRM': 'Bạn có chắc chắn muốn xóa tùy chọn giọng nói này không?',
  'DELETE_VOICE_OPTION_CONFIRM_WITH_NAME': 'Bạn có chắc chắn muốn xóa tùy chọn giọng nói \'{{name}}\'? Hành động này không thể hoàn tác.',
  'DELETE_VOICE_OPTION_SUCCESS': 'Xóa tùy chọn giọng nói thành công',
  'DELETE_VOICE_OPTION_ERROR': 'Xóa tùy chọn giọng nói thất bại',
  'UPDATE_VOICE_OPTION_SUCCESS': 'Cập nhật tùy chọn giọng nói thành công',
  'UPDATE_VOICE_OPTION_ERROR': 'Cập nhật tùy chọn giọng nói thất bại',
  'CREATE_VOICE_OPTION_SUCCESS': 'Tạo tùy chọn giọng nói thành công',
  'CREATE_VOICE_OPTION_ERROR': 'Tạo tùy chọn giọng nói thất bại',
  'UPLOADING_FILE': 'Đang tải lên tệp...',
  'UPLOAD_FILE_SUCCESS': 'Tải lên tệp thành công',
  'UPLOAD_FILE_ERROR': 'Tải lên tệp thất bại',
  'SAVE_BEFORE_UPLOAD': 'Vui lòng lưu tùy chọn giọng nói trước khi tải lên tệp',
  'SEARCH_VOICE_OPTION_PLACEHOLDER': 'Tìm kiếm theo tên hoặc mã',
  'NO_VOICE_OPTIONS_FOUND': 'Không tìm thấy tùy chọn giọng nói nào'
};
