import { put, select, takeLatest } from 'redux-saga/effects';
import Cookies from 'js-cookie';

import { CONSTANT, LANGUAGE, THEME_TYPE } from '@constant';

import { setCookieTheme } from '@common/functionCommons';

export const actionTypes = {
  ToggleLoading: 'App/ToggleLoading',
  ToggleTheme: 'App/ToggleTheme',
  GetAppTheme: 'App/GetAppTheme',
  SetBreadcrumb: 'App/SetBreadcrumb',
  SetProjectBreadcrumb: 'App/SetProjectBreadcrumb',
  SetFolderBreadcrumb: 'App/SetFolderBreadcrumb',

  SetLanguage: 'App/SetLanguage',

  ToggleDrawerMenu: 'App/ToggleDrawerMenu',
  SetShowDrawerMenu: 'App/SetShowDrawerMenu',
};

const initialAuthState = {
  isLoading: false,
  menuList: undefined,
  theme: THEME_TYPE.LIGHT,
  breadcrumbData: {},
  language: LANGUAGE.VI,

  isShowDrawerMenu: false,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetBreadcrumb: {
      const { breadcrumbData } = action.payload;
      return Object.assign({}, state, { breadcrumbData });
    }
    case actionTypes.SetProjectBreadcrumb: {
      const { project } = action.payload;
      return Object.assign({}, state, { breadcrumbData: { ...state.breadcrumbData, project } });
    }
    case actionTypes.SetFolderBreadcrumb: {
      const { folder } = action.payload;
      return Object.assign({}, state, { breadcrumbData: { ...state.breadcrumbData, folder } });
    }
    case actionTypes.ToggleLoading: {
      const { isLoading } = action.payload;
      return Object.assign({}, state, { isLoading });
    }
    case actionTypes.ToggleTheme: {
      const theme = state.theme === THEME_TYPE.LIGHT ? THEME_TYPE.DARK : THEME_TYPE.LIGHT;
      setCookieTheme(theme);
      return Object.assign({}, state, { theme });
    }
    case actionTypes.GetAppTheme: {
      const theme = Cookies.get('theme') || THEME_TYPE.LIGHT;
      return Object.assign({}, state, { theme });
    }
    case actionTypes.SetLanguage: {
      const { language } = action.payload;
      return Object.assign({}, state, { language });
    }
    case actionTypes.SetShowDrawerMenu: {
      const { isShowDrawerMenu } = action.payload;
      return Object.assign({}, state, { isShowDrawerMenu });
    }
    default:
      return state;
  }
};

export const actions = {
  toggleLoading: (isLoading) => ({ type: actionTypes.ToggleLoading, payload: { isLoading } }),
  getAppTheme: () => ({ type: actionTypes.GetAppTheme }),
  toggleTheme: () => ({ type: actionTypes.ToggleTheme }),
  setBreadcrumb: (breadcrumbData) => ({ type: actionTypes.SetBreadcrumb, payload: { breadcrumbData } }),
  setProjectBreadcrumb: (project) => ({ type: actionTypes.SetProjectBreadcrumb, payload: { project } }),
  setFolderBreadcrumb: (folder) => ({ type: actionTypes.SetFolderBreadcrumb, payload: { folder } }),

  setLanguage: (language) => ({ type: actionTypes.SetLanguage, payload: { language } }),
  setShowDrawerMenu: (isShowDrawerMenu) => ({ type: actionTypes.SetShowDrawerMenu, payload: { isShowDrawerMenu } }),
  toggleDrawerMenu: () => ({ type: actionTypes.ToggleDrawerMenu }),
};

export function * saga() {

  yield takeLatest(actionTypes.ToggleDrawerMenu, function * getToolSaga() {
    const isShowDrawerMenu = yield select(store => store.app.isShowDrawerMenu);
    yield put(actions.setShowDrawerMenu(!isShowDrawerMenu));
  });
}

