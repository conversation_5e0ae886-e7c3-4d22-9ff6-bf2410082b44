import { put, takeLatest } from 'redux-saga/effects';

import { CONSTANT } from '@src/constants/constant';
import { getNewToken, getUserByToken, login, logout } from '@services/Auth';
import { formatUnique } from '@src/common/functionCommons';

import { actions as appActions } from './app.duck';

export const actionTypes = {
  ToggleLoading: 'Auth/ToggleLoading',
  Login: 'Auth/Login',
  Logout: 'Auth/Logout',

  RequestUser: 'Auth/RequestUser',
  UserLoaded: 'Auth/UserLoaded',
  SetUser: 'Auth/SetUser',

  RemoveUserState: 'Auth/RemoveUserState',

  GetNewToken: 'Auth/GetNewToken',
};

const initialAuthState = {
  user: CONSTANT.INITIAL,
  permissionGranted: {},
};

function handlePermission(userData) {
  const userAction = userData?.roleId
    ?.filter(roleId => Array.isArray(roleId.permissionIds))
    .map(roleId => roleId.permissionIds).flat()
    .filter(permissionIds => Array.isArray(permissionIds.actions))
    .map(permissionIds => permissionIds.actions).flat()
    .sort((a, b) => a.localeCompare(b));

  const actionGranted = formatUnique(userAction);

  return actionGranted.reduce(function(grouped, element) {
    const [serviceName, actionName] = element.split('.');
    if (serviceName && actionName) {
      grouped[serviceName] ||= [];
      grouped[serviceName].push(actionName);
    }
    return grouped;
  }, {});
}

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetUser: {
      const { user } = action.payload;
      if (!user?._id) return { ...state, user: null };

      const permissionGranted = handlePermission(user);
      localStorage.removeItem(window.location.host + 'logout');
      return { ...state, user, permissionGranted };
    }

    case actionTypes.RemoveUserState: {
      delete state.user;
      return state;
    }

    default:
      return state;
  }
};

export const actions = {
  login: (data, history) => ({ type: actionTypes.Login, payload: { data, history } }),
  logout: () => ({ type: actionTypes.Logout }),

  requestUser: () => ({ type: actionTypes.RequestUser, payload: {} }),
  userLoaded: user => ({ type: actionTypes.UserLoaded, payload: { user } }),
  setUser: user => ({ type: actionTypes.SetUser, payload: { user } }),

  removeUserState: user => ({ type: actionTypes.RemoveUserState }),
};

export function * saga() {
  yield takeLatest(actionTypes.Login, function * loginSaga(data) {
    const dataResponse = yield login(data?.payload?.data);
    yield put(actions.userLoaded(dataResponse));
  });
  yield takeLatest(actionTypes.Logout, function * loginSaga(data) {
    const dataResponse = yield logout();
    if (dataResponse?.success) {
      localStorage.setItem(window.location.host + 'logout', CONSTANT.TRUE);
      yield put(actions.removeUserState());
    }
  });

  yield takeLatest(actionTypes.RequestUser, function * requestUserSaga() {
    const dataResponse = yield getUserByToken();
    yield put(actions.userLoaded(dataResponse));
  });

  yield takeLatest(actionTypes.GetNewToken, function * getNewTokenSaga() {
    const dataResponse = yield getNewToken();
    if (dataResponse) {
      yield put(actions.requestUser());
    }
  });

  yield takeLatest(actionTypes.UserLoaded, function * userLoadedSaga(data) {
    const userData = data?.payload?.user;
    yield put(actions.setUser(userData));
  });

}
