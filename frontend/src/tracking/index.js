import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { actions } from '../ducks/tracking.duck';

import { CONSTANT } from '@constant';

import { createTracking } from '@src/app/services/Tracking';
import { useDispatch } from 'react-redux';

const useTracking = (user) => {
  const location = useLocation();
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  useEffect(() => {
    const isAuthorized = !!user && (user !== CONSTANT.INITIAL);
    if (isAuthorized) {
      saveAccessHistory();
    }
  }, [location.pathname, user]);

  useEffect(() => {
    // dispatch(actions.trackCustomView());
  }, [location, dispatch]);

  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    if (userAgent.includes('Chrome')) {
      browserName = 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox';
    } else if (userAgent.includes('Safari')) {
      browserName = 'Safari';
    }
    return browserName;
  };

  const saveAccessHistory = async () => {
    const dataRequest = {
      lang: i18n.language,
      browser: getBrowserInfo(),
    };
    await createTracking(dataRequest);
  };
  return;
};

export default useTracking;