version: "3.7"

services:
  web:
    image: "registry.thinklabs.com.vn:5000/engsuitenewweb:latest"
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.environment==development ]
      restart_policy:
        condition: any
    environment:
      PORT: 80
      NODE_ENV: "production"
      SERVICE_80_NAME: "engsuitenewweb"
      SERVICE_NAME: "engsuitenewweb"
      SERVICE_TAGS: "engsuitenewweb"
      GA_ID: "G-WH2LW8XKZG"
    ports:
      - target: 80
        published: 8000
        mode: host
