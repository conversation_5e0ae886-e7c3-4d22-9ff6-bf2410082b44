const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = (app, options) => {
  const isProd = process.env.NODE_ENV === 'production';

  if (isProd) {
    app.use(corsMiddlewares);
    const addProdMiddlewares = require('./prod.middlewares');
    addProdMiddlewares(app, options);
  } else {
    app.use('/api', createProxyMiddleware({
      target: 'http://127.0.0.1:3000',
      secure: false,
    }));
    app.use('/upload', createProxyMiddleware({
      target: 'http://127.0.0.1:3000',
      changeOrigin: true,
      secure: false,
    }));
    app.use('/socket', createProxyMiddleware({
      target: 'http://127.0.0.1:3001',
      Ssecure: false,
      ws: true,
    }));
    const webpackConfig = require('../../webpack/webpack.dev.babel');
    const addDevMiddlewares = require('./dev.middlewares');
    addDevMiddlewares(app, webpackConfig);
  }
  return app;
};

function corsMiddlewares(req, res, next) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');
  res.setHeader('Access-Control-Allow-Credentials', true);
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  return next();
}
